import { h } from 'vue';
import { isNil } from 'lodash';
import { commonApi } from '@/api/common';
import { hydrateDeep } from '@/utils';
import { postbackCountApi } from '@/api/postback/count';
import { getCompanyGroupSource } from '@/utils/global-app';
import { getCountDefaultFormValue } from '@/views/postback/count/config/default-form';
import { AppGroupEnum } from '@/views/postback/new-link/constants';
import {
  countEventTypeEnum,
  countTypeMap,
  countDimOptions,
  countMethodOptions,
  countEventTypeOptions,
} from '@/views/postback/count/constants';
import CountEventConfig from '@/views/postback/count/components/count-event-config/index.vue';

export const getCountDefaultForm = () => {
  return {
    form: {
      formSchema: getFormSchema(),
    },
    async getDefaultValue(record) {
      const defaultData = getCountDefaultFormValue();
      const res = await postbackCountApi.detail({
        id: record.countStrategyId,
      });
      const data: any = formatApiData({
        defaultData,
        apiData: res.data ?? {},
      });

      data.id = record.countStrategyId;
      data.linkCount = res?.data?.linkCount ?? 0;
      data.disableCountFields = data?.eventConfigVo?.arpuFirst === true;
      return data;
    },
  };
};

function getFormSchema() {
  return {
    fields: [
      {
        type: 'viewCard',
        label: '基础配置',
        contentStyle: {
          width: '80%',
        },
        fields: [
          {
            label: '应用类型',
            name: 'appGroup',
            type: 'select',
            format: 'singleSelect',

            disabled: true,
            select: {
              allowSearch: true,
              allowClear: true,
            },
            source: {
              valueKey: 'value',
              labelKey: 'name',
              data: async () => {
                return commonApi.getAppClassifyList?.().then((res) => {
                  return res.data || [];
                });
              },
            },
          },
          {
            label: '所属组织',
            type: 'cascader',

            disabled: true,
            placeholder: '请选择组',
            name: 'departmentCode',
            multiple: false,
            source: {
              data() {
                return getCompanyGroupSource();
              },
            },
          },
          {
            label: '策略名称',
            name: 'strategyName',
            type: 'text',
            disabled: true,
            maxLength: 50,
            showWordLimit: true,
          },
          {
            label: '策略备注',
            name: 'note',
            type: 'textarea',
            disabled: true,
            maxLength: 100,
            showWordLimit: true,
          },
        ],
      },
      {
        type: 'viewCard',
        label: '策略配置',
        contentStyle: {
          width: '80%',
        },
        fields: [
          {
            label: '计数维度',
            name: 'countDim',
            type: 'select',
            format: 'buttonRadio',
            source: {
              data: countDimOptions,
            },
            disabled: true,
          },
          {
            label: '计数类型',
            name: 'countType',
            type: 'select',
            format: 'buttonRadio',

            disabled: true,
            source: {
              data: (data, form) => {
                if (form.appGroup === AppGroupEnum.Applet) {
                  return [
                    countTypeMap.RECHARGE,
                    countTypeMap.DEDUCT,
                    countTypeMap.NUMBER,
                  ];
                }
                return [countTypeMap.VALUE, countTypeMap.NUMBER];
              },
            },
          },
          {
            label: '计数方式',
            name: 'countMethod',
            type: 'select',
            format: 'buttonRadio',
            disabled: true,
            source: {
              data: (_, form) => {
                return countMethodOptions;
              },
            },
          },
          {
            label: '计数事件',
            name: 'countEvent',
            type: 'select',
            format: 'buttonRadio',
            source: {
              data: countEventTypeOptions,
            },

            disabled: true,
          },
          {
            name: 'eventConfigVo',
            label: '',
            type: 'custom',
            component: h(CountEventConfig, {
              mode: 'modify',
              disabledCallback: () => true,
            }),
          },
        ],
      },
    ],
  };
}

function formatApiData({ defaultData, apiData }) {
  if (!apiData) {
    return defaultData;
  }
  const data: any = hydrateDeep(defaultData, apiData);
  const { eventConfigVo } = apiData;
  data.eventConfigVo = {
    currentEvent: {
      eventName: eventConfigVo.eventName,
      eventId: eventConfigVo.eventId,
    },
    eventPkgData:
      apiData.countEvent === countEventTypeEnum.eventPacks &&
      !isNil(eventConfigVo.eventPkgId)
        ? {
            eventPkgName: eventConfigVo.eventPkgName,
            eventPkgId: eventConfigVo.eventPkgId,
          }
        : undefined,
    relationType: eventConfigVo.relationType,
    eventPropertyList: Array.isArray(eventConfigVo.eventPropertyList)
      ? eventConfigVo.eventPropertyList.map((item) => ({
          ...item,
          currentProperty: {
            propertyName: item.propertyName,
            propertyCode: item.propertyCode,
          },
        }))
      : [],
    ecpmFirstSwitch: apiData.ecpmFirstSwitch === 1,
    rewardVideoSwitch: apiData.rewardVideoSwitch === 1,
    arpuFirst: apiData.arpuFirst === 1,
    appId: apiData.appId ?? undefined,
  };
  return data;
}
