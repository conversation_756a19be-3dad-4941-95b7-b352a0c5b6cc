import { isNil, omit } from 'lodash';
import {
  beforeDayMap,
  columnCodeMap,
  ruleTargetEnum,
} from '@/views/tool/auto-rule/rule-manage/constants';
import { filterIfUse } from '../index';

export function getFictionReportColumns({
  canFilter = true,
  currentPathMap,
  checkOnlyMedia,
  beforeDay,
  ruleTarget,
  checkMedias,
}: {
  // 是否进行条件判断
  canFilter?: boolean;
  // 当天 or 昨天
  beforeDay?: number;
  currentPathMap?: Record<string, boolean>;
  checkOnlyMedia?: Record<string, boolean>;
  // 账户规则
  ruleTarget?: ruleTargetEnum.account | ruleTargetEnum.ad;
  ruleTypeFlag?: any;
  checkMedias?: any[];
}) {
  const isAccount = ruleTarget === ruleTargetEnum.account;
  const isAd = ruleTarget === ruleTargetEnum.ad;
  // 如果选择了三天或者七天
  const beforeDayFlag = isNil(beforeDay)
    ? false
    : [beforeDayMap.threeDays, beforeDayMap.sevenDays].includes(beforeDay);
  const isOnlyToutiao =
    (checkOnlyMedia?.toutiao || currentPathMap?.toutiao) ?? false;
  const isOnlyKuaishou =
    (checkOnlyMedia?.kuaishou || currentPathMap?.kuaishou) ?? false;

  const ksMedia = isOnlyKuaishou ? checkMedias?.[0] : null;
  const resultColumns = [
    // 公共指标
    { label: '消耗', value: 'media_cost' },

    { label: '展示', value: 'media_ad_show_num' },
    { label: '点击', value: 'media_ad_click_num' },
    {
      label: '创建时间',
      value: columnCodeMap.campaignCreateTime,
    },
    ...[
      {
        label: '账户-消耗',
        value: 'account-media_cost',
      },
      {
        label: '账户-IAA广告变现ROI',
        value: 'account-iaa_purchase_roi',
      },
      {
        label: '账户-当日广告变现ROI',
        value: 'account-iaa_purchase_amount_first_day_roi',
      },
    ].map((item) => {
      return {
        ...item,
        ifUse: () => {
          // 广告 && 只选了快手 &&  是 全部广告指定账户
          return (
            isAd && isOnlyKuaishou && [2, 1].includes(ksMedia?.isAllTarget)
          );
        },
      };
    }),
    {
      label: 'IAA广告变现ROI',
      value: 'iaa_purchase_roi',
      ifUse: () => {
        return isOnlyKuaishou;
      },
    },

    {
      label: '转化',
      value: 'media_covert_num',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: 'cpc',
      value: 'media_cpc',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: 'ecpm',
      value: 'media_ecpm',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '转化率',
      value: 'media_convert_ratio',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '点击率',
      value: 'media_ad_click_ratio',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '转化成本',
      value: 'media_convert_cost',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },

    {
      label: '激活当日ROI（计算）',
      value: 'template_roi',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },

    {
      label: '激活当日付费金额（计算）',
      value: 'template_value',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },

    // 头条账户  快手账户
    {
      label: '账户余额',
      value: 'media_balance',
      ifUse: () => {
        return isAccount && (isOnlyToutiao || isOnlyKuaishou) && !beforeDayFlag;
      },
    },

    // ks common
    ...[
      { label: '新增付费人数', value: 'event_new_user_pay' },
      { label: '新增付费人数成本', value: 'event_new_user_pay_cost' },
      { label: '新增付费人数率', value: 'event_new_user_pay_ratio' },
      { label: '首日付费次数', value: 'event_pay_first_day' },
      { label: '付费次数', value: 'event_pay' },
      { label: '付费次数成本', value: 'event_order_paid_cost' },
      { label: '付费金额', value: 'event_pay_purchase_amount' },
      { label: '付费arpu', value: 'event_pay_arpu' },
      { label: '唤起应用数', value: 'event_app_invoked' },

      { label: '当日广告变现ROI', value: 'iaa_purchase_amount_first_day_roi' },
      { label: 'IAA广告变现LTV', value: 'iaa_purchase_amount' },
      { label: '当日广告LTV', value: 'iaa_purchase_amount_first_day' },
      { label: '激活当日ROI', value: 'first_day_active_roi' },
      {
        label: '激活当日付费金额',
        value: 'event_pay_purchase_amount_first_day',
      },
    ].map((item) => {
      return {
        ...item,
        ifUse: () => {
          return isOnlyKuaishou && !beforeDayFlag;
        },
      };
    }),
  ];

  if (canFilter) {
    return filterIfUse({ ifList: resultColumns });
  }
  return resultColumns.map((item) => {
    return omit(item, 'ifUse');
  });
}
