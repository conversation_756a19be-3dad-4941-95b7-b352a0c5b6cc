<template>
  <DTableSelector
    class="table-selector"
    ref="tableSelectorRef"
    v-model="selectData"
    :filter="operationFilter"
    :load="operationTable(record).load"
    :columns="operationTable(record).columns"
    :custom-pagination="pageConfig"
    value-key="id"
    label-key="material_name"
    :is-multiple="true"
    hide-target
    show-filter-operation
    @query="resetSelectData"
    @reset="resetSelectData"
  >
    <template #operation>
      <div class="operation">
        <DynamicButton :config="passConfig" />
        <DynamicButton :config="unPassConfig" :style="{ marginLeft: '12px' }" />
      </div>
    </template>
  </DTableSelector>
  <PreviewMaterialModal
    v-model="previewMaterialVisible"
    v-model:options="previewMaterialOptions"
    @refresh="loadInfo"
    :material-num="1"
    @delete-success="loadInfo"
    @change-select="handleChangeSelect"
    :hide-edit="true"
  />
</template>

<script setup lang="tsx">
  import { ref, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import usePreviewMaterial from '@/views/sc-home/hook/preview-material';
  import { autoMaterial } from '@/api/automation/auto-material';
  import filterEmpty from '@/utils/filter-empty';
  import MaterialContent from '@/views/audit/common/components/material-content.vue';
  import PreviewMaterialModal from './preview-material-modal.vue';
  import { operationFilter } from './config';
  import { TaskState, MaterialState } from '../../constant';

  const selectData = ref<any[]>([]);
  const props = defineProps<{
    record?: any;
  }>();

  const { previewMaterialOptions, previewMaterialVisible, openMaterial } =
    usePreviewMaterial();

  function handlePreviewMaterial(record) {
    const list = tableData.value.filter((item) => {
      return MaterialState.includes(item.status);
    });
    const openIndex = list.findIndex((item) => item.id === record.id);

    openMaterial({
      record,
      list,
      index: openIndex,
    });
  }

  const tableSelectorRef = ref();
  const pageConfig = {
    current: 1,
    pageSize: 10,
    total: 20,
    showPageSize: true,
    showJumper: true,
    size: 'mini',
    showTotal: true,
    pageSizeOptions: [10, 20, 30, 40, 50, 100, 200],
  };
  async function loadInfo() {
    tableSelectorRef.value?.refreshTable();
  }
  async function handleChangeSelect(id) {
    await nextTick();
    spliceSelect(id);
  }
  const tableData = ref<any>([]);
  function getRowData({ rows, dataList }) {
    const rowIds = rows.map((row) => row.id);
    const result = dataList.filter((data) => rowIds.includes(data.id));
    return result;
  }
  // 操作记录列表
  const operationTable = (record) => {
    return {
      load: {
        action: async (filterParams, pagination) => {
          const query = {
            ...filterParams,
            ...pagination,
            id: record.id,
          };
          const { searchTime } = query;
          if (Array.isArray(searchTime) && searchTime.length >= 2) {
            query.startTime = searchTime[0];
            query.endTime = searchTime[1];
            delete query.searchTime;
          }

          const {
            data: { list, pageInfo },
          } = await autoMaterial.detail(filterEmpty(query));

          tableData.value = (list.materialList || []).map((item) => {
            return {
              ...item,
              materialName: item.material_name,
              materialType: 2,
              name: item.name,
              url: item.syn_url,
            };
          });

          return {
            list: tableData.value || [],
            current: pageInfo.page || 1,
            size: pageInfo.pageSize || 10,
            total: pageInfo.total || 0,
            pages: pageInfo.totalPage || 1,
          };
        },
      },

      columns: [
        {
          dataIndex: 'id',
          title: '序号',
          width: 70,
          render: ({ record: cutRecord }) => {
            return `atsc${cutRecord.id}`;
          },
        },
        { dataIndex: 'creative_name', title: '创意名称' },
        {
          dataIndex: 'material_name',
          title: '素材名称',
          width: 110,
          render: ({ record: cutRecord }) => {
            const item = {
              ...cutRecord,
              isLeaf: true,
              cover: cutRecord.syn_cover,
              url: cutRecord.syn_url,
              materialType: cutRecord.material_type,
            };
            return (
              <span>
                {MaterialState.includes(cutRecord.status) && (
                  <MaterialContent
                    record={item}
                    onClick={() => handlePreviewMaterial(cutRecord)}
                  />
                )}
                <a-link
                  style={{ display: 'inline' }}
                  type="text"
                  disabled={!MaterialState.includes(cutRecord.status)}
                  onClick={() => handlePreviewMaterial(cutRecord)}
                >
                  {cutRecord.material_name || '-'}
                </a-link>
              </span>
            );
          },
        },
        {
          dataIndex: 'status',
          title: '任务状态',
          width: 60,
          render: ({ record: cutRecord }) => {
            if (cutRecord.status === 8) {
              return (
                <a-tooltip content={cutRecord.errMsg}>
                  <span>推送失败</span>
                </a-tooltip>
              );
            }
            if (cutRecord.status === 4) {
              return (
                <a-tooltip content={cutRecord.errMsg}>
                  <span>合成失败</span>
                </a-tooltip>
              );
            }
            return TaskState.find((item) => item.value === cutRecord.status)
              ?.label;
          },
        },
        {
          dataIndex: 'artificialStatus',
          title: '审核状态',
          width: 60,
          customRender: {
            type: 'code-to-name',
            props: {
              map: [
                {
                  value: 2,
                  label: '合格',
                },
                {
                  value: 3,
                  label: '不合格',
                },
                {
                  value: 1,
                  label: '待确认',
                },
              ],
            },
          },
        },
        { dataIndex: 'createdAt', title: '创建时间', width: 120 },
        {
          title: '操作',
          width: 150,
          dataIndex: 'operations',
          fixed: 'right',
          customRender: {
            type: 'operations',
            props: {
              showOperationCount: 3,
              operations: [
                {
                  text: '终止',
                  props: {
                    type: 'text',
                    disabled: ({ record: cutRecord }) => {
                      // 只有排队中的任务才能终止
                      return cutRecord.status !== TaskState[0].value;
                    },
                  },
                  clickActionType: 'action',
                  action: async ({ refreshTable, record: cutRecord }) => {
                    await autoMaterial.stop({ id: cutRecord.id });
                    // 刷新列表
                    spliceSelect(cutRecord.id);
                    refreshTable();
                  },
                },
                {
                  text: '通过',
                  auth: 'automation_update_status',
                  props: {
                    type: 'text',
                    disabled: ({ record: cutRecord }) => {
                      // 只有排队中的任务才能终止
                      return !(
                        cutRecord.status === TaskState[2].value &&
                        (cutRecord.artificialStatus === 1 ||
                          cutRecord.artificialStatus === 3)
                      );
                    },
                  },
                  clickActionType: 'action',
                  action: async ({ refreshTable, record: cutRecord }) => {
                    await handleStatus([cutRecord.id], 2);
                    // 刷新列表
                    refreshTable();
                    await nextTick();
                    spliceSelect(cutRecord.id);
                  },
                },
                {
                  text: '不通过',
                  auth: 'automation_update_status',
                  props: {
                    type: 'text',
                    status: 'danger',
                    disabled: ({ record: cutRecord }) => {
                      // 只有排队中的任务才能终止
                      return !(
                        cutRecord.status === TaskState[2].value &&
                        cutRecord.artificialStatus === 1
                      );
                    },
                  },
                  clickActionType: 'action',
                  action: async ({ refreshTable, record: cutRecord }) => {
                    await handleStatus([cutRecord.id], 3);
                    refreshTable();
                    await nextTick();
                    spliceSelect(cutRecord.id);
                  },
                },
              ],
            },
          },
        },
      ],
    };
  };
  const passLoading = ref(false);
  async function handleStatus(ids, status) {
    passLoading.value = true;
    try {
      const res = await autoMaterial.status({
        ids,
        artStatus: status,
        tacticsId: props.record?.id,
      });
      if (res.code !== 1) {
        Message.error('操作失败');
        return;
      }
      Message.success('操作成功');
    } finally {
      passLoading.value = false;
    }
  }
  const passConfig = {
    text: '批量通过',
    auth: 'automation_batch_ update_status',
    props: {
      type: 'primary',
      disabled: () => {
        const selectRows = getRowData({
          rows: selectData.value,
          dataList: tableData.value,
        });
        const checkArr = selectRows.filter(
          (item) =>
            item.status === 3 &&
            (item.artificialStatus === 1 || item.artificialStatus === 3)
        );
        return (
          !selectData.value.length ||
          checkArr.length !== selectData.value.length
        );
      },
    },
    clickActionType: 'action',
    action: async () => {
      await handleStatus(
        selectData.value.map((item) => item.id),
        2
      );
      selectData.value = [];
      loadInfo();
    },
  };

  const unPassConfig = {
    text: '批量不通过',
    auth: 'automation_batch_ update_status',
    props: {
      type: 'primary',
      disabled: () => {
        const selectRows = getRowData({
          rows: selectData.value,
          dataList: tableData.value,
        });
        const checkArr = selectRows.filter(
          (item) =>
            item.status === 3 &&
            item.artificialStatus === 1 &&
            selectData.value.length
        );
        return (
          !selectData.value.length ||
          checkArr.length !== selectData.value.length
        );
      },
      status: 'danger',
    },
    clickActionType: 'action',
    action: async () => {
      await handleStatus(
        selectData.value.map((item) => item.id),
        3
      );
      selectData.value = [];
      loadInfo();
    },
  };
  const spliceSelect = (key) => {
    const newArr = selectData.value.filter((item) => item.id !== key);
    selectData.value = newArr;
  };
  const resetSelectData = async () => {
    await nextTick();
    selectData.value = [];
  };
</script>

<style scoped lang="less">
  .operation {
    width: 100%;
    padding-bottom: 10px;
    display: flex;
    justify-content: flex-end;
  }

  .table-selector {
    :deep(.table) {
      display: flex;
      flex-direction: column;
    }
  }
</style>
