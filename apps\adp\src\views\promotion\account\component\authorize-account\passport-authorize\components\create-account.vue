<template>
  <div>
    <div style="margin-bottom: 10px">
      <a-alert
        >注意：所选账户将自动绑定当前页面的产品（当前产品：
        {{ getCurAppName }}）</a-alert
      ></div
    >
    <a-collapse
      class="brand-collapse"
      :default-active-key="[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]"
    >
      <a-collapse-item
        v-for="(fItem, fIndex) in modelValue?.list"
        :key="fIndex"
      >
        <template #header>
          <div style="display: flex">
            <div style="margin-right: 8px"> 账户{{ fIndex + 1 }} </div>
          </div>
        </template>
        <template #extra>
          <a-button
            type="text"
            @click="removeAccount(fIndex)"
            v-if="modelValue?.list?.length > 1"
          >
            <template #icon>
              <icon-delete />
            </template>
          </a-button>
        </template>
        <DynamicForm
          ref="formRef"
          :form-schema="formSchema"
          v-model="modelValue.list[fIndex]"
        ></DynamicForm>
      </a-collapse-item>
    </a-collapse>
    <div>
      <a-button
        type="dashed"
        style="width: 100%; margin-top: 20px"
        @click="addAccount"
        >添加账户</a-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, defineProps } from 'vue';
  import accountCommonApi from '@/api/promotion/common';
  import { Message } from '@arco-design/web-vue';
  import { omit } from 'lodash';
  import useAppId from '@/hooks/app-id';
  import { getOppoFormData } from '@/views/promotion/account/oppo/constants';
  import {
    MediaType,
    mediaPlatformMap,
  } from '@/views/promotion/hooks/authorization';
  import { getFormData as getXiaomiFormData } from '@/views/promotion/account/xiaomi/constants';
  import { getFormData as getUcFormData } from '@/views/promotion/account/uc/constants';
  import { getFormData as getWifiFormData } from '@/views/promotion/account/wifi/constants';
  import { getHonorFormData } from '@/views/promotion/account/honor/constants';
  import { getFormData as getSigmobFormData } from '@/views/promotion/account/sigmob/constants';

  const appId = useAppId();

  // eslint-disable-next-line vue/valid-define-props
  const props = defineProps<{
    formSchema?: any;
    mediaType?: any;
  }>();

  const getCurAppName = computed(() => {
    return window.$appInfo?.appName;
  });

  const modelValue = defineModel<Record<string, any>>();

  const formRef = ref();

  const defaultDataMap = {
    OPPO: getOppoFormData,
    XIAOMI: getXiaomiFormData,
    UC: getUcFormData,
    WIFI: getWifiFormData,
    HONOR: getHonorFormData,
    SIGMOB: getSigmobFormData,
  };

  async function validatorFunc() {
    const result = formRef.value.map((item) => {
      return item.validate();
    });
    const validateRes = await Promise.all(result);
    return validateRes;
  }

  async function verify() {
    await validatorFunc();

    const submitData = modelValue.value?.list.map((item) => {
      return omit(item, ['ownerId']);
    });

    const result = {
      status: true,
      list: [],
    };
    try {
      const res = await accountCommonApi.accountVerify(submitData);
      if (res.data.errorList.length !== 0) {
        result.status = false;
        const errorAccount: any = [];
        const errorMsg: any = '授权失败，请检查参数是否填写正确！';
        const bindAccount: any = [];
        res.data.errorList.forEach((item) => {
          modelValue.value?.list.forEach((fItem, fIndex) => {
            if (fItem.sessionKey === item.sessionKey) {
              if (item.errorMsg === errorMsg) {
                errorAccount.push(fIndex + 1);
              } else {
                bindAccount.push(fIndex + 1);
              }
            }
          });
        });
        Message.warning({
          content: `${
            errorAccount.length > 0
              ? `账户${errorAccount.join('，')}授权失败，检查参数是否填写正确;`
              : ''
          }   
        ${
          bindAccount.length > 0
            ? `账户${bindAccount.join(
                '，'
              )}已被其他人绑定，请检查参数填写是否正确`
            : ''
        }`,
          duration: 6000,
        });
      }
      result.list = res.data.accountList;
      return result;
    } catch (e) {
      result.status = false;
      return result;
    }
  }

  function addAccount() {
    if (modelValue.value?.list.length === 10) {
      Message.warning('最多添加10个账户');
      return;
    }
    modelValue.value?.list.push({
      ...defaultDataMap[mediaPlatformMap[props.mediaType]](),
    });
  }

  function removeAccount(index) {
    modelValue.value?.list.splice(index, 1);
  }

  defineExpose({
    verify,
  });
</script>

<style scoped lang="less">
  .filter-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
    gap: 16px;
  }
</style>
