<template>
  <div class="create-time-scope">
    <a-button type="text" @click="handleBatchCreate">批量生成</a-button>
  </div>
</template>

<script setup lang="ts">
  import { inject } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import dayjs from 'dayjs';

  const formData = inject('formData') ?? {};
  const props = defineProps<{
    mode: 'add' | 'edit';
  }>();
  function generateDateArray(startDate, endDate) {
    const dates = [];
    let currentDate: any = dayjs(startDate);
    const end = dayjs(endDate);

    while (currentDate.isBefore(end) || currentDate.isSame(end)) {
      dates.push(currentDate.format('YYYY-MM-DD'));
      currentDate = currentDate.add(1, 'day');
    }

    return dates;
  }
  const handleBatchCreate = () => {
    const batchConfig = formData.value?.batchConfig ?? {};
    const { date, caliberType } = batchConfig;
    if (caliberType === '') {
      Message.warning('请选择批量质量分口径');
      return;
    }
    if (!(Array.isArray(date) && date.length === 2)) {
      Message.warning('请选择时间范围');
      return;
    }
    const dateArray = generateDateArray(date[0], date[1]);
    if (props.mode === 'edit') {
      const currentTime = dayjs().format('YYYY-MM-DD');
      const oldScoreList = (
        formData.value?.qualityScoreCaliberList ?? []
      ).filter((item) => {
        // 判断 之前的date 是否小于当前时间，小于则保留
        return dayjs(item.date).isBefore(currentTime);
      });
      formData.value.qualityScoreCaliberList = [
        ...oldScoreList,
        ...dateArray.map((item) => ({
          date: item,
          caliberType,
        })),
      ];
    } else {
      formData.value.qualityScoreCaliberList = dateArray.map((item) => ({
        date: item,
        caliberType,
      }));
    }

    Message.success('生成成功');
  };
</script>

<style scoped lang="less">
  .create-time-scope {
  }
</style>
