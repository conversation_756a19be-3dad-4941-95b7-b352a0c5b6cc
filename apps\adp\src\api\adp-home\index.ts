import { http } from '@/utils/http';

export const adpHomeApi = {
  // 广告数据概况
  getAdData(data) {
    return http.post('/transmit/bi/homepage/v1/dataOverview', data);
  },
  // 产品分析
  productAnalyses(data) {
    return http.post('/transmit/bi/homepage/v1/productAnalysis', data);
  },
  exportAnalyses(data) {
    return http.post('/transmit/bi/homepage/v1/productAnalysis/export', data, {
      responseType: 'blob',
    });
  },
  // 产品消耗分布
  consumeProduct(data) {
    return http.post(
      '/transmit/bi/homepage/v1/productConsumptionDistribution',
      data
    );
  },

  exportConsumeProduct(data) {
    return http.post(
      '/transmit/bi/homepage/v1/productConsumptionDistribution/export',
      data,
      {
        responseType: 'blob',
      }
    );
  },
  // 厂商消耗分布
  consumeBrand(data) {
    return http.post(
      '/transmit/bi/homepage/v1/brandConsumptionDistribution',
      data
    );
  },
  exportConsumeBrand(data) {
    return http.post(
      '/transmit/bi/homepage/v1/exportBrandConsumptionDistribution',
      data,
      {
        responseType: 'blob',
      }
    );
  },
  // 媒体消耗分布
  consumeMedia(data) {
    return http.post(
      '/transmit/bi/homepage/v1/mediaConsumptionDistribution',
      data
    );
  },
  exportConsumeMedia(data) {
    return http.post(
      '/transmit/bi/homepage/v1/mediaConsumptionDistribution/export',
      data,
      {
        responseType: 'blob',
      }
    );
  },
  // 优化师分析
  optimiserAnalyses(data) {
    return http.post('/transmit/bi/homepage/v1/optimizerAnalysis', data);
  },
  exportOptimiserAnalyses(data) {
    return http.post(
      '/transmit/bi/homepage/v1/optimizerAnalysis/export',
      data,
      {
        responseType: 'blob',
      }
    );
  },
  // 优化师消耗 - 产品维度
  optimiserConsumeProduct(data) {
    return http.post(
      '/transmit/bi/homepage/v1/productOptimizerConsumptionRanking',
      data
    );
  },
  // 优化师消耗 - 媒体维度
  optimiserConsumeMedia(data) {
    return http.post(
      '/transmit/bi/homepage/v1/mediaOptimizerConsumptionRanking',
      data
    );
  },
  // 优化师消耗分布
  optimiserConsumeDistribution(data) {
    return http.post(
      '/transmit/bi/homepage/v1/optimizerConsumptionDistribution',
      data
    );
  },
  exportOptimiserConsumeDistribution(data) {
    return http.post(
      '/transmit/bi/homepage/v1/optimizerConsumptionDistribution/export',
      data,
      {
        responseType: 'blob',
      }
    );
  },
  // 优化师消耗 - 奋斗分
  optimiserConsumeStruggle(data) {
    return http.post(
      '/transmit/bi/homepage/v1/productOptimizerFightScoreRanking',
      data
    );
  },
};
export default null;
