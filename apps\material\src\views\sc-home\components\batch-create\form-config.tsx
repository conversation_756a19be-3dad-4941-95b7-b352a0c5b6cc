/* eslint-disable import/no-relative-packages */
/* eslint-disable import/prefer-default-export */

import Upload from './components/upload.vue';

export const formSchema = {
  fields: [
    {
      label: '选择合成类型',
      name: '',
      type: 'title',
    },
    {
      label: '',
      hideLabel: true,
      name: 'uploadFormat',
      type: 'select',
      format: 'radio',
      disabled: true,
      source: {
        data: [
          {
            label: '解压小说',
            value: 1,
          },
          {
            label: '滚屏小说',
            value: 2,
          },
        ],
      },
    },
    {
      label: '配置合成规则',
      name: '',
      type: 'title',
    },
    {
      label: '背景音乐',
      // hideLabel: true,
      name: 'noMusic',
      type: 'select',
      format: 'radio',
      required: true,
      source: {
        data: [
          {
            label: '随机一首循环',
            value: false,
          },
          {
            label: '不要BGM',
            value: true,
          },
        ],
      },
    },
    {
      label: '字幕显示',
      name: 'showSubtitle',
      type: 'select',
      format: 'radio',
      required: true,
      source: {
        data: [
          {
            label: '显示',
            value: true,
          },
          {
            label: '不显示',
            value: false,
          },
        ],
      },
    },
    {
      label: '生成数量',
      name: 'taskCount',
      type: 'number',
      min: 1,
      max: 30,
      precision: 0,
      step: 1,
      required: true,
      style: {
        width: '30%',
      },
      placeholder: '请输入生成数量',
    },
    {
      viewType: 'card',
      name: 'file',
      label: '上传区域',
      type: 'object',
      fields: [
        {
          label: '文件',
          name: 'files',
          required: true,
          multiple: true,
          draggable: true,
          limit: 200,
          type: 'custom',
          component: Upload,
          params: {
            limit: 200,
          },
        },
      ],
    },
  ],
};

export const defaultFormData = () => {
  return {
    uploadFormat: 1,
    noMusic: false,
    showSubtitle: true,
    file: {
      files: [],
    },
    taskCount: 6,
    taskName: '',
  };
};
