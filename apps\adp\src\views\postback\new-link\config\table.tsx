import { postbackLink<PERSON>pi, postbackLinkOtherApi } from '@/api/postback/link';
import useAppId from '@/hooks/app-id';
import linkAuthKey from '@/constants/auth-key/postback/link';
import { useAppStore } from '@/store';
import { getAppGroupMap } from '@/views/postback/new-link/utils';
import { getPostbackRecord } from '@/views/postback/new-link/config/operations/postback-record';
import { newLinkType } from '@/views/postback/new-link/constants';
import ToolTipTable from '@/views/postback/components/tooltip-table/index.vue';
import { createOperation } from './operations/primary';
import { getManualPassBack } from './operations/manual-pass-back';
import { getCopyConfig } from './operations/copy';
import { getDeleteConfig } from './operations/delete';
import ExpandList from '../components/expand-list/index.vue';

const appId = useAppId();
export const getTableJson = () => {
  const currentApp: any = useAppStore().appInfo.curApp;
  const groupMap = getAppGroupMap(currentApp);
  const { isApplet, isApp, isApk, isQuickApp } = groupMap;
  // 硬核游戏类
  const isHardGame = isQuickApp && currentApp?.type === 7;
  // 一体两面小说类
  const isNovel = isApp && currentApp?.type === 15;

  const columns = [
    {
      title: '状态',
      dataIndex: 'status',
      fixed: 'left',
      customRender: {
        type: 'switch',
        props: {
          confirm: {
            title: '提示',
            content: '关闭后该链接将停止回传，请确认！',
            times: [0],
          },
          action: (value, record) => {
            return postbackLinkOtherApi.batchModify({
              idList: [record.id],
              value: `${value}`,
              opType: 'newLinkStatus',
            });
          },
        },
      },
    },
    {
      title: '链接ID',
      dataIndex: 'linkId',
      fixed: 'left',
      width: 100,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      width: 100,
    },
    {
      title: '计数数字',
      dataIndex: 'reportValue',
      width: 100,
    },
    {
      title: '计数策略',
      dataIndex: 'countStrategyName',
      width: 100,
      render: ({ record }) => {
        return <ExpandList type="count" record={record} />;
      },
    },
    {
      title: '通用策略',
      dataIndex: 'transmitStrategyName',
      width: 100,
      render: ({ record }) => {
        return <ExpandList type="transmit" record={record} />;
      },
    },
    {
      title: '补回传策略',
      dataIndex: 'supplyList',
      width: 100,
      render: ({ record }) => {
        return <ExpandList type="supply" record={record} />;
      },
    },
    {
      title: '账户名称',
      dataIndex: 'advertiserName',
      width: 200,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '落地页样式',
      dataIndex: 'styleName',
      align: 'center',
      width: 100,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '唤醒路径',
      dataIndex: 'revokePath',
      align: 'center',
      width: 100,
    },
    {
      // title: isApplet ? '启动参数' : 'hap链接',
      title: (() => {
        if (isApplet) {
          return '启动参数';
        }
        if (isApp || isApk) {
          return '直达链接';
        }
        return 'hap链接';
      })(),
      // dataIndex: isApplet ? 'launchUrl' : 'hapUrl',
      dataIndex: (() => {
        if (isApplet) {
          return 'launchUrlList';
        }
        if (isApp || isApk) {
          return 'directLinkList';
        }
        return 'hapUrlList';
      })(),
      align: 'center',
      width: 105,
      customRender: {
        type: (
          <ToolTipTable
            auth={linkAuthKey.POSTBACK_LINK_VIEW}
            isNovel={isNovel}
          />
        ),
      },
    },
    {
      title: 'h5链接',
      dataIndex: 'h5UrlList',
      align: 'center',
      width: 105,
      isH5: true,
      customRender: {
        type: (
          <ToolTipTable
            auth={linkAuthKey.POSTBACK_LINK_VIEW}
            isNovel={isNovel}
          />
        ),
      },
    },
    {
      title: '点击监测链接',
      dataIndex: 'clickUrlList',
      align: 'center',
      width: 150,
      customRender: {
        type: (
          <ToolTipTable
            auth={linkAuthKey.POSTBACK_LINK_VIEW}
            isNovel={isNovel}
          />
        ),
      },
    },
    {
      title: '曝光监测链接',
      dataIndex: 'showUrlList',
      width: 150,
      customRender: {
        type: (
          <ToolTipTable
            auth={linkAuthKey.POSTBACK_LINK_VIEW}
            isNovel={isNovel}
          />
        ),
      },
    },
    {
      title: '所属人',
      dataIndex: 'ownerName',
      width: 150,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '更新人',
      dataIndex: 'updaterName',
      width: 150,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateAt',
      width: 170,
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'operations',
      fixed: 'right',
      width: 350,
      customRender: {
        type: 'operations',
        props: {
          showOperationCount: 5,
          operations: [
            createOperation({ mode: 'modify' }),
            getPostbackRecord(),
            getManualPassBack(),
            getCopyConfig(),
            getDeleteConfig(),
          ],
        },
      },
    },
  ];
  if (isHardGame) {
    const h5Index = columns.findIndex((item) => item.isH5);
    if (h5Index !== -1) {
      const hapColumns = [
        {
          title: '硬核hap链接',
          dataIndex: 'hardCoreHapUrlList',
          align: 'center',
          width: 105,
          customRender: {
            type: (
              <ToolTipTable
                auth={linkAuthKey.POSTBACK_LINK_VIEW}
                isNovel={isNovel}
              />
            ),
          },
        },
        {
          title: '硬核h5链接',
          dataIndex: 'hardCoreH5UrlList',
          align: 'center',
          width: 105,
          customRender: {
            type: (
              <ToolTipTable
                auth={linkAuthKey.POSTBACK_LINK_VIEW}
                isNovel={isNovel}
              />
            ),
          },
        },
      ];
      columns.splice(h5Index + 1, 0, ...hapColumns);
    }
  }
  return {
    load: {
      action: async (filter, pagination) => {
        const { ...filterData } = filter;
        const formatLinkList =
          filterData.linkIdList?.map((item) => item.linkId) || [];
        filterData.linkIdList = formatLinkList;
        const params = {
          ...filterData,
          ...pagination,
          applicationId: appId.value,
          ...newLinkType,
        };
        const res: any = await postbackLinkApi.list?.(params);
        return res;
      },
    },
    columns,
  };
};
