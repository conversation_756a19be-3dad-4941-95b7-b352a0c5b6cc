<template>
  <DynamicForm class="dir-item" v-model="formData" :form-schema="formSchema">
  </DynamicForm>
</template>

<script setup lang="ts">
  import { useFormItem, Message } from '@arco-design/web-vue';
  import { watch } from 'vue';
  import { useClTree } from '@/hooks/cl-material-tree';

  const props = defineProps<{
    params: any;
  }>();
  const {
    trees,
    albumList,
    currentAlbumId,
    defaultList,
    updateTrees,
    updateFolderList,
  } = useClTree();
  const { eventHandlers } = useFormItem();
  const formData = defineModel<any>();

  const formSchema = {
    fields: [
      {
        name: 'albumId',
        label: '',
        type: 'select',
        format: 'singleSelect',
        placeholder: '请搜索并选择专辑',
        select: {
          // allowClear: true,
          allowSearch: true,
          isAsyncSearch: true,
          tooltip: true,
        },
        hideLabel: true,
        source: {
          valueKey: 'id',
          labelKey: 'name',
          data: async (_field, form, _path, text) => {
            const search = text || form.albumName;
            const data = await updateFolderList(search);
            return data;
          },
        },
        onChange(value) {
          currentAlbumId.value = value;
          const currentOptions = [...albumList.value];
          updateTrees();
          formData.value.folder = { label: '', value: '' };
          if (value) {
            const target: any = currentOptions.find(
              (item: any) => item.id === value
            );
            if (target) {
              formData.value.albumName = target.name;
            } else {
              const defaultTarget = defaultList.find(
                (item: any) => item.id === value
              );
              if (defaultTarget) {
                formData.value.albumName = defaultTarget.name;
              }
            }
          } else {
            formData.value.albumName = '';
          }
        },
      },
      {
        name: 'folder',
        label: '',
        type: 'treeSelect',
        hideLabel: true,
        select: {
          // allowClear: true,
          allowSearch: true,
          labelInValue: true,
          fullPath: true,
          multiple: props.params?.multiple,
          checkable: props.params?.multiple,
          treeCheckStrictly: props.params?.multiple,
          // data为source里的data
          customTitle: (data) => {
            return ` ${data.title} `;
          },
        },
        placeholder: '请选择文件夹',
        source: {
          valueKey: 'key',
          labelKey: 'path',
          data: trees,
        },
        onChange: (value: any, form: any) => {
          if (value.length > 10 && props.params?.multiple) {
            form.value.folder = value.pop();
            Message.warning('最多只能选择10个文件夹');
          }
        },
      },
    ],
  };

  watch(
    () => formData?.value?.albumId,
    (val) => {
      currentAlbumId.value = val;
      updateTrees();
    },
    { immediate: true }
  );
  watch(
    () => formData.value,
    () => {
      eventHandlers.value?.onChange?.();
    },
    { deep: true }
  );
</script>

<style scoped>
  /* .dir-item {
    display: flex;
    align-items: center;
  } */
</style>
