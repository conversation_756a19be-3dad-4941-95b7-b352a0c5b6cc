import { http } from '@/utils/http';

export const dictCommonApi = {
  // 字段类型下拉列表
  fileTypeList: (data) => {
    return http.get('/common/adReportColumn/v1/type/list', data);
  },
  // 新增字段
  addField: (data) => http.post('/common/adReportColumn/v1/save', data),
  // 修改字段
  modifyField: (data) => http.post('/common/adReportColumn/v1/update', data),
  // 删除字段
  deleteField: (data) => http.post('/common/adReportColumn/v1/update', data),
  // 字段详情
  fieldDetail: (data) => http.get('/common/adReportColumn/v1/get', data),
  // table列表
  fieldList: (data) => http.post('/common/adReportColumn/v1/page', data),
};
export default null;
