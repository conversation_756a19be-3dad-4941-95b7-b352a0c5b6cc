<template>
  <div class="authorize-account">
    <a-modal
      :visible="visible"
      title="授权账户"
      @cancel="handleCancel"
      fullscreen
      :render-to-body="false"
    >
      <div class="container">
        <a-steps :current="step" @change="setCurrent" class="step_container">
          <a-step>选择账户</a-step>
          <a-step>绑定链接</a-step>
          <a-step v-if="isAllLinked.length !== 0">关联回传</a-step>
          <a-step v-if="stepVisible">创建广告</a-step>
        </a-steps>
        <keep-alive :include="visible ? ['SelectAccount', 'PickAccount'] : ''">
          <PickAccount
            v-if="canPagingMedia && step === 1"
            v-model="formData"
            :params="pickParams"
            ref="pickAccountRef"
          />
          <SelectAccount
            ref="selectAccountRef"
            v-else-if="!canPagingMedia && step === 1"
            v-model="formData"
            :visible="visible"
            :media-type="mediaType"
            :qtt-ids="qttIds"
          />
          <SelectLink
            v-model="formData"
            v-else-if="step === 2"
            :link-order="linkOrder"
            :media-type="mediaType"
            @change-link-order="changeLinkOrder"
          />
          <RelatePostBack
            ref="relatePostBackRef"
            v-model="formData"
            v-else-if="step === 3"
          />
          <CreateAd :media-type="mediaType" v-else-if="step === 4" />
        </keep-alive>
      </div>
      <template #footer>
        <a-space size="large">
          <a-button type="secondary" v-if="step === 1" @click="handleCancel">
            取消
          </a-button>
          <a-button
            type="secondary"
            v-if="step > 1 && step < 4"
            :disabled="step <= 1"
            @click="onPrev"
          >
            <IconLeft /> 上一步
          </a-button>
          <a-button
            type="primary"
            v-if="nextBtnShow"
            :disabled="nextButtonDisabled"
            @click="onNext"
            :loading="nextLoading"
          >
            下一步<IconRight />
          </a-button>
          <a-button
            type="primary"
            v-if="step === 2 && isAllLinked.length === 0"
            :disabled="nextButtonDisabled"
            @click="onNext"
            :loading="nextLoading"
          >
            确认
          </a-button>
          <a-button
            type="primary"
            v-if="step === 3 && !createBatchAd"
            :disabled="nextButtonDisabled"
            @click="onNext"
            :loading="nextLoading"
          >
            确认
          </a-button>
          <a-button type="primary" v-if="step === 4" @click="handleCancel">
            确认
          </a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, Ref, watch } from 'vue';
  import { cloneDeep, compact, map, omit } from 'lodash';
  import { useRoute, useRouter } from 'vue-router';
  import {
    mediaPlatformMap,
    MediaType,
  } from '@/views/promotion/hooks/authorization';
  import {
    accountGdtApi,
    accountKsApi,
    accountTtApi,
  } from '@/api/promotion/account';
  import accountCommonApi from '@/api/promotion/common';
  import useAppId from '@/hooks/app-id';
  import { Message } from '@arco-design/web-vue';
  import { linkMatchLinkType } from '@/views/promotion/account/component/authorize-account/components/utils';
  import SelectAccount from './components/select-account.vue';
  import SelectLink from './components/select-link/index.vue';
  import RelatePostBack from './components/relate-post-back.vue';
  import CreateAd from './components/create-ad.vue';
  import { defaultFormData, formatCountConfig } from './constants';
  import { getCustomSubmitFormData } from './components/select-link/form';
  import PickAccount from './components/pick-account/index.vue';

  const appId = useAppId();
  const visible: any = defineModel<boolean>();
  const emit = defineEmits(['success', 'close']);
  const relatePostBackRef = ref();
  const props = defineProps<{
    mediaType: number;
    qttIds?: string[];
  }>();
  const route = useRoute();
  const router = useRouter();
  const step: Ref<number> = ref(1);
  const formData: any = ref(cloneDeep(defaultFormData));
  const linkOrder: any = ref(''); // 选择链接的顺序   链接模板/自定义配置
  const isAuthSuccess: any = ref(false);
  const nextLoading = ref(false);

  // 后端分页的账户选择媒体
  // 目前： 快手 和趣头条
  const canPagingMedia = computed(() => {
    return [MediaType.KUAI_SHOU, MediaType.QU_TOU_TIAO].includes(
      props.mediaType
    );
  });
  const pickParams = computed(() => {
    const data = {
      mediaType: props.mediaType,
      qttIds: props.qttIds,
      visible: visible.value,
    };
    return data;
  });
  const authCodeComputed = () => {
    switch (props.mediaType) {
      case MediaType.TOU_TIAO:
      case MediaType.KUAI_SHOU:
        return 'auth_code';
      case MediaType.HUA_WEI:
      case MediaType.GDT:
        return 'authorization_code';
      case MediaType.BAI_DU:
        return 'authCode';
      default:
        return 'code';
    }
  };

  // 为0 是全有链接的

  const isAllLinked = computed(() => {
    return formData.value.adAccountList.filter((item) => !item.linkId);
  });

  const stepVisible = computed(() => {
    if (isAllLinked.value.length !== 0 && createBatchAd.value) {
      return true;
    }
    return false;
  });

  const nextButtonDisabled = computed(() => {
    if (step.value === 1) {
      return formData.value.adAccountList.length === 0;
    }
    if (step.value === 2) {
      return (
        formData.value.adAccountList.some((item) => !item.linkId) &&
        linkOrder.value === ''
      );
    }
    if (step.value === 3) {
      // const flag = formData.value.adAccountList.every((item) => item.linkId);
      // return flag ? false : !formData.value?.ruleConfigTemplateId;
      return false;
    }
    return false;
  });

  const createBatchAd = computed(() => {
    return ['TOUTIAO', 'KUAISHOU', 'GDT'].includes(
      mediaPlatformMap[props.mediaType]
    );
  });

  const nextBtnShow = computed(() => {
    if (step.value === 2 && isAllLinked.value.length === 0) {
      return false;
    }
    if (
      (step.value === 3 && !createBatchAd.value) ||
      (step.value === 4 && createBatchAd.value)
    ) {
      return false;
    }

    return true;
  });

  function handleCancel() {
    if (props.mediaType !== MediaType.QU_TOU_TIAO) {
      const query = omit(route.query, [authCodeComputed(), 'state']);
      router.replace({ query });
      emit('close');
    }
    visible.value = false;
  }

  function changeLinkOrder(order: string) {
    linkOrder.value = order;
  }

  function setCurrent(current: number) {
    step.value = current;
  }
  function onPrev() {
    step.value = Math.max(1, step.value - 1);
  }

  function delayClose() {
    return new Promise((res, rej) => {
      setTimeout(() => {
        res(1);
      }, 1500);
    });
  }

  const selectAccountRef = ref<any>();
  const pickAccountRef = ref<any>();
  async function onNext() {
    nextLoading.value = true;
    if (step.value === 1) {
      if (!validatorAgent()) {
        Message.warning('请选择代理商');
        nextLoading.value = false;
        return;
      }
      if (!validatorLink()) {
        Message.warning('投放链接重复，请重新选择');
        nextLoading.value = false;
        return;
      }
      let linkOptions: any;
      if (canPagingMedia.value) {
        linkOptions = pickAccountRef.value?.getLinkOptions?.() ?? [];
      } else {
        linkOptions = selectAccountRef.value?.getLinkOptions?.() ?? [];
      }

      linkMatchLinkType({
        adCountList: formData.value.adAccountList,
        linkOptions,
      });
    } else if (step.value === 2 && isAllLinked.value.length === 0) {
      try {
        await linkedSubmit();
        if (props.mediaType === MediaType.TOU_TIAO) {
          await delayClose();
        }
        handleCancel();
        Message.success('授权成功');
        emit('success');
      } catch (e) {
        console.log('e', e);
        Message.error('授权失败');
        return;
      } finally {
        nextLoading.value = false;
      }
    } else if (step.value === 3) {
      await submit();
      if (isAuthSuccess.value || !createBatchAd.value) {
        return;
      }
    }
    step.value = Math.min(4, step.value + 1);
    nextLoading.value = false;
  }

  function validatorLink() {
    // 选择的所有链接
    const linkIdList: any = [];
    formData.value.adAccountList.forEach((item, index) => {
      if (item.linkId !== null && item.linkId !== '') {
        linkIdList.push(item.linkId);
      }
    });
    const linkObj = {};
    const repeatList: any = [];
    linkIdList.forEach((element) => {
      if (linkObj[element]) {
        repeatList.push(element);
      } else {
        linkObj[element] = true;
      }
    });

    formData.value.adAccountList.forEach((item, index) => {
      if (repeatList.includes(item.linkId)) {
        formData.value.adAccountList[index].linkShowTips = true;
      } else {
        formData.value.adAccountList[index].linkShowTips = false;
      }
    });
    if (repeatList.length !== 0) {
      return false;
    }
    return true;
  }

  function validatorAgent() {
    const agentIdList: any = [];
    formData.value.adAccountList.forEach((item) => {
      agentIdList.push(item.agentId);
    });
    if (compact(agentIdList).length !== agentIdList.length) {
      return false;
    }
    return true;
  }

  async function linkedSubmit() {
    let save: any;
    switch (props.mediaType) {
      case MediaType.TOU_TIAO:
        save = accountTtApi.batchUpload;
        break;
      case MediaType.KUAI_SHOU:
        save = accountKsApi.batchUpload;
        break;
      case MediaType.GDT:
        save = accountGdtApi.batchUpload;
        break;
      default:
        save = accountCommonApi.batchUpload;
        break;
    }

    await save?.(
      map(formData.value.adAccountList, (item) => ({
        ...omit(item, ['linkShowTips']),
        mediaPlatform: props.mediaType,
        applicationId: window.$appInfo?.id,
      }))
    );
  }

  async function submit() {
    await relatePostBackRef.value?.validator();
    isAuthSuccess.value = false;
    const submitData = cloneDeep(formData.value);
    submitData.customConfig = getCustomSubmitFormData(submitData.customConfig);
    submitData.adAccountList = formData.value.adAccountList.map((item) => {
      item.mediaPlatform = props.mediaType;
      item.applicationId = appId.value;
      delete item.linkShowTips;
      return item;
    });
    if (linkOrder.value !== 'custom') {
      submitData.customConfig = null;
    }
    submitData.countConfig = submitData.ruleConfigTemplateId
      ? formatCountConfig(submitData.countConfig)
      : null;
    try {
      await accountCommonApi.bindLink(submitData);
      if (!createBatchAd.value) {
        handleCancel();
      }
      Message.success('授权成功');
      emit('success');
    } catch (e) {
      Message.error('授权失败');
      isAuthSuccess.value = true;
    } finally {
      nextLoading.value = false;
    }
  }
  watch(visible, (newVal) => {
    if (!newVal) {
      step.value = 1;
      formData.value = cloneDeep(defaultFormData);
      linkOrder.value = '';
      isAuthSuccess.value = false;
      nextLoading.value = false;
    }
  });
</script>

<style scoped lang="less">
  .container {
    margin: 0 24px;

    .step_container {
      margin: 0 auto 40px;
      width: 70%;
    }
  }

  .authorize-account {
    :deep(.arco-modal-fullscreen) {
      width: 80%;
    }
  }
</style>
