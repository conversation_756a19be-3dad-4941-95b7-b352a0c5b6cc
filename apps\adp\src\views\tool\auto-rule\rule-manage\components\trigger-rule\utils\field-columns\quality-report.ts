import { isNil, omit } from 'lodash';
import {
  beforeDayMap,
  columnCodeMap,
  ruleTargetEnum,
} from '@/views/tool/auto-rule/rule-manage/constants';
import { mediaNumMap } from '@/views/tool/common';
import { filterIfUse } from '../index';

export function getQualityReportColumns({
  canFilter = true,
  ruleTarget,
  quantityFieldsCodes,
  beforeDay,
  currentPathMap,
  checkMediaMap,
}: {
  // 是否进行条件判断
  canFilter?: boolean;
  // 账户规则
  ruleTarget?: ruleTargetEnum.account | ruleTargetEnum.ad;
  // 质量分报表远程字段
  quantityFieldsCodes?: string[];
  // 当天 or 昨天
  beforeDay?: number;
  // 当前路径
  currentPathMap?: Record<string, boolean>;
  // 选中的媒体
  checkMediaMap?: Record<string, boolean>;
}) {
  // 如果选择了三天或者七天
  const beforeDayFlag = isNil(beforeDay)
    ? false
    : [beforeDayMap.threeDays, beforeDayMap.sevenDays].includes(beforeDay);
  const baseColumns = [
    {
      label: '消耗',
      value: 'media_cost',
    },
    {
      label: '创建时间',
      value: columnCodeMap.campaignCreateTime,
    },
    {
      label: '点击',
      value: 'media_ad_click_num',
    },
    {
      label: '展示',
      value: 'media_ad_show_num',
    },
    {
      label: 'cpm质量分',
      value: 'cpm_score',
    },
    {
      label: '质量分',
      value: 'quality_score',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: 'uv成本',
      value: 'cost_per_uv',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '转化成本',
      value: 'media_convert_cost',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: 'hap拉起率',
      value: 'hap_active_ratio',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '实时roi',
      value: columnCodeMap.timeRoi,
      ifUse: () => {
        return (
          quantityFieldsCodes?.includes(columnCodeMap.timeRoi) && !beforeDayFlag
        );
      },
    },
    {
      label: '点击率',
      value: 'media_ad_click_ratio',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '转化',
      value: 'media_covert_num',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '转化率',
      value: 'media_convert_ratio',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '优数UV',
      value: 'hap_uv',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '优数广告点击',
      value: 'ad_click_num',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '优数广告展示',
      value: 'ad_show_num',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '人均点击',
      value: 'ad_click_num_per_uv',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '均次点击',
      value: 'ad_click_num_per_pv',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '广告点击UV占比',
      value: 'ad_click_uv_ratio',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '广告展示UV占比',
      value: 'ad_show_uv_ratio',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '余额',
      value: columnCodeMap.mediaBalance,
      ifUse: () => {
        return ruleTarget === ruleTargetEnum.account && !beforeDayFlag;
      },
    },
    {
      label: '广告关停时间',
      value: columnCodeMap.adCloseTime,
      ifUse: () => {
        return (
          ruleTarget === ruleTargetEnum.ad &&
          beforeDay === beforeDayMap.yesterday &&
          !beforeDayFlag
        );
      },
    },

    {
      label: '激活当日ROI（计算）',
      value: 'template_roi',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: '激活当日付费金额（计算）',
      value: 'template_value',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
    {
      label: 'ecpm',
      value: 'media_ecpm',
      ifUse: () => {
        return !beforeDayFlag;
      },
    },
  ];

  if (canFilter) {
    return filterIfUse({ ifList: baseColumns });
  }
  return baseColumns.map((item) => {
    return omit(item, 'ifUse');
  });
}
