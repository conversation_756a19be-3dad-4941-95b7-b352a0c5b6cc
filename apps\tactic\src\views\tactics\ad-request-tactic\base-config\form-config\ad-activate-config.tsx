import switchConfig from '@/views/tactics/common/switch-config';

export const getAdActivateConfigSchema = () => {
  return [
    {
      type: 'object',
      name: 'adActivateConfig',
      hideLabel: true,
      fields: [
        {
          type: 'viewCard',
          label: '广告补激活',
          contentStyle: {
            width: '640px',
          },
          fields: [
            {
              label: '启用开关',
              name: 'enable',
              ...switchConfig,
              span: 6,
              rules: [{ required: true }],
            },
            {
              label: '补激活概率',
              name: 'adActivateRate',
              type: 'number',
              span: 6,
              min: 0,
              max: 100,
              suffix: '%',
              rules: [{ required: true }],
              visibleOn: (data) => {
                return data.enable === 1;
              },
            },
            {
              label: '下发待激活应用数量',
              name: 'unActivateAppCount',
              type: 'number',
              span: 6,
              precision: 0,
              min: 0,
              rules: [{ required: true }],
              visibleOn: (data) => {
                return data.enable === 1;
              },
            },
          ],
        },
      ],
    },
  ];
};

export const getAdActivateConfigData = () => {
  return {
    adActivateConfig: {
      enable: 1,
      adActivateRate: 30,
      unActivateAppCount: 3,
    },
  };
};

export default null;
