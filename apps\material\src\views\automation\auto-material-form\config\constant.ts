export const CONSTANT = {
  mediaCodeOptions: [
    {
      label: '全部',
      value: 0,
    },
    {
      label: '快手',
      value: 100,
    },
    {
      label: '头条',
      value: 101,
    },
    {
      label: '广点通',
      value: 102,
    },
    // {
    //   label: '百度',
    //   value: 103,
    // },
  ],
  StartStrategy: [
    { label: '指定文件夹', value: 1 },
    { label: '跟踪计划', value: 3 },
    { label: '三方平台', value: 2 },
  ],
  sourceOptions: [
    { label: '原料库', value: 1 },
    { label: '优量', value: 2 },
    { label: '创量', value: 3 },
  ],
  ProcessingType: [
    { label: '原料加工', value: 1 },
    { label: '去重衍生', value: 2 },
  ],
  ProductType: [
    { label: '小说素材', value: 1 },
    { label: '快应用素材', value: 2 },
  ],
  VideoType: [
    { label: '解压素材', value: 1 },
    { label: '滚屏素材', value: 2 },
  ],
  AuditStrategy: [
    { label: '免审核', value: 1 },
    { label: '手动审核', value: 3 },
  ],
  // 插件安装文档路径
  PluginInstallDocPath:
    'https://alidocs.dingtalk.com/i/nodes/qnYMoO1rWxbByL5mskkDpyY2J47Z3je9?doc_type=wiki_doc&utm_medium=dingdoc_doc_plugin_card&utm_scene=team_space&utm_source=dingdoc_doc',
};

// 三方平台
export const ThirdPlatform = {
  fx: { label: '风行千推', value: 1 },
  // manual: { label: '手动上传', value: 2 },
};

// 执行规则
export const ExecuteRule = {
  // 固定执行频次 创意触发
  FixedFrequency: { label: '固定执行频次', value: 1 },
  // 创意触发
  CreativeTrigger: { label: '创意触发', value: 2 },
};

export const MEDIA_OPTIONS = [
  {
    label: '快手',
    value: 100,
  },
  {
    label: '头条',
    value: 101,
  },
];

export const FILTER_OPTIONS = [
  {
    label: '消耗',
    value: 'media_cost',
  },
  {
    label: '点击数',
    value: 'media_ad_click_num',
  },
  {
    label: '点击率',
    value: 'media_ad_click_ratio',
  },
  {
    label: '转化数',
    value: 'media_covert_num',
  },
  {
    label: '转化率',
    value: 'media_convert_ratio',
  },
  {
    label: '转化成本',
    value: 'media_convert_cost',
  },
  {
    label: 'cpc(平均点击单价)',
    value: 'media_cpc',
  },
];
export const FOLDER_FILTER_OPTIONS_MAP = {
  0: [
    {
      label: '消耗',
      value: 'media_cost',
    },
    {
      label: '点击率',
      value: 'media_ad_click_ratio',
    },
    {
      label: '转化率',
      value: 'media_convert_ratio',
    },
    {
      label: '转化成本',
      value: 'media_convert_cost',
    },
  ],
  101: [
    {
      label: '消耗',
      value: 'media_cost',
    },
    {
      label: '点击率',
      value: 'media_ad_click_ratio',
    },
    {
      label: '转化率',
      value: 'media_convert_ratio',
    },
    {
      label: '转化成本',
      value: 'media_convert_cost',
    },
    {
      label: 'cpc(平均点击单价)',
      value: 'media_cpc',
    },
    // {
    //   label: '次留率',
    //   value: 'next_day_retention_rate',
    // },
  ],
  100: [
    {
      label: '花费',
      value: 'media_cost',
    },
    {
      label: '素材点击率',
      value: 'media_ad_click_ratio',
    },
    {
      label: '转化率(回传时间)',
      value: 'media_convert_ratio',
    },
    {
      label: '转化成本(回传时间)',
      value: 'media_convert_cost',
    },
    {
      label: 'cpc(平均封面点击单价)',
      value: 'media_cpc',
    },
  ],
  102: [
    {
      label: '消耗',
      value: 'media_cost',
    },
    {
      label: '点击率',
      value: 'media_ad_click_ratio',
    },
    {
      label: '目标转化率',
      value: 'media_convert_ratio',
    },
    {
      label: 'cpc(点击均价)',
      value: 'media_cpc',
    },
    // {
    //   label: '次日留存率',
    //   value: 'next_day_retention_rate',
    // },
  ],
};
export const PLAN_FILTER_OPTIONS = [
  {
    label: '当天消耗',
    value: 'media_cost',
  },
  {
    label: '创建时间',
    value: 'create_time',
  },
  {
    label: 'ROI',
    value: 'ad_click_income_roi',
  },
];

export const COMPARE_OPTIONS = [
  {
    label: '大于',
    value: 'gt',
  },
  {
    label: '大于等于',
    value: 'gte',
  },
  {
    label: '小于',
    value: 'lt',
  },
  {
    label: '小于等于',
    value: 'lte',
  },
  // {
  //   label: '等于',
  //   value: 'equal ',
  // },
  {
    label: 'Top',
    value: 'top',
    disabled: false,
  },
];
export const COMPARE_MAP = {
  create_time: [
    {
      label: '等于',
      value: 'equal',
    },
  ],
  ad_click_income_roi: [
    {
      label: '大于',
      value: 'gt',
    },
    {
      label: '大于等于',
      value: 'gte',
    },
  ],
  media_cost: COMPARE_OPTIONS,
  media_ad_click_num: COMPARE_OPTIONS,
  media_ad_click_ratio: COMPARE_OPTIONS,
  media_covert_num: COMPARE_OPTIONS,
  media_convert_ratio: COMPARE_OPTIONS,
  media_cpc: COMPARE_OPTIONS,
  media_convert_cost: COMPARE_OPTIONS,
};
export const VALUE_MAP = {
  create_time: [
    {
      label: '当天创建的计划',
      value: 0,
    },
    {
      label: '近3天创建的计划',
      value: 3,
    },
    {
      label: '近7天创建的计划',
      value: 7,
    },
  ],
  ad_click_income_roi: 'float',
  media_cost: 'float',
  media_ad_click_num: 'number',
  media_ad_click_ratio: 'float',
  media_covert_num: 'number',
  media_convert_ratio: 'float',
  media_convert_cost: 'float',
  media_cpc: 'float',
  // next_day_retention_rate: 'float',
};

export default CONSTANT;
