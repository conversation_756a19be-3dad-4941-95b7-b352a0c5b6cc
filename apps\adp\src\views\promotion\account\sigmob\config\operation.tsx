import account from '@/constants/auth-key/promotion/account';
import { MediaType } from '@/views/promotion/hooks/authorization';
import { getCustomFormData } from '@/views/promotion/account/component/authorize-account/constants';
import PassportAuthorize from '@/views/promotion/account/component/authorize-account/passport-authorize/index.vue';
import { getFormSchema, getFormData } from '../constants';

const getOperation = (successHandle?) => {
  return [
    {
      text: '新增账户',
      auth: account.ACCOUNT_SIGMOB_ADD,
      props: {
        type: 'primary',
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: '授权账户',
          width: 1200,
          fullscreen: false,
          footer: false,
        },
        contentType: 'custom',
        custom: (
          <PassportAuthorize
            formSchema={getFormSchema()}
            media-type={MediaType.SIGMOB}
            onSuccess={successHandle}
          />
        ),
        getDefaultValue: () => {
          return {
            list: [getFormData()],
            adAccountList: [],
            linkTemplateId: undefined,
            customConfig: getCustomFormData(),
            ruleConfigTemplateId: undefined,
            countConfig: {
              reportType: '',
              countType: 'CID',
              reportValue: 1,
              countEventConfig: {
                eventId: '',
                eventName: '',
                propertyList: [],
                rewardVideo: false,
              },
            },
          };
        },
        close: (data: any) => {
          data.refreshTable();
        },
      },
    },
    {
      text: '批量管理',
      props: {
        type: 'outline',
      },
      clickActionType: 'batch',
    },
  ];
};

export default getOperation;
