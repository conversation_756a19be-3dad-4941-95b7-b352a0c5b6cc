<template>
  <span>
    <template v-if="displayValue">
      <DynamicButton :config="modalEditPrice" :record="record" />
      <span>{{ displayValue }}</span>
    </template>
    <span v-else>-</span>
  </span>
</template>

<script setup lang="tsx">
  import { computed } from 'vue';
  import { DButton } from '@repo/kola/src/components/dynamic/types/button';
  import { getCountDefaultForm } from './count';
  import { getTransmitDefaultForm } from './transmit';
  import { getSupplyDefaultForm } from './supply';

  const { record, type } = withDefaults(
    defineProps<{
      record: any;
      type: string;
    }>(),
    {
      type: 'count',
    }
  );

  const TypeMap = {
    count: {
      text: '计数策略',
      form: getCountDefaultForm(),
    },
    transmit: {
      text: '通用策略',
      form: getTransmitDefaultForm(),
    },
    supply: {
      text: '补回传策略',
      form: getSupplyDefaultForm(),
    },
  };

  // 计算需要显示的值
  const displayValue = computed(() => {
    if (type === 'count' && record.countStrategyId) {
      return record.countStrategyName;
    }
    if (type === 'transmit' && record.transmitStrategyId) {
      return record.transmitStrategyName;
    }
    if (type === 'supply' && record.supplyStrategyId) {
      const { supplyList } = record;
      if (Array.isArray(supplyList) && supplyList.length > 0) {
        return supplyList.map((item) => item.name).join(',');
      }
    }
    return null;
  });

  const modalEditPrice: DButton = {
    text: '',
    icon: 'icon-expand',
    props: {
      type: 'text',
    },
    clickActionType: 'drawer',
    modal: {
      props: {
        title: TypeMap[type].text,
        width: 1218,
        footer: false,
      },
      contentType: 'form',
      ...TypeMap[type].form,
      action: async (value: any) => {
        return value;
      },
    },
  };
</script>

<style scoped>
  :deep(.arco-btn-icon) {
    padding: 0 4px !important;
  }
</style>
