import { DButton } from '@repo/kola/src/components/dynamic/types/button';
import { Message, Notification } from '@arco-design/web-vue';
import { advertisingManageApi } from '@/api/setting/advertising-config/advertising-manage';
import { downloadFile } from '@/utils/download';
import { ADVERTISING_MANAGE } from '@/constants/auth-key/setting/advertisingConfig';
import { isEmpty, isNil } from 'lodash';
import { advertisingFormSchema, exportForm, FIELD_MODE } from './formSchema';
import { getDefaultValue } from './getDefaultValue';
import UploadFile from '../components/upload-file.vue';

const titleText = '新建广告位';
/** Modal Button */
export const modalButtonConfig: DButton = {
  text: titleText,
  auth: ADVERTISING_MANAGE.ADVERTISING_MANAGE_CREATE,
  props: {
    type: 'primary',
    disabled: false,
    loading: false,
    id: '#complete-button',
  },
  clickActionType: 'modal',
  linkUrl: '',
  modal: {
    props: { title: titleText, width: '700px' },
    contentType: 'form',
    form: {
      formSchema: { fields: [...advertisingFormSchema(FIELD_MODE.add)] },
    },
    getDefaultValue,
    action: async (value: any): Promise<boolean | void> => {
      const { refreshTable, formData } = value;
      let { qualityScoreCaliberList } = formData;
      qualityScoreCaliberList = qualityScoreCaliberList?.filter((item) => {
        return (
          !isNil(item.caliberType) &&
          item.caliberType !== '' &&
          item.date &&
          !isEmpty(item.date)
        );
      });

      delete formData.batchConfig;
      const params = {
        ...formData,
        qualityScoreCaliberList,
      };
      await advertisingManageApi.create?.({ ...params });
      Message.success('创建成功');
      return refreshTable();
    },
    close: () => {},
  },
};

export const importButtonConfig: DButton = {
  text: '批量上传广告位',
  auth: ADVERTISING_MANAGE.ADVERTISING_MANAGE_IMPORT,
  props: {
    type: 'primary',
    disabled: false,
    loading: false,
    id: '#complete-button',
  },
  clickActionType: 'modal',
  linkUrl: '',
  modal: {
    props: { title: '批量上传广告位', width: '600px' },
    contentType: 'form',
    form: {
      formSchema: {
        fields: [
          {
            label: '',
            name: 'customData',
            type: 'custom',
            component: UploadFile,
          },
        ],
      },
    },
    getDefaultValue() {
      return {
        customData: {
          applicationId: undefined,
          file: undefined,
        },
      };
    },
    action: async (value: any): Promise<boolean | void> => {
      const { refreshTable, formData } = value;
      const { file, applicationId } = formData.customData;
      const form: any = new FormData();
      form.append('file', file);
      form.append('applicationId', applicationId);
      const {
        data: { status, failList },
      } = await advertisingManageApi.import(form);
      if (status === 'SUCCESS') {
        Message.success('上传成功');
      } else if (status === 'PARTIAL_SUCCESS') {
        Message.warning('部分上传成功');
        const contentHtml = (
          <div>
            {failList.map((errItem, index) => (
              <p key={index}>{errItem.errorMsg}</p>
            ))}
          </div>
        );
        Notification.warning({
          title: '上传失败项',
          content: contentHtml,
          closable: true,
          duration: 0,
        });
      } else {
        Message.error('上传失败');
        const contentHtml = (
          <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
            {failList.map((errItem, index) => (
              <p key={index}>{errItem.errorMsg}</p>
            ))}
          </div>
        );
        Notification.warning({
          title: '上传失败项',
          content: contentHtml,
          closable: true,
          duration: 0,
        });
      }
      return refreshTable();
    },
    close: () => {},
  },
};
const downLoadExport = async (filter) => {
  try {
    const { brands = [], applicationIds = [], keywords = '' } = filter;
    const response = await fetch(
      `/apis/transmit/adreportadsense/v1/export?brands=${brands.join(
        ','
      )}&applicationIds=${applicationIds.join(',')}&keywords=${keywords}`
    );
    if (!response.ok) {
      throw new Error(
        `Failed to download file. Status: ${response.status} - ${response.statusText}`
      );
    }

    const blob = await response.blob();
    downloadFile(blob, `广告位-批量导出.xlsx`, 'application/vnd.ms-excel');
  } catch (error) {
    console.error('Error downloading file:', error);
  }
};
export const exportButtonConfig: DButton = {
  text: '',
  auth: ADVERTISING_MANAGE.ADVERTISING_MANAGE_EXPORT,
  props: {
    type: 'text',
    loading: false,
    id: '#export-button',
  },
  right: true,
  tooltip: '导出数据',
  icon: 'icon-download',
  clickActionType: 'action',
  action: async (value: any) => {
    const { filter } = value;
    downLoadExport(filter);
  },
};

export const batchButtonConfig: DButton = {
  text: '批量管理',
  props: { type: 'primary', disabled: false, loading: false },
  clickActionType: 'batch',
};
const operation: DButton[] = [
  modalButtonConfig,
  importButtonConfig,
  exportButtonConfig,
  batchButtonConfig,
];

export default operation;
