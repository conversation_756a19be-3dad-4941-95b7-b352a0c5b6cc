<template>
  <div class="list-box">
    <ConditionItem
      v-for="(item, index) in list"
      :key="index"
      :index="index"
      v-model="list[index]"
      :list="list"
      :path="path"
      :first-options="firstOptions"
      :second-options="secondOptions"
      :third-options="thirdOptions"
      :can-delete="canDelete"
      @remove="handleDelete"
      :min-length="minLength"
    ></ConditionItem>
    <a-button
      v-show="canAdd"
      type="dashed"
      class="add-condition-item"
      @click="handleConditionAdd"
      >+</a-button
    >
  </div>
</template>

<script setup lang="ts">
  import ConditionItem from './condition-item.vue';

  const list = defineModel<any>();
  const props = defineProps<{
    path: string;
    firstOptions: any;
    secondOptions: any;
    thirdOptions: any;
    canAdd?: boolean;
    canDelete?: boolean;
    minLength?: number;
  }>();
  const handleConditionAdd = () => {
    list.value.push({
      field: '',
      condition: '',
      value: '',
    });
  };
  const handleDelete = (index: number) => {
    list.value.splice(index, 1);
  };
</script>

<style lang="less" scoped>
  .list-box {
    border: 1px solid var(--color-neutral-3);
    padding: 8px 12px 8px 60px;
  }

  .add-condition-item {
    border-radius: 2px;
    width: 87%;
    border: 1px dotted #ebebeb;
  }
</style>
