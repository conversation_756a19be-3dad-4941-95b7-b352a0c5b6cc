<template>
  <a-collapse
    :default-active-key="Object.keys(originData)"
    class="brand-collapse"
  >
    <a-collapse-item v-for="(fItem, fKey) in originData" :key="fKey">
      <template #header>
        <div style="display: flex">
          <div style="margin-right: 8px">
            {{ fItem.label }}
          </div>
          <a-switch
            @click.stop
            v-model="formData[fKey].isOpen"
            :checked-value="1"
            :unchecked-value="0"
          ></a-switch>
        </div>
      </template>
      <DynamicForm
        ref="formRef"
        :form-schema="fItem?.formSchema"
        v-model="formData[fKey]"
        class="dynamicForm-instance"
      ></DynamicForm>
    </a-collapse-item>
  </a-collapse>
</template>

<script setup lang="tsx">
  import { Message } from '@arco-design/web-vue';
  import { computed, ref } from 'vue';
  import { cloneDeep, omit } from 'lodash';
  import { brandParamsApi } from '@/api/setting/brand-params-config';
  import { useAppStore } from '@/store';

  const formRef = ref();
  const contentStyle = { width: '624px' };

  const props = defineProps<{
    record: any;
  }>();

  const formData = defineModel<any>();

  const appStore = useAppStore();
  const currentApp = computed(() => {
    const { appList } = appStore.appInfo;
    return appList.find((item) => item.appId === props.record.id) ?? {};
  });
  const originData = {
    huawei_ads: {
      label: '华为ads',
      formSchema: {
        fields: [
          getField('Client ID', 'clientId'),
          getField('Secret ID', 'secretID'),
          getField('App ID', 'appId'),
        ],
        contentStyle,
      },
    },
    huawei_agd: {
      label: '华为agd',
      formSchema: {
        fields: [
          getField('Client ID', 'clientId'),
          getField('Secret ID', 'secretID'),
        ],
      },
    },
    baidu: {
      label: '百度',
      formSchema: {
        fields: [
          getField('AccessKey', 'accessKey'),
          getField('App ID', 'appId'),
          {
            label: '私钥',
            name: 'privateKeyPath',
            type: 'file',
            fileType: 'pem',
            limitOne: true,
            required: (field, curFormData) => {
              return curFormData.isOpen === 1;
            },
            showOriginFileName: true,
            uploadButton: (
              <a-button
                type="primary"
                v-slots={{
                  icon: <icon-upload />,
                }}
              >
                上传私钥
              </a-button>
            ),
          },
        ],
      },
    },
    vivo: {
      label: 'VIVO',
      formSchema: {
        fields: [
          getField('account name', 'accountName'),
          getField('Secret key', 'secretID'),
          getField('Media ID', 'appId'),
        ],
      },
    },
    oppo: {
      label: 'OPPO',
      formSchema: {
        fields: [
          getField('client ID', 'clientId'),
          getField('client secret', 'secretID'),
          getField('App ID', 'mediaId'),
          getField('App name', 'mediaName'),
        ],
      },
    },
    xiaomi: {
      label: '小米',
      formSchema: {
        fields: [
          getField('dev ID', 'devId'),
          getField('app ID', 'appId'),
          getField('密钥', 'secretID'),
          getField('appKey', 'appKey', {
            tooltip: '用户获取IAA实时变现数据，确认可获取实时数据后再配置',
            required: false,
            visibleOn: () => {
              return ['APP', 'APK'].includes(currentApp.value?.appGroup);
            },
          }),
        ],
      },
    },
    ylh: {
      label: '优量汇',
      formSchema: {
        fields: [
          getField('开发者账号', 'appId'),
          getField('媒体ID', 'mediaId'),
          getField('媒体名称', 'mediaName'),
          getField('secret', 'secretID'),
        ],
      },
    },
    honor: {
      label: '荣耀',
      formSchema: {
        fields: [
          getField('Client ID', 'clientId'),
          getField('密钥secret', 'secretID'),
          getField('媒体ID', 'mediaId'),
        ],
      },
    },
  };

  function downloadPublicKey() {
    const url = formData.value?.baidu?.publicKeyPath;
    if (!url) {
      Message.error('公钥地址为空');
      return;
    }
    window.open(url, '_blank');
  }

  async function validator() {
    const result = formRef.value.map((item) => {
      return item.validate();
    });
    const validateRes = await Promise.all(result);
    return validateRes;
  }

  async function submit(value) {
    await validator();

    const submitData = Object.keys(cloneDeep(formData.value)).map((key) => {
      const submitObj = {
        applicationId: props.record.id,
        packageName: props.record.packageName,
        isOpen: formData.value[key].isOpen,
        manufacturer: key,
        content: {
          ...omit(formData.value[key], ['isOpen', 'id']),
        },
      };
      if (formData.value[key].id) {
        submitObj.id = formData.value[key].id;
      }
      return submitObj;
    });
    await brandParamsApi.saveConfig(submitData);
    Message.success('保存成功');
    value.dispatch({ type: 'refreshTable' });
  }

  function getField(label, name, config = {}) {
    return {
      name,
      label,
      type: 'text',
      maxLength: 100,
      required: (field, curFormData) => {
        return curFormData.isOpen === 1;
      },
      setter: (val: string) => {
        return val.trim();
      },
      ...config,
      // required: true,
    };
  }

  function reset() {
    formRef.value?.reset();
  }

  defineExpose({
    done: submit,
    reset,
  });
</script>

<style scoped lang="less">
  .brand-collapse {
    :deep(.arco-collapse-item) {
      .arco-collapse-item-header {
      }

      .arco-collapse-item-content {
        background-color: #fff;
        padding: 0 16px;

        .arco-collapse-item-content-box {
          padding: 20px 0 0;
        }
      }
    }

    .dynamicForm-instance {
      :deep(#privateKeyPath) {
        .arco-upload-list-item-name {
          white-space: normal;
        }
      }
    }
  }
</style>
