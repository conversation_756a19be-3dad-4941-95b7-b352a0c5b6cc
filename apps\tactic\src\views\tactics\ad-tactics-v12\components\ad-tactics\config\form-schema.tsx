import { ref } from 'vue';
import { get, max } from 'lodash';
import commonApi from '@/api/common';
import switchConfig from '@/views/tactics/common/switch-config';
import useAppId from '@/hooks/app-id';
import TacticName from '@/views/tactics/common/tactic-name.vue';

import TacticsConfig from '../../../../common/tactics-config.vue';
import AdSourceConfig from '../components/ad-source-config.vue';
import AbandonConfig from '../components/abandonRule/abandon-config.vue';
import ErrorCodeConfig from '../components/errorCode-config/index.vue';
import FrequencyControlConfig from '../components/frequencyControlConfig/frequency-control-config.vue';
import {
  abandonFormSchema,
  getDefaultAbandonFormData,
} from '../components/abandonRule/abandon-form';
import { getAdSourceFormSchema } from '../../ad-source/config/adSourceForm';
import { frequencyControlFormSchema } from '../components/frequencyControlConfig/frequency-control-form';
import { errorCodeConfigFormSchema } from '../components/errorCode-config/errorCode-config-form';

const appId = useAppId();
export const triggerLength = ref(0);

function getAbandonStatus(data) {
  return data.isOpenAdAbandon === 1;
}

function getDefaultRequestTacticsConfig() {
  return {
    adCount: 2,
    customRate: 0,
    adSourceList: [],
    adExposureCount: 0,
    adsArea3ExposureCount: 1,
    straightAdSourceList: [],
    isReqByAdAva: 2,
  };
}

function getDefaultAbandonConfig() {
  return {
    isOpenAdAbandon: 1,
    isAbandonWhileDownloading: 1,
    apkInstalledWaitTime: 10,
    abandonRuleList: [
      {
        id: 1,
        abandonType: 1,
        ...getDefaultAbandonFormData(),
      },
      {
        id: 2,
        abandonType: 2,
        ...getDefaultAbandonFormData(),
      },
    ],
  };
}

export const defaultAdTactic = {
  name: '默认广告策略',
  isGeneral: 1,
  requestTacticsConfig: {
    nativeAdSource: getDefaultRequestTacticsConfig(),
    biddingAdSourceList: [],
    otherAdSourceConfig: {
      rewardVideoAdCount: 1,
      interstitialMonitorCount: 1,
      adSourceList: [],
    },

    adsTemplateConfig: {
      adsTemplateList: [],
      adsTemplateRequestLimit: 50,
    },
  },
  abandonConfig: getDefaultAbandonConfig(),
  returnConfig: {
    returnTacticsRule: 'time',
    isQueryBothPool: 2,
    isDownloadPriority: 1,
    wakeUpEcpm: 0,
    isGetLowAd: 1,
  },
  frequencyControlConfig: {
    ParallelShowAdCount: 3,
    adShowDuration: 500,
    adRequestRange: [50, 100],
    singleAdRequestMaxCount: 999,
    frequencyControlList: [],
  },
  adExposureConfig: {
    exposureSort: 1,
  },
  adActivateConfig: {
    enable: 1,
    adActivateRate: 30,
    unActivateAppCount: 3,
  },
  monitorPageExistConfig: {
    enable: 2,
  },
  biddingConfig: {
    scale: [],
    failScale: undefined,
  },
  errCodeConfig: {
    enable: 2,
    errorCodeConfigList: [],
  },
  sameAdUsePopConfig: {
    enable: 2,
    adClickGapTime: 1,
    isUseSamePkgBeforeInterval: 1,
  },
};

const formSchema = {
  fields: [
    {
      label: '广告策略名称',
      name: 'name',
      type: 'custom',
      span: 8,
      component: TacticName,
      params: {
        staticPreText: 'G',
        preTextLength: 3,
      },
      required: true,
    },
    {
      label: '广告策略',
      name: 'adTacticsConfigData',
      type: 'array',
      format: TacticsConfig,
      defaultItem: defaultAdTactic,
      hideLabel: true,
      item: {
        type: 'object',
        name: '',
        label: '',
        fields: [
          {
            label: '生效地区',
            name: 'effectCity',
            type: 'cascader',
            placeholder: '请选择地区',
            multiple: true,
            maxTagCount: 10,
            span: 8,
            disabled: true,
            source: {
              data() {
                return commonApi.getLevelCity();
              },
            },
            required: true,
            visibleOn(data) {
              return data.isGeneral !== 1;
            },
          },
          {
            label: '生效时间',
            name: 'effectTime',
            type: 'timeRange',
            span: 8,
            required: true,
            disabled: true,
            visibleOn(data) {
              return data.isGeneral !== 1;
            },
          },
          // 请求策略
          {
            label: '请求策略',
            name: 'requestTacticsConfig',
            type: 'object',
            viewType: 'card',
            hideLabel: true,
            fields: [
              {
                label: '原生/信息流广告',
                name: 'nativeAdSource',
                type: 'object',
                fields: [
                  {
                    label: '原生/信息流-数据池监控广告数',
                    name: 'adCount',
                    type: 'number',
                    required: true,
                    min: 0,
                    suffix: '个',
                    span: 8,
                    tooltip:
                      '指数据池至少有该数量原生/信息流广告，超出该数量的广告将进入页面池展示',
                  },
                  {
                    label: '模板概率',
                    name: 'customRate',
                    type: 'number',
                    required: true,
                    min: 0,
                    span: 8,
                    tooltip:
                      '华为厂商,广告源中的渲染方式配置不生效，根据概率，同时必须填ads模板广告id',
                  },
                  {
                    label: '目标价广告位',
                    name: 'adSourceList',
                    type: 'array',
                    format: <AdSourceConfig isNative={true} />,
                    item: {
                      type: 'object',
                      name: '',
                      label: '',
                      fields: getAdSourceFormSchema({}).fields,
                    },
                  },
                  {
                    label: '原生/信息流-补曝光池监控广告数',
                    name: 'adExposureCount',
                    type: 'number',
                    required: true,
                    suffix: '个',
                    span: 8,
                  },
                  {
                    label: '有可用广告时广告位id是否请求',
                    name: 'isReqByAdAva',
                    ...switchConfig,
                    span: 8,
                    tooltip:
                      '小米厂商,开启时为，当数据池中有相同广告位的广告或页面池和弹窗上有正在展示的广告时，该广告位id锁住不请求',
                    rules: [{ required: true }],
                  },
                  {
                    label: 'ads模版广告-区域3补曝光池监控广告数',
                    name: 'adsArea3ExposureCount',
                    type: 'number',
                    required: true,
                    tooltip: '华为厂商',
                    suffix: '个',
                    span: 8,
                  },
                  {
                    label: '直拿直取广告位',
                    name: 'straightAdSourceList',
                    type: 'array',
                    minLength: 0,
                    tooltip:
                      '只支持小米，支持左图右文带标题A、上文下图（组图）',
                    format: (
                      <AdSourceConfig isStraight={true} isNative={false} />
                    ),
                    item: {
                      type: 'object',
                      name: '',
                      label: '',
                      fields: getAdSourceFormSchema({}).fields,
                    },
                  },
                ],
              },
              {
                label: '激励视频&插屏&横幅广告',
                name: 'otherAdSourceConfig',
                type: 'object',
                fields: [
                  {
                    label: '激励视频-数据池监控广告数',
                    name: 'rewardVideoAdCount',
                    type: 'number',
                    span: 8,
                    suffix: '个',
                    required: true,
                    tooltip:
                      '指数据池至少有该数量激励视频广告，达到该数量后，暂停队列的激励视频广告请求。小米320不生效，走直拿直取',
                  },
                  {
                    label: '插屏-数据池监控广告数',
                    name: 'interstitialMonitorCount',
                    type: 'number',
                    span: 8,
                    suffix: '个',
                    required: true,
                    tooltip:
                      '指数据池至少有该数量激励视频广告，达到该数量后，暂停队列的激励视频广告请求。小米320不生效，走直拿直取',
                  },
                  {
                    label: '广告源',
                    name: 'adSourceList',
                    hideLabel: true,
                    type: 'array',
                    format: <AdSourceConfig isVideo={true} />,
                    minLength: (data, formData, path) => {
                      const curData = get(formData, path);
                      if (
                        curData?.interstitialMonitorCount === 0 &&
                        curData?.rewardVideoAdCount === 0
                      ) {
                        return 0;
                      }
                      return 1;
                    },
                    item: {
                      type: 'object',
                      name: '',
                      label: '',
                      fields: getAdSourceFormSchema({}).fields,
                    },
                  },
                ],
              },
              {
                label: 'bidding广告位',
                name: 'biddingAdSourceList',
                type: 'array',
                minLength: 0,
                format: <AdSourceConfig isBidding={true} isNative={true} />,
                item: {
                  type: 'object',
                  name: '',
                  label: '',
                  fields: getAdSourceFormSchema({}).fields,
                },
              },
              {
                label: 'ads模板广告',
                name: 'adsTemplateConfig',
                type: 'object',
                fields: [
                  {
                    label: 'ads模板广告',
                    name: 'adsTemplateList',
                    hideLabel: true,
                    type: 'array',
                    format: (
                      <AdSourceConfig isAdsTemplate={true} isNative={true} />
                    ),
                    minLength: 0,
                    item: {
                      type: 'object',
                      name: '',
                      label: '',
                      fields: getAdSourceFormSchema({}).fields,
                    },
                  },
                  {
                    label: 'ads模板广告请求限制',
                    name: 'adsTemplateRequestLimit',
                    type: 'number',
                    span: 8,
                    suffix: '个',
                    precision: 0,
                    required: true,
                    tooltip: '用户每次进入ads模版广告达到该请求次数则停止请求',
                  },
                ],
              },
            ],
          },
          {
            label: '返回策略',
            name: 'returnConfig',
            type: 'object',
            viewType: 'card',
            hideLabel: true,
            fields: [
              {
                label: '排序逻辑',
                name: 'returnTacticsRule',
                type: 'select',
                format: 'singleSelect',
                span: 8,
                rules: [{ required: true }],
                source: {
                  data: [
                    {
                      label: '按入池时间后进先出',
                      value: 'time',
                    },
                    {
                      label: '按广告价格高价先出',
                      value: 'price',
                    },
                  ],
                },
                tooltip:
                  '指从数据池拿取广告的逻辑，支持按广告价格高价先出或按入池时间后进先出；按照入池时间后进先出只有小米厂商联盟广告生效',
              },
              {
                label: '是否同时查询数据池和页面池',
                name: 'isQueryBothPool',
                tooltip: '小米厂商',
                ...switchConfig,
                span: 4,
                rules: [{ required: true }],
              },
              {
                label: '是否下载类优先',
                name: 'isDownloadPriority',
                tooltip:
                  '关闭时按照原有逻辑,开启时优先下载类,优先于促激活、非下载类广告',
                ...switchConfig,
                span: 4,
                rules: [{ required: true }],
              },
              {
                label: '唤醒广告ecpm',
                name: 'wakeUpEcpm',
                type: 'number',
                span: 4,
                min: 0,
                suffix: '元',
                required: true,
                tooltip: '小于等于配置价格的唤醒广告不拿取',
                visibleOn: (data) => data.isDownloadPriority === 1,
              },
              {
                label: '无广告时是否拿取低价唤醒广告',
                name: 'isGetLowAd',
                ...switchConfig,
                span: 4,
                required: true,
                visibleOn: (data) => data.isDownloadPriority === 1,
              },
            ],
          },
          {
            label: '遗弃策略',
            name: 'abandonConfig',
            viewType: 'card',
            type: 'object',
            fields: [
              {
                label: '是否开启广告遗弃',
                name: 'isOpenAdAbandon',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              {
                label: '下载中广告是否遗弃',
                name: 'isAbandonWhileDownloading',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
                tooltip: '华为,荣耀厂商',
              },
              {
                label: '时间间隔',
                name: 'apkInstalledWaitTime',
                type: 'number',
                min: 0,
                span: 8,
                suffix: 's',
                required: true,
                tooltip:
                  '小米厂商默认下载中广告曝光不遗弃，此配置决定广告扫包成功后多久拿出来使用',
              },
              {
                label: '遗弃规则',
                name: 'abandonRuleList',
                hideLabel: true,
                type: 'array',
                format: <AbandonConfig />,
                visibleOn: (data) => getAbandonStatus(data),
                item: {
                  type: 'object',
                  name: '',
                  label: '',
                  fields: abandonFormSchema.fields,
                },
              },
            ],
          },
          {
            label: '频控配置',
            name: 'frequencyControlConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '并行展示广告数量',
                name: 'ParallelShowAdCount',
                type: 'number',
                suffix: '个',
                required: true,
                span: 8,
                min: 1,
              },
              {
                label: '广告展示间隔',
                name: 'adShowDuration',
                type: 'number',
                span: 8,
                suffix: 'ms',
                required: true,
              },
              {
                label: '广告请求间隔',
                name: 'adRequestRange',
                type: 'numberRange',
                span: 4,
                suffix: 'ms',
                required: true,
                rules: [
                  {
                    validator: (value, cb) => {
                      console.log('adRequestRange', value[0], value[1]);
                      if (!value[0] || !value[1]) {
                        cb('请输入广告请求间隔');
                        return;
                      }
                      cb();
                    },
                  },
                ],
              },
              {
                label: '单广告位请求次数上限',
                name: 'singleAdRequestMaxCount',
                type: 'number',
                tooltip: '小米厂商',
                span: 4,
                min: 1,
                suffix: '次',
                required: true,
              },
              {
                label: '配置',
                name: 'frequencyControlList',
                hideLabel: true,
                type: 'array',
                format: FrequencyControlConfig,
                rules: [
                  {
                    minLength: 1,
                    message: '请至少添加一个频控配置',
                  },
                ],
                item: {
                  type: 'object',
                  name: '',
                  label: '',
                  fields: frequencyControlFormSchema.fields,
                },
              },
            ],
          },
          {
            label: '广告补激活',
            name: 'adActivateConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 6,
                rules: [{ required: true }],
              },
              {
                label: '补激活概率',
                name: 'adActivateRate',
                type: 'number',
                span: 6,
                suffix: '%',
                rules: [{ required: true }],
                visibleOn: (data) => {
                  return data.enable === 1;
                },
              },
              {
                label: '下发待激活应用数量',
                name: 'unActivateAppCount',
                type: 'number',
                span: 6,
                precision: 0,
                min: 0,
                rules: [{ required: true }],
                visibleOn: (data) => {
                  return data.enable === 1;
                },
              },
            ],
          },
          {
            label: '监测页面是否存在',
            name: 'monitorPageExistConfig',
            type: 'object',
            viewType: 'card',
            tooltip: 'OPPO、VIVO、荣耀厂商',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
            ],
          },
          {
            label: 'bidding结果回传系数',
            name: 'biddingConfig',
            type: 'object',
            viewType: 'card',
            tooltip: 'OPPO、VIVO、华为厂商',
            fields: [
              {
                label: '竞胜回传浮动系数',
                name: 'scale',
                type: 'numberRange',
                max: 1,
                required: true,
                span: 6,
              },
              {
                label: '竞败回传浮动系数',
                name: 'failScale',
                type: 'number',
                required: true,
                span: 6,
              },
            ],
          },
          {
            label: '错误码管理',
            name: 'errCodeConfig',
            type: 'object',
            viewType: 'card',
            tooltip: '小米、荣耀厂商',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              {
                label: '错误码配置列表',
                name: 'errorCodeConfigList',
                hideLabel: true,
                type: 'array',
                format: <ErrorCodeConfig />,
                visibleOn: (data) => data.enable === 1,
                item: {
                  type: 'object',
                  name: '',
                  label: '',
                  fields: errorCodeConfigFormSchema.fields,
                },
              },
            ],
          },
          {
            label: '不拿取相同广告',
            name: 'sameAdUsePopConfig',
            type: 'object',
            viewType: 'card',
            tooltip: '华为、荣耀、小米厂商',
            fields: [
              {
                label: '开关',
                name: 'enable',
                ...switchConfig,
                span: 6,
                rules: [{ required: true }],
              },
              {
                label: '相同广告点击间隔',
                name: 'adClickGapTime',
                type: 'number',
                min: 0,
                span: 6,
                suffix: 's',
                required: true,
                tooltip: '小米厂商',
                visibleOn: (data) => data.enable === 1,
              },
              {
                label: '无广告时是否拿取未到配置时间的唤醒广告',
                name: 'isUseSamePkgBeforeInterval',
                ...switchConfig,
                span: 6,
                required: true,
                visibleOn: (data) => data.enable === 1,
              },
            ],
          },
        ],
      },
    },
  ],
};

export default formSchema;
