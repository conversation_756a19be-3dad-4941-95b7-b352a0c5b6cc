// eslint-disable-next-line import/no-cycle

import { cloneDeep, omit, pick } from 'lodash';
import { DPage } from '@repo/seal/src/components/dynamic/types/page';
import {
  marketingToolAdAdbComponent,
  marketingToolAdAdbComponentGroup,
} from '@/api/marketing-tool/ad-component/adb-component';
import useAppId from '@/hooks/app-id';
import { hydrateDeep } from '@/utils';
import adbComponentAuthKey from '@/constants/auth-key/marketing-tool/ad-component';
import configBase from './config-module/config-base';
import delayIntercept from './config-module/delay-intercept';
import skeletonScreen from './config-module/skeleton-screen';
import template from './config-module/template';
import {
  // getFormSchema as getTemplateFormSchema,
  getFormData as getTemplateFormData,
} from '../../../ad-template-component/adb-template/config/operation';

const appId = useAppId();

export function getFormSchema() {
  return {
    fields: [
      configBase.getFormSchema(),
      delayIntercept.getFormSchema(),
      skeletonScreen.getFormSchema(),
      template.getFormSchema(),
    ],
  };
}

export function getFormData() {
  return {
    id: undefined,
    groupId: undefined,
    ...configBase.getFormData(),
    ...delayIntercept.getFormData(),
    ...skeletonScreen.getFormData(),
    ...template.getFormData(),
  };
}

// 格式化提交数据
export function getSubmitData(data) {
  const formData = cloneDeep(data);
  formData.appId = appId.value;
  formData.template.template = formData.template.template || 0;
  Object.assign(formData, formData.configBase);
  formData.templateName = formData.createNewTemplate
    ? formData.base.name
    : undefined;
  formData.forwardBtnConfig.styleConfig.buttons?.forEach((item) => {
    item.supportAdType = [item.supportAdType];
  });
  formData.styleConfig = formData.forwardBtnConfig.styleConfig;
  formData.forwardBtnConfig = omit(formData.forwardBtnConfig, 'styleConfig');
  delete formData.configBase;
  delete formData.base;
  return formData;
}

// 设置回显数据
function getFormatFormData(data) {
  let formData: any = Object.assign(getTemplateFormData(), getFormData());
  formData = hydrateDeep(formData, data);
  formData.configBase = hydrateDeep(formData.configBase, data);
  formData.base = hydrateDeep(formData.base, data);
  formData.base.name = '';
  if (data.template.template === 0) {
    formData.template.template = '';
  }
  data.styleConfig.buttons?.forEach((item) => {
    item.supportAdType = item.supportAdType[0];
  });
  formData.forwardBtnConfig.styleConfig = data.styleConfig;
  return formData;
}

export const fetchModifyDetail = (record) => {
  return marketingToolAdAdbComponent
    .detail?.({ id: record.id, appId: appId.value })
    .then((res) => {
      res.data.id = record.id;
      res.data.groupId = record.groupId;
      res.data = getFormatFormData(res.data);
      return res;
    });
};

const operation: DPage['operation'] = {
  batchRemove: {
    auth: adbComponentAuthKey.MARKETING_TOOL_AD_COMPONENT_ADB_BATCH_DELETE,
    action: (data) => {
      return marketingToolAdAdbComponent.remove?.({
        appId: appId.value,
        ids: data,
      });
    },
  },
  copy: {
    auth: adbComponentAuthKey.MARKETING_TOOL_AD_COMPONENT_ADB_COPY,
    ui: { width: '800px' },
    action: (data) => {
      data.configBase.name = data.name;
      data = getSubmitData(data);
      return marketingToolAdAdbComponent.create?.(data);
    },
    form: {
      formSchema: {
        fields: [
          {
            label: '新增样式名称',
            name: 'name',
            type: 'text',
            placeholder: '请输入样式名称',
            required: true,
          },
          {
            label: '复制至分组',
            name: 'groupId',
            type: 'select',
            placeholder: '请选择分组',
            format: 'singleSelect',
            select: {
              allowClear: true,
              allowSearch: true,
            },
            required: true,
            source: {
              labelKey: 'name',
              valueKey: 'id',
              data: () => {
                return marketingToolAdAdbComponentGroup
                  .list?.({ appId: appId.value, elementType: 4 })
                  .then((res) => {
                    return res.data;
                  });
              },
            },
          },
          {
            label: '',
            name: '',
            type: 'alert',
            title: '说明',
            content:
              '注意不同厂商支持的广告类型不同，复制后需要调整多adb组件的广告类型。',
          },
        ],
      },
      defaultFormData: {
        name: '',
        groupId: undefined,
      },
    },
    fetchDetail(record) {
      return marketingToolAdAdbComponent
        .detail?.({ id: record.id, appId: appId.value })
        .then((res) => {
          res.data = getFormatFormData(res.data);
          res.data.id = record.id;
          res.data.groupId = record.groupId;
          res.data.name = record.name;
          return res;
        });
    },
  },
  remove: {
    auth: adbComponentAuthKey.MARKETING_TOOL_AD_COMPONENT_ADB_DELETE,
    action: (data) => {
      return marketingToolAdAdbComponent.remove?.({
        ids: [data.id],
        appId: appId.value,
      });
    },
  },
  // customTableOperation: {
  //   preview,
  // },
};

export default operation;
