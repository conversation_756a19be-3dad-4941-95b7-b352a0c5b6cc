<template>
  <section class="home-head">
    <ul class="card-list">
      <li
        class="card-item"
        v-for="(item, index) in cardList"
        :key="index"
        :style="item.style"
        @click="item.click?.()"
      >
        <div class="card-title">{{ item.title }}</div>
        <div class="card-dir">{{ item.dir }}</div>
        <SvgIcon
          v-if="item.icon"
          class="card-icon"
          :icon="item.icon"
          :style="item.iconStyle"
        />
      </li>
    </ul>
  </section>
  <LocalUpload v-model:visible="visible" />
  <BatchCreate v-model:visible="batchVisible" />
  <AiCreatePic v-model:visible="aiCreatePicVisible" @refresh="handleRefresh" />
</template>

<script setup lang="ts">
  import { ref, inject, watch } from 'vue';
  import LocalUpload from './local-upload/index.vue';
  import BatchCreate from './batch-create/index.vue';
  import { homeContext } from '../utils';
  import AiCreatePic from './ai-create-pic/index.vue';

  const activeInfo: any = inject('activeInfo');
  const emit = defineEmits(['taskRefresh']);
  const homeInstances = inject<any>(homeContext);
  const { activeKey } = activeInfo;
  const visible = ref(false);
  const batchVisible = ref(false);
  const aiCreatePicVisible = ref(false);
  const cardList = ref<any[]>([
    {
      title: '视频批量合成',
      dir: '批量修改视频元素，更快更高效',
      style: {
        background: 'linear-gradient( 180deg, #165DFF 0%, #A0CDFB 100%)',
      },
      icon: 'index-triangles',
      click: () => {
        window.open('/editor?from=create', '_blank');
      },
    },
    {
      title: '本地上传',
      dir: '批量上传本地素材，高效管理',
      style: {
        background: 'linear-gradient( 180deg, #F77234 0%, #FFCAB1 100%)',
        marginLeft: '16px',
      },
      icon: 'local-upload',
      iconStyle: {
        width: '63px',
        height: '51px',
        opacity: '0.36',
        right: 0,
        top: 0,
      },
      click: () => {
        visible.value = true;
      },
    },
    {
      title: '图片批量合成',
      dir: '批量合成图片，去水印，加元素',
      style: {
        background: 'linear-gradient( 180deg, #2BC566 0%, #98EDC5 100%)',
        marginLeft: '16px',
      },
      icon: 'pic-editor',
      iconStyle: {
        width: '63px',
        height: '51px',
        opacity: '0.36',
        right: 0,
        top: 0,
      },
      click: () => {
        window.open('/pic-editor', '_blank');
      },
    },
    {
      title: 'AI批量生图',
      dir: '批量文生图，图生图',
      style: {
        background: 'linear-gradient(180deg, #8A2BE2 0%, #D8BFD8 100%)',
        marginLeft: '16px',
      },
      icon: 'ai-pic',
      iconStyle: {
        width: '63px',
        height: '51px',
        opacity: '0.36',
        right: 0,
        top: 1,
      },
      click: () => {
        aiCreatePicVisible.value = true;
      },
    },

    {
      title: '批量导入合成',
      dir: '直接使用原始文件批量合成成品素材',
      style: {
        background: 'linear-gradient(180deg, #EB6793 0%, #FFB0C7 100%)',
        marginLeft: '16px',
      },
      icon: 'batch-create',
      iconStyle: {
        width: '63px',
        height: '51px',
        opacity: '0.36',
        right: 0,
        top: 0,
      },
      click: () => {
        batchVisible.value = true;
      },
    },
  ]);
  watch(visible, (val) => {
    if (!val && activeKey.value === 'uploadList') {
      homeInstances?.handleUploadSearch?.();
    }
  });
  watch(batchVisible, (val) => {
    if (!val && activeKey.value === 'batchList') {
      homeInstances?.handleBatchSearch?.();
    }
  });
  const handleRefresh = () => {
    emit('taskRefresh');
  };
</script>

<style scoped lang="less">
  .card-list {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
    // justify-content: space-between;
    padding: 0;
    margin: 0;

    .card-item {
      position: relative;
      width: 232px;
      height: 96px;
      background: linear-gradient(180deg, #f77234 0%, #ffcab1 100%);
      border-radius: 8px;
      color: #fff;
      padding-left: 16px;
      padding-top: 20px;
      line-height: 22px;
      cursor: no-drop;
      margin: 8px 0;

      .card-title {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-dir {
        font-weight: 500;
        font-size: 12px;
      }

      &:nth-child(1),
      &:nth-child(2),
      &:nth-child(3),
      &:nth-child(4),
      &:nth-child(5) {
        cursor: pointer;

        .card-icon {
          position: absolute;
          right: -6px;
          top: 0;
          width: 71px;
          height: 71px;
        }
      }
    }
  }
</style>
