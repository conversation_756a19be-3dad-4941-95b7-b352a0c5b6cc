import ruleAuth from '@/constants/auth-key/tool/rule-manage';
import { Message } from '@arco-design/web-vue';
import { cloneDeep, pick } from 'lodash';
import autoRuleApi from '@/api/tool/auto-rule';
import { mediaNumMap } from '@/views/tool/common';
import {
  getQualityReportColumns,
  getApkReportColumns,
  getFictionReportColumns,
} from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/utils/field-columns';
import {
  getRuleDefaultFormData,
  reportOriginMap,
  ruleTargetEnum,
} from '../constants';
import useActiveKey from '../hooks/use-active-key';
import { getFormSchema } from './form';

const { setActiveKey } = useActiveKey();
const getOperation = () => {
  return [
    {
      text: '新建规则',
      auth: ruleAuth.RULE_MANAGE_CREATE,
      props: {
        type: 'primary',
      },
      clickActionType: 'drawer',
      modal: {
        props: {
          title: '新增规则',
          width: 1200,
        },
        contentType: 'form',
        form: {
          upRender: true,
          formSchema: getFormSchema(),
        },
        getDefaultValue() {
          return getRuleDefaultFormData();
        },
        action: async (data) => {
          const { formData, refreshTable } = data;
          const submitData = {
            ...formatSubmitData(cloneDeep(formData)),
            appPkgName: window.$appInfo.packageName,
            applicationId: window.$appInfo.appId,
          };
          await autoRuleApi.create(submitData);
          Message.success('新增规则成功');
          setActiveKey(mediaNumMap.toutiao);
          refreshTable();
        },
        close: () => {
          setActiveKey(mediaNumMap.toutiao);
        },
      },
    },
    {
      text: '',
      auth: ruleAuth.RULE_MANAGE_CREATE,
      props: {
        type: 'text',
      },
      icon: 'icon-info-circle',
      tooltip:
        '生效范围：自动规则不对“自动投放、数据缺失或异常”的应用对象生效。其中数据缺失，举例：a.条件包含“过去三天”的规则，不会对当天新建广告生效，因为没有“过去三天”的数据；检查时间：受制于客观技术条件，具体规则检查并运行时间可能存在轻微延迟。在您使用此能力前，请您确保已知悉并接受因此产生的后果。',
    },
  ];
};

export function formatSubmitData(originFormData) {
  const { actionTime, ruleTarget, autoRuleCondition } = originFormData;
  if (actionTime === 2) {
    originFormData.actionTimeType = 2;
  } else {
    originFormData.actionTimeType = 1;
    originFormData.actionTimeValue = actionTime;
    originFormData = {
      ...originFormData,
      ...pick(originFormData.actionTimeRange, [
        'actionStartTime',
        'actionEndTime',
      ]),
    };
  }
  delete originFormData.actionTime;
  delete originFormData.actionTimeRange;

  const newTargetItem = originFormData.autoRuleTargetItem.filter(
    (item) => item.isCheck
  );
  // 账户数据处理
  originFormData.autoRuleTargetItem = newTargetItem.map(
    (mediaItem, mediaIndex) => {
      mediaItem.channelIds = Array.isArray(mediaItem.channelIds)
        ? mediaItem.channelIds?.join(',')
        : '';

      mediaItem.autoRuleCondition.conditionItems = formatConditionItems({
        dataSource: originFormData.dataSource,
        list: mediaItem.autoRuleCondition.conditionItems,
      });

      if (ruleTarget === ruleTargetEnum.ad) {
        // 0 指定广告 1 指定账户
        if (mediaItem.isAllTarget === 0 || mediaItem.isAllTarget === 1) {
          const data = originFormData.applicationIds.map((appIdVal) => {
            return {
              applicationId: appIdVal,
              advertiserTargets: mediaItem.mediaTarget
                .filter((fItem) => fItem?.appId === appIdVal)
                .map((mItem) => {
                  return {
                    advertiserId: mItem.value,
                    promotionIds: (mItem.children ?? []).map(
                      (childItem) => childItem.value
                    ),
                  };
                }),
            };
          });
          newTargetItem[mediaIndex].mediaTarget = data;
        } else {
          newTargetItem[mediaIndex].mediaTarget = null;
        }
      } else if (ruleTarget === ruleTargetEnum.account) {
        //   账户 - 指定账户
        if (mediaItem.isAllTarget === 0) {
          const data = originFormData.applicationIds.map((appIdVal) => {
            return {
              applicationId: appIdVal,
              advertiserTargets: mediaItem.mediaTarget
                .filter((fItem) => fItem?.appId === appIdVal)
                .map((mItem) => {
                  return {
                    advertiserId: mItem.value,
                    promotionIds: [],
                  };
                }),
            };
          });
          newTargetItem[mediaIndex].mediaTarget = data;
        } else {
          newTargetItem[mediaIndex].mediaTarget = null;
        }
      }
      return mediaItem;
    }
  );
  if (autoRuleCondition) {
    autoRuleCondition.conditionItems = formatConditionItems({
      dataSource: originFormData.dataSource,
      list: autoRuleCondition.conditionItems,
    });
  }
  originFormData.applicationIds = originFormData.applicationIds.join(',');
  return originFormData;
}

function formatConditionItems({ dataSource, list }) {
  let mapList: any = [];
  if (dataSource === reportOriginMap.quality) {
    mapList = getQualityReportColumns({
      canFilter: false,
    });
  } else if (dataSource === reportOriginMap.apk) {
    mapList = getApkReportColumns({
      canFilter: false,
    });
  } else if (dataSource === reportOriginMap.fiction) {
    mapList = getFictionReportColumns({
      canFilter: false,
    });
  }
  return list.map((conItem) => {
    const val: any = {
      ...conItem,
      columnName: mapList.find((item: any) => item.value === conItem.columnCode)
        ?.label,
    };
    delete val._rowKey;
    return val;
  });
}
export default getOperation;
