<template>
  <div>
    <div style="margin-bottom: 10px">
      <a-alert
        >注意：所选账户将自动绑定当前页面的产品（当前产品：
        {{ getCurAppName }}）</a-alert
      ></div
    >
    <a-spin :loading="loading">
      <DTableSelector
        v-model="formData.adAccountList"
        :columns="getTableColumn()"
        :up-to-date-table-data="tableData"
        value-key="advertiserId"
        label-key="advertiserName"
        :is-multiple="true"
        :options="batchConfigs"
        :select-all-fields="true"
        ref="selectorRef"
        :show-count="true"
        :custom-pagination="pagination"
        :show-filter-operation="false"
        :show-clear="true"
        :right-operation-style="{ minWidth: '250px' }"
      >
        <template #filter>
          <div style="display: flex">
            <a-form :model="form" @submit="handleSubmit" layout="inline">
              <TextField
                path="search"
                v-model="form.search"
                :field="{
                  hideLabel: true,
                  useParse: true,
                  placeholder: searchPlaceholder,
                  allowClear: true,
                  style: {
                    width: '300px',
                  },
                }"
              />
              <div style="margin-right: 10px">
                <a-button type="primary" html-type="submit">
                  <template #icon> <icon-search /></template>
                  查询
                </a-button>
                <a-button
                  @click="reset"
                  class="reset-btn"
                  style="margin-left: 10px"
                >
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </div>
              <a-form-item field="isUnpersonOnly" hide-label>
                <a-checkbox v-model="form.noOwnerOnly" @change="handleCheck"
                  >仅显示无所属人员账户
                </a-checkbox>
              </a-form-item>
            </a-form>
          </div>
        </template>
      </DTableSelector>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import useAppStore from '@/store/modules/app';
  import {
    accountGdtApi,
    accountKsApi,
    accountTtApi,
  } from '@/api/promotion/account';
  import accountCommonApi from '@/api/promotion/common';
  import accountQttApi from '@/api/promotion/account/qutoutiao';
  import { agentManageApi } from '@/api/promotion/agent-manage';
  import {
    MediaType,
    mediaPlatformMap,
  } from '@/views/promotion/hooks/authorization';
  import {
    cloneDeep,
    filter,
    includes,
    isEmpty,
    isNil,
    map,
    toLower,
  } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import { commonApi } from '@/api/common';
  import useMediaRedirectState from '@/hooks/media-redirect-state';
  import { getBatchAdvertiserNameConfig } from '@/views/promotion/account/component/authorize-account/components/batch-advertiser-name/config';
  import { mockAccountRes } from '@/views/promotion/account/component/authorize-account/mock-account';
  import TextField from '@repo/kola/src/components/dynamic/form/widgets/text-field/index.vue';
  import { getColumns } from '../constants';

  defineOptions({
    name: 'SelectAccount',
  });

  const formData = defineModel<any>({
    default: () => ({}),
  });
  const getCurAppName = computed(() => {
    return window.$appInfo?.appName;
  });
  const props = defineProps<{
    visible: boolean;
    mediaType: number;
    qttIds?: string[];
  }>();
  const route = useRoute();
  const loading = ref(false);
  const originData = ref<any[]>([]);
  const tableData = ref<any[]>([]);
  const pagination = ref({
    pageSize: 10,
    current: 1,
    total: 0,
    showPageSize: true,
    showJumper: true,
    showTotal: true,
  });

  const defaultPagination = {
    pageSize: 10,
    pageNum: 1,
  };

  const isPagination = computed(() => {
    return [MediaType.QU_TOU_TIAO].includes(props.mediaType);
  });

  const currentSelected = computed(() => {
    return formData.value.adAccountList.map((item) => item.advertiserId);
  });

  const form = ref({
    search: '',
    noOwnerOnly: false,
  });
  const searchPlaceholder = computed(() => {
    if (props.mediaType === MediaType.QU_TOU_TIAO) {
      return '请输入账户名称/账户ID搜索';
    }
    return '请输入账户名称/账户ID/主体名称搜索';
  });

  const agentOptions = ref([]);
  async function getAgentOptions() {
    const res = await agentManageApi.getList({
      medias: props.mediaType,
      companyCode: window.$appInfo.companyCode,
    });
    agentOptions.value = res.data.map((item) => {
      return {
        label: item.agentName,
        value: item.id,
      };
    });
  }
  getAgentOptions();

  const getBatchOperationConfig = ({
    type,
    formSchema,
  }: {
    type: string;
    formSchema: any;
  }) => {
    const textMap = {
      remark: '批量备注',
      agentId: '批量选择代理商',
    };
    return {
      text: textMap[type],
      props: {
        type: 'primary',
        disabled: () => {
          return formData.value.adAccountList.length === 0;
        },
      },
      clickActionType: 'modal',
      modal: {
        props: {
          title: textMap[type],
          width: 500,
        },
        contentType: 'form',
        form: {
          formSchema,
        },
        action: (record) => {
          // 更新原数据和tableData
          originData.value = map(originData.value, (obj) => {
            if (includes(currentSelected.value, obj.advertiserId)) {
              return { ...obj, [type]: record.formData[type] };
            }
            return obj;
          });
          tableData.value = originData.value.filter((item) => {
            return getSearchFlag({
              searchText: form.value.search,
              data: item,
            });
          });
          // 更新已选数据
          formData.value.adAccountList = map(
            formData.value.adAccountList,
            (obj) => {
              if (includes(currentSelected.value, obj.advertiserId)) {
                return { ...obj, [type]: record.formData[type] };
              }
              return obj;
            }
          );
        },
      },
    };
  };
  const remarkFormSchema = {
    fields: [
      {
        label: '备注',
        type: 'textarea',
        name: 'remark',
        hideLabel: true,
        required: true,
        hideAsterisk: true,
        maxLength: 30,
        showWordLimit: true,
      },
    ],
  };
  const agentFormSchema = {
    fields: [
      {
        label: '代理商',
        type: 'select',
        name: 'agentId',
        format: 'singleSelect',
        select: {
          allowClear: true,
          allowSearch: true,
          tooltip: true,
        },
        required: true,
        source: {
          data: () => {
            return agentOptions.value;
          },
        },
      },
    ],
  };
  const remarkConfig = getBatchOperationConfig({
    type: 'remark',
    formSchema: remarkFormSchema,
  });

  const agentConfig = getBatchOperationConfig({
    type: 'agentId',
    formSchema: agentFormSchema,
  });

  const batchAdvertiserNameConfig = getBatchAdvertiserNameConfig({
    formVal: formData.value,
    getOriginData: () => {
      return originData.value;
    },
    action: (data) => {
      const formVal = data.formData;
      const { list } = formVal;
      formData.value.adAccountList = list.map((item) => {
        const result = {
          ...item,
          advertiserName: item.newAdvertiserName,
          updateAccName: true,
        };
        const matchLink = getLinkIdFromName(item.newAdvertiserName);
        // 修改已选择的数据
        if (matchLink) {
          result.linkId = matchLink;
        }

        const matchTableItem = tableData.value.find(
          (tableItem) => tableItem.advertiserId === item.advertiserId
        );

        // 修改列表数据
        if (!isNil(matchTableItem)) {
          matchTableItem.advertiserName = result.newAdvertiserName;
          if (matchLink) {
            matchTableItem.linkId = matchLink;
          }
        }

        const originTableItem = originData.value.find(
          (tableItem) => tableItem.advertiserId === item.advertiserId
        );

        // 修改源数据
        if (!isNil(originTableItem)) {
          originTableItem.advertiserName = result.newAdvertiserName;
          if (matchLink) {
            originTableItem.linkId = matchLink;
          }
        }
        delete result.newAdvertiserName;
        return result;
      });
    },
  });
  const batchConfigs = computed(() => {
    const result: any = [agentConfig, remarkConfig];
    if (MediaType.TOU_TIAO === props.mediaType) {
      result.push(batchAdvertiserNameConfig);
    }
    return result;
  });
  const updateCommonFn = ({ value, row, field }) => {
    row.record[field] = value;

    formData.value.adAccountList = map(formData.value.adAccountList, (obj) => {
      if (obj.advertiserId === row.record.advertiserId) {
        return { ...obj, [field]: value };
      }
      return obj;
    });
  };

  const options = ref([]);

  async function getLinkIds() {
    await commonApi
      .getMonitorUrl({
        pkg: window.$appInfo?.packageName,
        platform: mediaPlatformMap[props.mediaType],
        filterBindAccount: true,
        applicationId: window.$appInfo?.id,
      })
      .then((res) => {
        options.value = res.data.map((val) => {
          return {
            linkType: val.linkType,
            label: `${val.linkId}-${val.channelTitle}`,
            value: val.linkId,
          };
        });
      });
  }
  getLinkIds();

  function getTableColumn() {
    const tableColumn = cloneDeep(
      getColumns({
        updateFunc: updateCommonFn,
        options: options.value,
        agentOptions: agentOptions.value,
      })
    );
    if (
      [
        MediaType.HUA_WEI,
        MediaType.VIVO,
        MediaType.QU_TOU_TIAO,
        MediaType.BAI_DU,
        MediaType.WEI_BO,
      ].includes(props.mediaType)
    ) {
      // 过滤主体名称
      return tableColumn.filter((item) => item.dataIndex !== 'corporationName');
    }
    return tableColumn;
  }

  const queryMap = {
    [MediaType.WEI_BO]: route.query?.code,
    [MediaType.BAI_DU]: route.query?.authCode,
    [MediaType.HUA_WEI]: route.query?.authorization_code,
    [MediaType.QU_TOU_TIAO]: route.query?.authorization_code,
    [MediaType.VIVO]: route.query?.code,
    [MediaType.IQIYI]: route.query?.auth_code,
  };

  const queryData = ref();
  // const getOriginData = ref();
  const originDataApi = ref();

  watch(
    () => props.visible,
    (value) => {
      if (!value) {
        tableData.value = [];
        form.value = {
          search: '',
          noOwnerOnly: false,
        };
        return;
      }
      queryData.value = route.query?.auth_code;
      const appStore = useAppStore();
      const appList = appStore.appInfo?.appList;
      const companyCode = appStore.appInfo?.curApp?.companyCode;
      const queryState: any = useMediaRedirectState().getMediaState();
      const developerAppId = queryState?.serverState ?? '';
      switch (props.mediaType) {
        case MediaType.TOU_TIAO:
          originDataApi.value = accountTtApi.originData;
          queryData.value = {
            authCode: route.query?.auth_code,
            developerAppId,
            companyCode,
          };
          break;
        case MediaType.KUAI_SHOU:
          originDataApi.value = accountKsApi.originData;
          queryData.value = {
            authCode: route.query?.auth_code,
            groupName:
              appList.find((appItem: any) => appItem.id === window.$appInfo?.id)
                ?.departmentCode ?? '',
            developerAppId,
            companyCode,
          };
          break;
        case MediaType.GDT:
          originDataApi.value = accountGdtApi.originData;
          queryData.value = {
            authCode: route.query?.authorization_code,
            developerAppId,
            companyCode,
          };
          break;
        case MediaType.QU_TOU_TIAO:
          originDataApi.value = accountQttApi.originData;
          queryData.value = {
            mediaAppIds: props.qttIds,
          };
          break;
        default:
          originDataApi.value = accountCommonApi.originData;
          queryData.value = {
            authCode: queryMap[props.mediaType],
            mediaPlatform: props.mediaType,
            companyCode,
          };
          break;
      }
      // 百度授权必填字段
      if (props.mediaType === MediaType.BAI_DU) {
        queryData.value.authUserId = route.query?.userId;
        queryData.value.developerAppId = developerAppId;
      }

      if (props.mediaType === MediaType.WEI_BO) {
        queryData.value.developerAppId = developerAppId;
      }

      // tableLoading.value = true;
      getLoadData(originDataApi.value, queryData.value);
    },
    { immediate: true }
  );

  function getLoadData(
    apiFn,
    subData,
    loadPagination = {
      pageSize: pagination.value.pageSize,
      pageNum: pagination.value.current,
    }
  ) {
    let submitData = cloneDeep(subData);
    if (isPagination.value) {
      submitData = {
        ...submitData,
        ...loadPagination,
        ownerIdExist: form.value.noOwnerOnly ? 1 : 0,
      };
    }
    loading.value = true;
    apiFn?.(submitData)
      ?.then((res) => {
        originData.value = Array.isArray(res.data)
          ? res.data
          : res.data.list ?? [];
        initValue();
      })
      .finally(() => {
        loading.value = false;
      });
    // setTimeout(() => {
    //   const res = mockAccountRes;
    //   originData.value = res.data;
    //   initValue();
    // }, 1000);
  }
  async function initValue() {
    await getLinkIds();
    originData.value = originData.value?.map((item) => {
      return {
        ...item,
        linkId: [MediaType.TOU_TIAO, MediaType.KUAI_SHOU].includes(
          props.mediaType
        )
          ? item.linkId ?? getLinkIdFromName(item.advertiserName)
          : item.linkId,
        disabled: [MediaType.TOU_TIAO, MediaType.KUAI_SHOU].includes(
          props.mediaType
        )
          ? !!(item.ownerId && item.isValid === 1)
          : item.ownerId,
      };
    });

    tableData.value = cloneDeep(originData.value);
  }

  function getLinkIdFromName(name) {
    const start =
      name.indexOf('tg_') === -1 ? name.indexOf('th_') : name.indexOf('tg_');
    if (start === -1) {
      return null;
    }
    const end = name.indexOf('-', start);
    const result = end === -1 ? name.slice(start) : name.slice(start, end);
    if (options.value.find((item: any) => item.value === result)) {
      return result;
    }
    return null;
  }

  const handleSubmit = () => {
    if (isPagination.value) {
      getLoadData(
        originDataApi.value,
        {
          mediaAppIds: props.qttIds,
          keyword: form.value.search,
        },
        defaultPagination
      );
    } else {
      tableData.value = filter(originData.value, (obj) => {
        return getSearchFlag({ searchText: form.value.search, data: obj });
      });
      initPagination();
    }

    if (form.value.noOwnerOnly) {
      handleCheck(true);
    }
  };

  function reset() {
    form.value.search = '';
    handleSubmit();
  }

  function getSearchFlag({ searchText, data }) {
    if (isEmpty(searchText)) {
      return true;
    }
    const searchArr = searchText.split(',');
    if (searchArr.length === 1) {
      return (
        includes(data.advertiserName, searchText) ||
        includes(data.corporationName, searchText) ||
        includes(data.advertiserId, searchText)
      );
    }
    if (searchArr.length > 1) {
      return (
        includes(searchArr, data.advertiserName) ||
        includes(searchArr, data.corporationName) ||
        includes(searchArr, data.advertiserId)
      );
    }
    return false;
  }
  const handleCheck = (selected: boolean) => {
    if (selected) {
      if (isPagination.value) {
        getLoadData(
          originDataApi.value,
          {
            mediaAppIds: props.qttIds,
            keyword: form.value.search,
          },
          defaultPagination
        );
      } else {
        tableData.value = filter(tableData.value, (item) => !item.ownerId);
        initPagination();
      }
    } else {
      handleSubmit();
    }
  };

  const initPagination = () => {
    pagination.value.current = 1;
    pagination.value.pageSize = 10;
    pagination.value.total = tableData.value.length;
  };

  watch(
    () => formData.value.adAccountList,
    () => {
      // 当已选账户修改且linkId不为空时，重置已选择的模板配置和回传
      if (formData.value.adAccountList.every((item) => item.linkId)) {
        formData.value.linkTemplateId = undefined;
        formData.value.ruleConfigTemplateId = undefined;
      }
    }
  );

  defineExpose({
    getLinkOptions: () => options.value,
  });
</script>
