import { FormSchema } from '@repo/seal/src/components/dynamic/types/form';
import { downloadEffectApi } from '@/api-v1/config/tactic-config/download-effect';
import auth from '@/constants/auth-key-v1/config/tactic-config/download-effect';

const ui = {
  width: '800px',
};

function getFormSchema(): FormSchema {
  return {
    fields: [
      {
        label: '广告应用包名',
        name: 'pkg',
        type: 'text',
        maxLength: 50,
        required: true,
        showWordLimit: true,
      },
      {
        label: '广告应用名称',
        name: 'name',
        type: 'text',
        maxLength: 20,
        required: true,
        showWordLimit: true,
      },
      {
        label: '目标次留率',
        name: 'targetRate',
        type: 'number',
        precision: 2,
        suffix: '%',
        required: true,
      },
      {
        label: '安装后激活时间',
        name: 'activateValidHour',
        type: 'number',
        precision: 0,
        required: true,
      },
      {
        label: 'Deeplink地址',
        name: 'deeplink',
        type: 'text',
      },
      {
        label: '关键词',
        name: 'keyword',
        type: 'textarea',
        placeholder: '用英文逗号隔开',
        required: true,
      },
    ],
  };
}

function getFormData() {
  return {
    pkg: '',
    name: '',
    deeplink: '',
    keyword: '',
    activateValidHour: 8,
  };
}

const getOperation = (brand: string) => {
  return {
    create: {
      auth: auth.DOWNLOAD_EFFECT_ADD,
      action: (data, customParams) => {
        console.log('action');
        Object.assign(data, customParams);
        return downloadEffectApi?.create({
          brand,
          ...data,
        });
      },
      form: {
        title: '创建补曝光任务',
        formSchema: getFormSchema(),
        defaultFormData: getFormData(),
      },
      ui,
    },
    modify: {
      auth: auth.DOWNLOAD_EFFECT_MODIFY,
      action: (data) => {
        return downloadEffectApi?.modify({
          ...data,
          brand,
        });
      },
      form: {
        title: '编辑补曝光任务',
        formSchema: getFormSchema(),
      },
      ui,
      fetchDetail(record) {
        return downloadEffectApi?.detail({
          id: record.id,
          brand,
        });
      },
    },
    remove: {
      auth: auth.DOWNLOAD_EFFECT_DELETE,
      action: (data) => {
        return downloadEffectApi?.remove({
          ids: [data.id],
          brand,
        });
      },
    },
    batchUpload: {
      auth: auth.DOWNLOAD_EFFECT_BATCH_ADD,
      templateName: '优化效果设置.xlsx',
      action: (formData) => {
        formData.append('brand', brand);
        return downloadEffectApi?.batchUpload?.(formData);
      },
    },
    batchRemove: {
      auth: auth.DOWNLOAD_EFFECT_BATCH_DELETE,
      action: (data) => {
        return downloadEffectApi?.remove?.({
          ids: data,
          brand,
        });
      },
    },
  };
};

export default getOperation;
