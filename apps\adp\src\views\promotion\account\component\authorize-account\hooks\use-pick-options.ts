import { ref } from 'vue';
import { agentManageApi } from '@/api/promotion/agent-manage';
import { commonApi } from '@/api/common';
import { mediaPlatformMap } from '@/views/promotion/hooks/authorization';

function usePickOptions() {
  const agentOptions = ref<any>([]);
  const linkOptions = ref<any>([]);
  async function fetchAgentOptions(params) {
    const res = await agentManageApi.getList(params);
    agentOptions.value = res.data.map((item) => {
      return {
        label: item.agentName,
        value: item.id,
      };
    });
  }
  async function fetchLinkOptions(params) {
    await commonApi.getMonitorUrl(params).then((res) => {
      linkOptions.value = res.data.map((val) => {
        return {
          linkType: val.linkType,
          label: `${val.linkId}-${val.channelTitle}`,
          value: val.linkId,
        };
      });
    });
  }

  return {
    agentOptions,
    linkOptions,
    fetchAgentOptions,
    fetchLinkOptions,
  };
}

export default usePickOptions;
