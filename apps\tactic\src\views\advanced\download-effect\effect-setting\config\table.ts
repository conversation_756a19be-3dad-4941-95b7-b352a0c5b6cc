import { isNil, omit } from 'lodash';

import { DTable } from '@repo/seal/src/components/dynamic/types/table';
import downloadEffectApi from '@/api/advanced/download-effect';

import downloadEffectAuth from '@/constants/auth-key/advanced/download-effect';

import useAppId from '@/hooks/app-id';
import OverallRate from '../components/overall-rate.vue';

const appId = useAppId();

const getTable = (brand: string) => {
  return {
    load: {
      action: async (filter, pagination) => {
        const formattedFilter = omit(
          filter,
          Object.keys(filter).filter(
            (key) => isNil(filter[key]) || filter[key] === ''
          )
        );

        return downloadEffectApi.effect.list?.({
          appId: appId.value,
          brand,
          ...formattedFilter,
          ...pagination,
        });
      },
    },
    columns: [
      {
        title: '序号',
        dataIndex: 'id',
      },
      {
        title: '广告应用名称',
        dataIndex: 'name',
      },
      {
        title: '广告应用包名',
        dataIndex: 'pkg',
      },

      {
        title: '大盘次留率',
        dataIndex: 'overallRate',
        customRender: {
          type: OverallRate,
        },
      },
      {
        title: '目标次留率',
        dataIndex: 'targetRate',
      },
      {
        title: '激活有效期',
        dataIndex: 'activateValidHour',
      },
      {
        title: 'Deeplink地址',
        dataIndex: 'deeplink',
      },
      {
        title: '关键词',
        dataIndex: 'keyword',
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        width: 156,
      },
      {
        title: '更新人',
        dataIndex: 'updatedUname',
      },
      {
        title: '是否启用',
        dataIndex: 'isOpen',
        customRender: {
          type: 'switch',
          props: {
            auth: downloadEffectAuth.ADVANCED_DOWNLOAD_EFFECT_EDIT,
            action(data, record) {
              return downloadEffectApi.effect.toggleStatus?.({
                appId: appId.value,
                id: record.id,
                isOpen: data,
              });
            },
          },
        },
      },
    ],
  };
};

export default getTable;
