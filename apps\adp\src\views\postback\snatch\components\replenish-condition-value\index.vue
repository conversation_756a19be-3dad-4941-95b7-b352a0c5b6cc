<template>
  <div class="condition-value">
    <template v-if="isRange">
      <a-form-item
        hide-label
        :rules="[
          { required: true, message: '请输入最小值' },
          { validator: validateMinNumber },
        ]"
        :style="{ width: '160px' }"
        :validate-trigger="validateTrigger"
        :field="basePath + '.value'"
      >
        <a-input-number
          v-model="valueNumber"
          v-bind="numberProps"
          :precision="0"
          placeholder="请输入最小值"
        >
          <template #suffix v-if="currentSuffix">
            {{ currentSuffix }}
          </template>
        </a-input-number>
      </a-form-item>
      <div style="width: 20px; text-align: center; font-size: 20px">-</div>
      <a-form-item
        hide-label
        :rules="[
          { required: true, message: '请输入最大值' },
          { validator: validateMaxNumber },
        ]"
        :style="{ width: '160px' }"
        :validate-trigger="validateTrigger"
        :field="basePath + '.maxValue'"
      >
        <a-input-number
          v-model="maxValue"
          v-bind="numberProps"
          :precision="0"
          placeholder="请输入最大值"
        >
          <template #suffix v-if="currentSuffix">
            {{ currentSuffix }}
          </template>
        </a-input-number>
      </a-form-item>
    </template>

    <template v-else>
      <a-form-item
        v-if="isInputNumber"
        hide-label
        :rules="[{ required: true, message: '请输入值' }]"
        :style="{
          width: field === 'time' ? '248px' : '160px',
        }"
        :validate-trigger="validateTrigger"
        :field="basePath + '.value'"
        :disabled="disabled"
      >
        <a-input-number
          v-model="valueNumber"
          v-bind="numberProps"
          :precision="0"
          placeholder="请输入值"
        >
          <template #suffix v-if="currentSuffix">
            {{ currentSuffix }}
          </template>
        </a-input-number>
      </a-form-item>
      <a-form-item
        v-else-if="isInput"
        hide-label
        :rules="[{ required: true, message: '请输入值' }]"
        :style="{
          width: field === 'time' ? '248px' : '160px',
        }"
        :validate-trigger="validateTrigger"
        :field="basePath + '.value'"
      >
        <a-textarea
          v-if="isInclude"
          v-model="valueNumber"
          v-bind="numberProps"
          @paste="onPaste"
          placeholder="请输入值"
          @change="handleChange"
          @keydown.enter.prevent
        >
        </a-textarea>
        <a-input
          v-else
          v-model="valueNumber"
          v-bind="numberProps"
          @paste="onPaste"
          placeholder="请输入值"
          @change="handleChange"
          style="min-height: 32px"
        >
        </a-input>
      </a-form-item>
      <a-form-item
        v-else
        hide-label
        :rules="[{ required: true, message: '请输入值' }]"
        :style="{
          width: field === 'time' ? '248px' : '160px',
        }"
        :validate-trigger="validateTrigger"
        :field="basePath + '.value'"
      >
        <a-select v-model="valueNumber" :options="timeList"></a-select>
      </a-form-item>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { isFunction, isNil } from 'lodash';
  import {
    operatorEnum,
    timeList,
    inputNumberCodes,
    inputCodes,
  } from '../../constants';

  const modelValue = defineModel<any>();
  type numberType = number | undefined;
  const maxValue = defineModel<any>('maxValue');
  const valueNumber = defineModel<any>('value');
  const validateTrigger = ['change', 'blur'];
  const props = defineProps({
    field: String,
    op: String,
    rangePath: String,
    basePath: String,
    suffixOptions: Array,
    getNumProps: Function,
    disabled: Boolean,
  });
  const numberProps = computed(() => {
    const config = {
      max: undefined,
      min: 0,
      precision: 2,
    };
    if (isFunction(props.getNumProps)) {
      const getConfig = props.getNumProps(props.field) ?? {};
      Object.assign(config, getConfig);
    }
    return {
      'placeholder': '请输入值',
      'model-event': 'input',
      ...config,
    };
  });
  const currentSuffix = computed(() => {
    return (
      props.suffixOptions?.find((item: any) => item.value === props.field)
        ?.suffix ?? ''
    );
  });

  const isRange = computed(() => {
    return props.op === operatorEnum.between;
  });

  const isInputNumber = computed(() => {
    return inputNumberCodes.includes(props.field);
  });

  const isInput = computed(() => {
    return inputCodes.includes(props.field) || !props.field;
  });

  const isInclude = computed(() => {
    return [operatorEnum.in, operatorEnum.notin].includes(props.op);
  });

  function validateMinNumber(val, callback) {
    if (isRange.value) {
      if (!isNil(valueNumber.value) && !isNil(maxValue.value)) {
        if (valueNumber.value >= maxValue.value) {
          callback('最小值不能大于等于最大值');
          return;
        }
      }
    }
    callback();
  }
  function validateMaxNumber(val, callback) {
    if (isRange.value) {
      if (!isNil(valueNumber.value) && !isNil(maxValue.value)) {
        if (maxValue.value <= valueNumber.value) {
          callback('最大值不能小于等于最小值');
          return;
        }
      }
    }
    callback();
  }

  function onPaste(event) {
    const paste = event.clipboardData.getData('text');
    const result = paste
      .split('\n')
      .filter((word) => !!word.trim())
      .map((word) => word.replace(/^[\r\n]+|[\r\n]+$/g, ''));

    event.preventDefault();

    valueNumber.value = [operatorEnum.equal, operatorEnum.neq].includes(
      props.op
    )
      ? result[0]
      : result.join(',');
  }

  function handleChange(val) {
    if ([operatorEnum.equal, operatorEnum.neq].includes(props.op)) {
      const valueList = val.split(',');
      valueNumber.value = valueList[0];
    }
  }
</script>

<style scoped lang="less">
  .condition-value {
    display: flex;
    gap: 16px;
  }
</style>
