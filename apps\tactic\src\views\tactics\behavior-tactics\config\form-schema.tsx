import { get, set, chain, debounce, uniqBy, isNil, cloneDeep } from 'lodash';
import { Message } from '@arco-design/web-vue';
import commonApi from '@/api/common';
import useAppId from '@/hooks/app-id';
import dict from '@/api/dict';
import { useAppStore } from '@/store';
import TacticName from '@/views/tactics/common/tactic-name.vue';
import { getAdGetConfigFormSchema } from '../components/ad-get-config/ad-get-config-form';

import TacticsConfig from '../../common/tactics-config.vue';
import triggerTimeConfig from '../components/trigger-time-config.vue';
import switchConfig from '../../common/switch-config';
import adGetConfig from '../components/ad-get-config/index.vue';

import { TRIGGER_ACTION_MAP } from '../constants';

const isEnable = (data: any) => data.enable === 1;
const appId = useAppId();

const fetchTriggerTimeSource: any = async () => {
  const res = await dict.triggerTime({
    appId: appId.value,
  });
  return res;
};

const fetchPassiveTriggerSource = async () => {
  const res = await dict.triggerTime({
    appId: appId.value,
    triggerType: 2,
  });
  return res;
};

const fetchSceneSource = async (triggerCodes: string[]) => {
  const res = await Promise.all([
    fetchPassiveTriggerSource(),
    fetchTriggerTimeSource(),
  ]).then(([passiveRes, triggerRes]) => {
    const allRes = [...passiveRes, ...triggerRes];
    return uniqBy(
      allRes.filter((item) => {
        return triggerCodes.includes(item.value);
      }),
      'value'
    );
  });
  return res;
};

function getTriggerOptions({ path, formData }) {
  const currentPath = path.split('.')?.[0];
  if (!currentPath) {
    return [];
  }

  const { triggerTimingConfig } = get(formData, currentPath, []);
  if (triggerTimingConfig.length <= 0) {
    return [];
  }
  const triggerCodes = chain(triggerTimingConfig)
    .map((item) => {
      return item.triggerTimingData.map(
        (triggerChild) => triggerChild.triggerTiming
      );
    })
    .filter((item) => !!item)
    .flattenDeep()
    .uniq()
    .value();
  if (triggerCodes.length <= 0) {
    return [];
  }
  return triggerCodes;
}

// 主动触发和被动触发 获取所有触发时机
function getTriggerTimeOption({ path, formData }) {
  const currentPath = path.split('.')?.[0];
  if (!currentPath) {
    return [];
  }

  const curScene =
    cloneDeep(get(formData, currentPath, []).adClickMistakeConfig?.scene) ?? [];

  const { triggerTimingConfig, passiveTriggerTimingConfig } = get(
    formData,
    currentPath,
    []
  );
  const result: any = [];
  triggerTimingConfig?.forEach((item) => {
    item.triggerTimingData.forEach((mItem) => {
      if (
        curScene.includes(mItem.triggerTiming) &&
        !isNil(mItem.triggerAdComponent)
      )
        result.push(mItem.triggerAdComponent);
    });
  });
  passiveTriggerTimingConfig?.forEach((item) => {
    if (
      curScene.includes(item.triggerTiming) &&
      !isNil(item.triggerAdComponent)
    ) {
      result.push(item.triggerAdComponent);
    }
  });
  return result;
}

function getPassiveTriggerOptions({ path, formData }, type?: string) {
  const currentPath = path.split('.')?.[0];
  if (!currentPath) {
    return [];
  }

  const { passiveTriggerTimingConfig } = get(formData, currentPath, []);
  if (passiveTriggerTimingConfig.length <= 0) {
    return [];
  }

  const passiveTriggerCodes = chain(passiveTriggerTimingConfig)
    .map((item) => {
      if (type) {
        // 适用场景判断被动触发时机为触发视频弹窗和触发动作为触发广告组件
        return item.triggerAction === 1 ? item.triggerTiming : undefined;
      }
      return item.triggerTiming;
    })
    .filter((item) => !!item)
    .uniq()
    .value();

  if (passiveTriggerCodes.length <= 0) {
    return [];
  }

  return passiveTriggerCodes;
}

function getPassiveAdbField(type, mode?: string) {
  const label =
    type === 'trigger'
      ? `触发${mode === 'horizontal' ? '横' : '竖'}版adb广告组件`
      : `关闭${mode === 'horizontal' ? '横' : '竖'}版adb广告组件`;

  const name = mode ? 'triggerHorizontalAdbComponent' : 'triggerAdbComponent';
  const required = mode !== 'horizontal';
  return {
    label,
    name,
    type: 'select',
    format: 'singleSelect',
    select: {
      allowSearch: true,
      allowClear: true,
      searchByValueAndLabel: true,
    },
    required,
    visibleOn: (data) => {
      const showList = type === 'trigger' ? [1, 6] : [3];
      return data.triggerTiming && showList.includes(data.triggerAction);
    },
    span: 6,

    source: {
      data: () => {
        return dict.adComponent({
          appId: appId.value,
          elementType: 4,
        });
      },
    },
  };
}

export const defaultBehaviorTactic = {
  name: '默认行为策略',
  isGeneral: 1,
  triggerTimingConfig: [],
  passiveTriggerTimingConfig: [
    {
      triggerTiming: undefined,
      triggerAction: undefined,
      triggerAdComponent: undefined,
      jumpPage: 1,
      retainEffectOpt: 1,
      effectDuration: 2000,
      pickupOutDuration: 5000,
    },
  ],
  pageRouteConfig: [{}],
  adGetXiaomiConfig: {
    adGetXiaomiConfigList: [
      {
        adType: 'native',
        adGetIntervalDuration: 100,
        adGetTimeoutDuration: 5000,
      },
      {
        adType: 'rewardVideo',
        adGetIntervalDuration: 100,
        adGetTimeoutDuration: 5000,
      },
      {
        adType: 'interstitial',
        adGetIntervalDuration: 100,
        adGetTimeoutDuration: 5000,
      },
    ],
  },
  adGetConfig: {
    adPositiveTimeoutDuration: 2000,
    adNegativeTimeoutDuration: 2000,
    adGetDownloading: 1,
    downloadAdDuration: 10,
    adGetConfigList: [
      {
        adType: 'positive_native',
        adGetIntervalDuration: 100,
        adGetTimeoutDuration: 5000,
      },
      {
        adType: 'negative_native',
        adGetIntervalDuration: 100,
        adGetTimeoutDuration: 5000,
      },
      {
        adType: 'rewardVideo',
        adGetIntervalDuration: 100,
        adGetTimeoutDuration: 5000,
      },
      {
        adType: 'interstitial',
        adGetIntervalDuration: 100,
        adGetTimeoutDuration: 5000,
      },
    ],
  },
  skeletonScreenConfig: {
    enable: 2,
    customStyle: '',
    skeletonScreenOpaque: 80,
    displayTime: 0,
  },
  novelContentConfig: [],
  primitiveOpenScreenConfig: {
    enable: 2,
    splashScene: [1],
    startupCount: 3,
    switchCount: 3,
    closeCount: 3000,
    backCount: 0,
    shakeErrorConfig: 1,
    fullScreenError: 2,
    skipError: 2,
    bannerErrorStyle: 1,
    bannerCount: 0,
  },
  bannerAdConfig: {
    enable: 2,
  },
  rewardVideoConfig: {
    frequencyTriggerEnable: 2,
    frequency: {
      count: 4,
      duration: 20,
    },
    triggerRatioByFrequency: 100,
    priceTriggerEnable: 2,
    triggerRatioByPrice: 100,
    behaviorTriggerEnable: 2,
    unUserBehaviorDuration: 5,
    effectTimes: 99,
  },
  wakeupConfig: {
    enable: 2,
    wakeUpScene: 1,
    wakeUpCount: 3,
    wakeUpDelay: 2000,
    brand: 1,
    wakeUpType: ['2'],
    actionToastText: '活动推荐成功，即将进行下一次推荐',
    wakeToastText: '活动推荐成功，即将进行下一次推荐',
    readToastText: '小说推荐成功，即将进行下一次推荐',
    skitToastText: '短剧推荐成功，即将进行下一次推荐',
    cancelbtn: 2,
  },
  adClickMistakeConfig: {
    enable: 2,
    errorType: [1],
    blackScreenTouch: 2,
    shakeSensitivity: 2,
    countDownTime: 2000,
    fullScreenAdbRatio: 0,
  },
  adbButtonConfig: {
    enable: 2,
    adbRatio: 100,
    tempClickPercent: 100,
    mode: 1,
  },
  templateAdConfig: {
    tempStyleType: 2,
  },
  adCoverConfig: {
    enable: 2,
  },
  jumpAppConfig: {
    enable: 2,
    jumpAppLimitCount: 3,
    jumpAppRequestCount: 3,
    jumpAppCountDown: 3,
    jumpAppSingleTimeLimitCount: 3,
    videoCountDown: 3,
  },
  startBackPlayConfig: {
    startPlaySwitch: 2,
    startType: 1,
    backPlaySwitch: 2,
    backType: 1,
  },
  complaintEntryConfig: {
    complaintIcon: 1,
    complaintIconTop: 1,
  },
  dualAdTriggerConfig: {
    enable: 2,
    ratio: 100,
  },
  templateAdClickConfig: {
    continueClicks: 2,
    continueTime: 100,
  },
  noAdPopShowCountConfig: {
    enable: 2,
    noAdPopCount: 3,
  },
  backConfig: {
    time: 0,
    backCount: 99,
  },
  activePageConfig: {
    actionID: '',
    autoJump: 2,
  },
  customParamsConfig: {},
  clickManyAdConfig: {
    enable: 2,
    ecpmRange: [],
  },
  hxTypeAdConfig: {
    adcRatio: 0,
  },
};
// 清除依赖的字段
export const clearRelyForm = ({ path, formData }) => {
  const currentPath = path?.split('.')?.[0];
  if (currentPath) {
    // 清除 活动推荐 适用场景
    // 清除 广告点击误触 适用场景
    set(formData, `${currentPath}.wakeupConfig.scene`, []);
    set(formData, `${currentPath}.adClickMistakeConfig.scene`, []);
  }
};
const appStore = useAppStore();

const formSchema = {
  fields: [
    {
      label: '行为策略名称',
      name: 'name',
      type: 'custom',
      span: 8,
      component: TacticName,
      params: {
        staticPreText: 'X',
        preTextLength: 3,
      },
      required: true,
    },
    {
      label: '行为策略',
      name: 'behaviorTacticsConfigData',
      type: 'array',
      format: TacticsConfig,
      defaultItem: defaultBehaviorTactic,
      hideLabel: true,
      item: {
        type: 'object',
        name: '',
        label: '',
        fields: [
          {
            label: '生效地区',
            name: 'effectCity',
            type: 'cascader',
            placeholder: '请选择地区',
            multiple: true,
            maxTagCount: 10,
            span: 8,
            disabled: true,
            source: {
              data() {
                return commonApi.getLevelCity();
              },
            },
            required: true,
            visibleOn(data) {
              return data.isGeneral !== 1;
            },
          },
          {
            label: '生效时间',
            name: 'effectTime',
            type: 'timeRange',
            span: 8,
            required: true,
            disabled: true,
            visibleOn(data) {
              return data.isGeneral !== 1;
            },
          },
          // 触发时机配置
          {
            label: '触发时机配置',
            name: 'triggerTimingConfig',
            type: 'array',
            viewType: 'card',
            format: triggerTimeConfig,
            defaultItem: {
              templateAdPopupEnable: 2,
              adsTemplateAdPopupEnable: 2,
              triggerTimingData: [
                {
                  jumpPage: 1,
                  retainEffectOpt: 1,
                },
              ],
            },
            hideLabel: true,
            item: {
              label: '',
              name: '',
              hideLabel: true,
              type: 'object',
              fields: [
                {
                  label: '是否使用ads模版广告组件',
                  ...switchConfig,
                  name: 'adsTemplateAdPopupEnable',
                  span: 8,
                  required: true,
                  value: 2,
                },
                {
                  label: '触发时机',
                  name: 'triggerTimingData',
                  defaultItem: {
                    jumpPage: 1,
                    retainEffectOpt: 1,
                  },
                  type: 'array',
                  hideLabel: true,
                  // eslint-disable-next-line func-names
                  onChange: debounce(function (value, formData, path) {
                    clearRelyForm({ path, formData: formData.value });
                  }, 100),
                  addDisabled: (data, formData, path) => {
                    const triggerTimingPathList = path.split('.');
                    const triggerItemIndex = triggerTimingPathList[2]
                      ?.split('[')[1]
                      .split(']')[0];
                    const triggerTimingPath = triggerTimingPathList
                      .splice(0, 2)
                      .join('.');
                    const triggerData = get(formData, triggerTimingPath);

                    if (triggerData.pageCode === 'pages_video') {
                      return triggerItemIndex < 1;
                    }
                    return false;
                  },
                  deleteDisabled: (data, formData, path) => {
                    const triggerTimingPathList = path.split('.');
                    const triggerItemIndex = triggerTimingPathList[2]
                      ?.split('[')[1]
                      .split(']')[0];
                    const triggerTimingPath = triggerTimingPathList
                      .splice(0, 2)
                      .join('.');
                    const triggerData = get(formData, triggerTimingPath);

                    if (triggerData.pageCode === 'pages_video') {
                      return triggerItemIndex < 2;
                    }
                    return triggerData.triggerTimingData.length <= 1;
                  },
                  item: {
                    type: 'object',
                    name: '',
                    label: '',
                    fields: [
                      {
                        label: '触发时机',
                        name: 'triggerTiming',
                        type: 'select',
                        format: 'singleSelect',
                        span: 6,
                        source: {
                          data: async (field, formData, path) => {
                            const triggerTimingPathList = path.split('.');
                            const triggerTimingPath = triggerTimingPathList
                              .splice(0, 2)
                              .join('.');

                            const operationIndex = path.slice(
                              Number(path.lastIndexOf('[')) + 1,
                              -1
                            );

                            const triggerTimingItem = get(
                              formData,
                              triggerTimingPath
                            );

                            const data = await fetchTriggerTimeSource();

                            if (
                              triggerTimingItem.pageCode === 'pages_video' &&
                              operationIndex <= 1
                            ) {
                              return data;
                            }
                            return data.filter(
                              (item) =>
                                !['actionRisk', 'actionReward'].includes(
                                  item.value
                                )
                            );
                          },
                        },
                        select: {
                          allowSearch: true,
                          allowClear: true,
                          disabled: (data, formData, path) => {
                            const triggerTimingPathList = path.split('.');
                            const triggerTimingPath = triggerTimingPathList
                              .splice(0, 2)
                              .join('.');
                            const triggerData = get(
                              formData,
                              triggerTimingPath
                            );
                            const operationIndex = path.slice(
                              Number(path.lastIndexOf('[')) + 1,
                              -1
                            );
                            if (
                              triggerData.pageCode === 'pages_video' &&
                              operationIndex <= 1
                            ) {
                              return true;
                            }
                            return false;
                          },
                        },
                        tooltip:
                          '满足埋点上报或业务方自定义的时机，触发广告组件',
                        required: true,
                        onChange(data, formData, path) {
                          clearRelyForm({ path, formData: formData.value });
                        },
                      },
                      {
                        label: '关闭竖版adb广告组件',
                        name: 'closeAdbComponent',
                        type: 'select',
                        format: 'singleSelect',
                        select: {
                          allowSearch: true,
                          allowClear: true,
                          searchByValueAndLabel: true,
                        },
                        span: 6,
                        source: {
                          data: () => {
                            return dict.adComponent({
                              appId: appId.value,
                              elementType: 4,
                            });
                          },
                        },
                        visibleOn: (data) => {
                          return data.triggerTiming === 'POPUP_CLOSE';
                        },
                        required: true,
                      },
                      {
                        label: '触发竖版adb广告组件',
                        name: 'triggerAdbComponent',
                        type: 'select',
                        format: 'singleSelect',
                        select: {
                          allowSearch: true,
                          allowClear: true,

                          searchByValueAndLabel: true,
                        },
                        span: 6,

                        source: {
                          data: () => {
                            return dict.adComponent({
                              appId: appId.value,
                              elementType: 4,
                            });
                          },
                        },
                        required: true,
                      },
                      {
                        label: '华为广告不跳转',
                        name: 'jumpPage',
                        type: 'switch',
                        span: 3,
                        tooltip:
                          '开启后，华为下载类广告，用户点击广告后，停留在应用内，不跳转落地页',
                      },
                      {
                        label: '留存效果优化',
                        name: 'retainEffectOpt',
                        type: 'switch',
                        span: 3,
                        tooltip:
                          '该能力为创新能力，启用后可提升广告应用留存效果',
                      },
                      {
                        span: 6,
                        startNewRow: true,
                      },
                      {
                        label: '关闭横版adb广告组件',
                        name: 'closeHorizontalAdbComponent',
                        type: 'select',
                        format: 'singleSelect',
                        select: {
                          allowSearch: true,
                          allowClear: true,

                          searchByValueAndLabel: true,
                        },
                        span: 6,
                        source: {
                          data: () => {
                            return dict.adComponent({
                              appId: appId.value,
                              elementType: 4,
                            });
                          },
                        },
                        visibleOn: (data) => {
                          return data.triggerTiming === 'POPUP_CLOSE';
                        },
                      },
                      {
                        label: '触发横版adb广告组件',
                        name: 'triggerHorizontalAdbComponent',
                        type: 'select',
                        format: 'singleSelect',
                        select: {
                          allowSearch: true,
                          allowClear: true,
                          searchByValueAndLabel: true,
                        },
                        span: 6,
                        source: {
                          data: () => {
                            return dict.adComponent({
                              appId: appId.value,
                              elementType: 4,
                            });
                          },
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          {
            label: '被动触发时机配置',
            name: 'passiveTriggerTimingConfig',
            type: 'array',
            viewType: 'card',
            hideLabel: true,
            minLength: 0,
            format: 'list',
            defaultItem: {
              jumpPage: 1,
              retainEffectOpt: 1,
              effectDuration: 2000,
            },
            item: {
              type: 'object',
              name: '',
              label: '',
              fields: [
                {
                  label: '被动触发时机',
                  name: 'triggerTiming',
                  type: 'select',
                  format: 'singleSelect',
                  span: 6,
                  select: {
                    allowSearch: true,
                    allowClear: true,
                  },
                  source: {
                    data: () => {
                      return fetchPassiveTriggerSource();
                    },
                  },
                },
                {
                  label: '触发动作',
                  name: 'triggerAction',
                  type: 'select',
                  format: 'singleSelect',
                  span: 6,
                  select: {
                    allowSearch: true,
                    allowClear: true,
                  },
                  source: {
                    data: TRIGGER_ACTION_MAP,
                  },
                },
                {
                  label: '拿取超时时长',
                  name: 'pickupOutDuration',
                  type: 'number',
                  suffix: 'ms',
                  span: 6,
                  visibleOn: (data) => {
                    return data.triggerTiming && data.triggerAction === 2;
                  },
                },
                {
                  label: 'A拉B失败广告组件',
                  name: 'triggerAdComponent',
                  type: 'select',
                  format: 'singleSelect',
                  span: 6,
                  visibleOn: (data) => {
                    return data.triggerTiming && data.triggerAction === 3;
                  },
                  select: {
                    allowClear: true,
                    allowSearch: true,
                  },
                  source: {
                    data: () => {
                      return dict.adComponent({ appId: appId.value });
                    },
                  },
                },
                {
                  label: '触发广告组件',
                  name: 'triggerAdComponent',
                  type: 'select',
                  format: 'singleSelect',
                  tooltip: '触发广告组件时，显示配置的广告组件组',
                  select: {
                    allowSearch: true,
                    allowClear: true,
                    searchByValueAndLabel: true,
                  },
                  visibleOn: (data) => {
                    return (
                      data.triggerTiming && [1, 6].includes(data.triggerAction)
                    );
                  },
                  span: 6,
                  source: {
                    data: () => {
                      return dict.adComponent({ appId: appId.value });
                    },
                  },
                },
                {
                  label: '拉起B产品链接',
                  name: 'jumpAppUrl',
                  type: 'text',
                  span: 6,
                  visibleOn: (data) => {
                    return data.triggerTiming && data.triggerAction === 3;
                  },
                },
                {
                  label: '华为广告不跳转',
                  name: 'jumpPage',
                  type: 'switch',
                  span: 3,
                  visibleOn: (data) => {
                    return (
                      data.triggerTiming && [1, 5].includes(data.triggerAction)
                    );
                  },
                  tooltip:
                    '开启后，华为下载类广告，用户点击广告后，停留在应用内，不跳转落地页',
                },
                {
                  label: '留存效果优化',
                  name: 'retainEffectOpt',
                  type: 'switch',
                  visibleOn: (data) => {
                    return (
                      data.triggerTiming && [1, 5].includes(data.triggerAction)
                    );
                  },
                  span: 3,
                  tooltip: '该能力为创新能力，启用后可提升广告应用留存效果',
                },
                {
                  label: '生效时长',
                  name: 'effectDuration',
                  type: 'number',
                  suffix: 'ms',
                  placeholder: '请输入生效时长',
                  tooltip: '华为厂商',
                  span: 6,
                },
                getPassiveAdbField('trigger'),
                getPassiveAdbField('trigger', 'horizontal'),
                getPassiveAdbField('close'),
                getPassiveAdbField('close', 'horizontal'),
              ],
            },
            rules: [
              {
                validator(value, callback) {
                  value.forEach((item) => {
                    if (item.triggerAction === 2 && !item?.pickupOutDuration) {
                      callback('请完善被动触发时机');
                    }
                  });
                },
              },
            ],
          },

          // 页面路由配置
          {
            label: '页面路由',
            name: 'pageRouteConfig',
            type: 'array',
            viewType: 'card',
            format: 'table',
            hideLabel: true,
            item: {
              type: 'object',
              name: '',
              label: '',
              fields: [
                {
                  name: 'pageCode',
                  label: '页面',
                  hideLabel: true,
                  type: 'select',
                  format: 'singleSelect',
                  select: {
                    allowSearch: true,
                    allowClear: true,
                  },
                  source: {
                    data() {
                      return dict.page({ appId: appId.value });
                    },
                  },
                  required: true,
                },
              ],
            },
          },
          // 广告拿取配置
          {
            label: '广告拿取配置-v11',
            name: 'adGetXiaomiConfig', // v11使用
            type: 'object',
            hideLabel: true,
            viewType: 'card',
            tooltip: '荣耀、小米的广告拿取走该配置',
            fields: [
              {
                label: '广告拿取配置-v11',
                name: 'adGetXiaomiConfigList',
                type: 'array',
                hideLabel: true,
                format: <adGetConfig getType={'xiaomi'} />,
                item: {
                  type: 'object',
                  name: '',
                  label: '',
                  fields: getAdGetConfigFormSchema('xiaomi').fields,
                },
              },
            ],
          },
          // 广告拿取配置
          {
            label: '广告拿取配置',
            name: 'adGetConfig',
            type: 'object',
            hideLabel: true,
            viewType: 'card',
            fields: [
              {
                label: '正向广告超时时长',
                name: 'adPositiveTimeoutDuration',
                type: 'number',
                required: true,
                span: 8,
                tooltip:
                  '指正向场景若在该时长内未请求到正向广告，则可拿取负向广告在正向场景使用',
              },
              {
                label: '负向广告超时时长',
                name: 'adNegativeTimeoutDuration',
                type: 'number',
                required: true,
                span: 8,
                tooltip:
                  '当负向场景若在该时长内未请求到负向广告，则可拿取正向广告在负向场景使用',
              },
              {
                label: '是否拿取下载中广告',
                name: 'adGetDownloading',
                ...switchConfig,
                span: 3,
                required: true,
                tooltip:
                  '开启时下载中的广告正常竞价;关闭时标记为下载中的广告不可拿取，直至状态改变后可被使用',
              },
              {
                label: '下载广告时间间隔',
                name: 'downloadAdDuration',
                type: 'number',
                span: 5,
                suffix: 's',
                required: true,
                tooltip:
                  '当扫包扫出来应用信息后+配置的下载广告时间间隔后将广告状态改为唤醒类广告，可被拿取使用',
              },
              {
                label: '广告拿取配置',
                name: 'adGetConfigList',
                type: 'array',
                format: adGetConfig,
                item: {
                  type: 'object',
                  name: '',
                  label: '',
                  fields: getAdGetConfigFormSchema().fields,
                },
              },
            ],
          },
          // 骨架屏样式
          {
            label: '骨架屏配置',
            name: 'skeletonScreenConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              {
                label: '骨架屏背景不透明度',
                name: 'skeletonScreenOpaque',
                type: 'number',
                span: 8,
                suffix: '%',
                tooltip: '骨架屏黑色蒙版透明度调节',
                visibleOn: (data) => {
                  return data.enable === 1;
                },
              },
              {
                label: '自定义骨架屏样式',
                name: 'customStyle',
                type: 'file',
                accept: 'jpg,png,gif',
                span: 8,
                visibleOn: (data) => {
                  return data.enable === 1;
                },
                tooltip:
                  '填充的素材不论像素多大都会被渲染至150*150居中对齐摆放',
              },
              {
                label: '骨架屏展示时间',
                name: 'displayTime',
                type: 'number',
                require: true,
                span: 2,
                suffix: 'ms',
                tooltip:
                  '非首个弹窗在触发时机触发后先展示骨架屏一定时间后，再展示有广告弹窗',
                visibleOn: (data) => {
                  return data.enable === 1;
                },
              },
            ],
          },
          // 小说配置
          {
            label: '小说配置',
            name: 'novelContentConfig',
            type: 'array',
            format: 'table',
            viewType: 'card',
            hideLabel: true,
            minLength: 0,
            visibleOn: () => {
              return appStore.currentApp.type === 1;
            },
            item: {
              type: 'object',
              name: '',
              label: '',
              fields: [
                // 小说ID
                {
                  label: '小说ID',
                  name: 'novelId',
                  type: 'text',
                  maxLength: 20,
                  rules: [
                    {
                      required: true,
                    },
                  ],
                },
                {
                  label: '小说自定义内容code',
                  name: 'novelContentCode',
                  type: 'text',
                  maxLength: 40,
                  showWordLimit: true,
                  rules: [
                    {
                      required: true,
                    },
                  ],
                },
                // 权重
                {
                  label: '权重',
                  name: 'weight',
                  type: 'number',
                  min: 1,
                  max: 99,
                  rules: [
                    {
                      required: true,
                    },
                  ],
                },
              ],
            },
          },
          // 原生开屏
          // {
          //   label: '原生开屏',
          //   name: 'primitiveOpenScreenConfig',
          //   type: 'object',
          //   viewType: 'card',
          //   fields: [
          //     {
          //       label: '启用开关',
          //       name: 'enable',
          //       ...switchConfig,
          //       span: 24,
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '开屏场景',
          //       name: 'splashScene',
          //       type: 'select',
          //       format: 'checkbox',
          //       source: {
          //         data: [
          //           {
          //             value: 1,
          //             label: '启动开屏',
          //           },
          //           {
          //             value: 2,
          //             label: '后台切换开屏',
          //           },
          //         ],
          //       },
          //       span: 8,
          //       rules: [{ required: true }],
          //       visibleOn: isEnable,
          //     },
          //     {
          //       label: '每日启动开启次数',
          //       name: 'startupCount',
          //       type: 'number',
          //       span: 8,
          //       visibleOn(data) {
          //         return isEnable(data) && data.splashScene?.includes(1);
          //       },
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '每日后台切换开启次数',
          //       name: 'switchCount',
          //       type: 'number',
          //       span: 8,
          //       visibleOn(data) {
          //         return isEnable(data) && data.splashScene?.includes(2);
          //       },
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '关闭倒计时',
          //       type: 'number',
          //       name: 'closeCount',
          //       suffix: 'ms',
          //       startNewRow: true,
          //       span: 8,
          //       visibleOn: isEnable,
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '物理返回拦截',
          //       type: 'number',
          //       name: 'backCount',
          //       suffix: 'ms',
          //       span: 8,
          //       visibleOn: isEnable,
          //       rules: [{ required: true }],
          //     },
          //     // 摇一摇误触
          //     {
          //       label: '摇一摇误触',
          //       name: 'shakeErrorConfig',
          //       type: 'select',
          //       format: 'singleSelect',
          //       span: 8,
          //       source: {
          //         data: [
          //           {
          //             value: 1,
          //             label: '关闭',
          //           },
          //           {
          //             value: 2,
          //             label: '低灵敏度',
          //           },
          //           {
          //             value: 3,
          //             label: '中灵敏度',
          //           },
          //           {
          //             value: 4,
          //             label: '高灵敏度',
          //           },
          //         ],
          //       },
          //       visibleOn: isEnable,
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '全屏误触开关',
          //       name: 'fullScreenError',
          //       ...switchConfig,
          //       span: 8,
          //       visibleOn: isEnable,
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '跳过误触开关',
          //       name: 'skipError',
          //       ...switchConfig,
          //       span: 8,
          //       visibleOn: isEnable,
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '横幅误触样式',
          //       name: 'bannerErrorStyle',
          //       type: 'select',
          //       format: 'singleSelect',
          //       span: 8,
          //       source: {
          //         data: [
          //           {
          //             value: 1,
          //             label: '左图右文',
          //           },
          //           {
          //             value: 2,
          //             label: '右图左文',
          //           },
          //         ],
          //       },
          //       visibleOn: isEnable,
          //       rules: [{ required: true }],
          //     },
          //     {
          //       label: '横幅显示倒计时',
          //       type: 'number',
          //       name: 'bannerCount',
          //       suffix: 'ms',
          //       span: 8,
          //       visibleOn: isEnable,
          //       rules: [{ required: true }],
          //     },
          //   ],
          // },
          // 横幅广告
          {
            label: '横幅广告',
            name: 'bannerAdConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
            ],
          },
          // 激励视频
          {
            label: '激励视频',
            name: 'rewardVideoConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              // 广告点击频次过高触发激励视频开关
              {
                label: '广告点击频次过高触发激励视频',
                name: 'frequencyTriggerEnable',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              // 广告点击频次
              {
                label: '广告点击频次',
                name: 'frequency',
                type: 'object',
                span: 8,
                rules: [{ required: true }],
                visibleOn: (data) => data.frequencyTriggerEnable === 1,
                fields: [
                  {
                    label: '次数',
                    name: 'count',
                    type: 'number',
                    hideLabel: true,
                    suffix: '次',
                    span: 8,
                    rules: [{ required: true }],
                  },
                  {
                    label: '持续时间',
                    name: 'duration',
                    span: 8,
                    hideLabel: true,
                    suffix: '秒',
                    type: 'number',
                    rules: [{ required: true }],
                  },
                ],
              },
              // 触发比例
              {
                label: '触发比例',
                name: 'triggerRatioByFrequency',
                type: 'number',
                suffix: '%',
                span: 8,
                rules: [{ required: true }],
                visibleOn: (data) => data.frequencyTriggerEnable === 1,
              },
              // 比原生广告价格更高触发激励视频开关
              {
                label: '比原生广告价格更高触发激励视频',
                name: 'priceTriggerEnable',
                startNewRow: true,
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              // 触发比率
              {
                label: '触发比率',
                name: 'triggerRatioByPrice',
                type: 'number',
                suffix: '%',
                span: 8,
                rules: [{ required: true }],
                visibleOn: (data) => data.priceTriggerEnable === 1,
              },
              {
                label: '无用户行为时触发激励视频',
                name: 'behaviorTriggerEnable',
                startNewRow: true,
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              {
                label: '无用户行为时间',
                name: 'unUserBehaviorDuration',
                type: 'number',
                suffix: '秒',
                span: 8,
                rules: [{ required: true }],
                min: 1,
                precision: 0,
                visibleOn: (data) => data.behaviorTriggerEnable === 1,
              },
              {
                label: '生效次数',
                name: 'effectTimes',
                type: 'number',
                suffix: '次',
                span: 8,
                rules: [{ required: true }],
                visibleOn: (data) => data.behaviorTriggerEnable === 1,
                min: 1,
              },
            ],
          },
          // 活动推荐
          {
            label: '活动推荐',
            name: 'wakeupConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 24,
                rules: [{ required: true }],
                setter(value, formData, path) {
                  const backPlaySwitch = get(
                    formData.value,
                    path.replace(
                      'wakeupConfig',
                      'startBackPlayConfig.backPlaySwitch'
                    )
                  );

                  if (backPlaySwitch === 1 && value === 1) {
                    Message.warning(
                      '大退播放和活动推荐不可同时开启，请关闭活动推荐后再启用大退播放'
                    );
                    return 2;
                  }
                  return value;
                },
              },

              {
                label: '推荐场景',
                type: 'select',
                name: 'wakeUpScene',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      value: 1,
                      label: '点击广告',
                    },
                    {
                      value: 2,
                      label: '非广告点击',
                    },
                    {
                      value: 3,
                      label: '退出应用',
                    },
                  ],
                },
                visibleOn: isEnable,
              },
              {
                label: '适用场景',
                type: 'select',
                name: 'scene',
                format: 'multipleSelect',
                span: 8,
                source: {
                  // data: fetchTriggerTimeSource,
                  data: (field, formData, path) => {
                    // 已选的
                    const allTriggerCodes = [
                      ...getTriggerOptions({ path, formData }),
                      ...getPassiveTriggerOptions({
                        path,
                        formData,
                      }),
                    ];
                    return fetchSceneSource(allTriggerCodes);
                  },
                },
                visibleOn: (data) => isEnable(data) && data.wakeUpScene === 1,

                rules: [{ required: true }],
              },
              {
                label: '应用品牌',
                type: 'select',
                name: 'brand',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      value: 1,
                      label: '全部',
                    },
                    {
                      value: 2,
                      label: '华为',
                    },
                    {
                      value: 4,
                      label: '小米',
                    },
                    {
                      value: 5,
                      label: 'OPPO',
                    },
                    {
                      value: 6,
                      label: 'VIVO',
                    },
                    {
                      value: 7,
                      label: '荣耀',
                    },
                    // {
                    //   value: 3,
                    //   label: '其他',
                    // },
                  ],
                },
                visibleOn: isEnable,
                rules: [{ required: true }],
                onChange(value, formData, path) {
                  set(formData.value, `${path}.wakeUpType`, []);
                },
              },
              {
                // 推荐方式 取值：激励视频、push、插屏 可多选
                label: '推荐方式',
                type: 'select',
                name: 'wakeUpType',
                format: 'multipleSelect',
                span: 8,
                source: {
                  data: (field, formData, path) => {
                    const value = get(formData, `${path}.brand`);

                    const WakeUpType = {
                      PUSH: '1',
                      INCENTIVE_VIDEO: '2',
                      INTERSTITIAL: '3',
                      SYSTEM_PAGE: '4',
                      CONTACTS: '5',
                      SHARE_PAGE: '6',
                      IMAGE: '7',
                      THIRD_PARTY_SHARE: '8',
                      NAVIGATOR: '9',
                      SPLASH: '10',
                      VIVO_SYSTEM: '11',
                      OPPO_SYSTEM: '12',
                      HONOR_SENSOR: '13',
                    };

                    const wakeUpTypes = [
                      { value: WakeUpType.PUSH, label: 'Push' },
                      { value: WakeUpType.INCENTIVE_VIDEO, label: '激励视频' },
                      { value: WakeUpType.INTERSTITIAL, label: '插屏' },
                      { value: WakeUpType.SYSTEM_PAGE, label: '系统页面' },
                      { value: WakeUpType.CONTACTS, label: '通讯录' },
                      { value: WakeUpType.SHARE_PAGE, label: '分享页' },
                      { value: WakeUpType.IMAGE, label: 'Image' },
                      {
                        value: WakeUpType.THIRD_PARTY_SHARE,
                        label: '第三方分享',
                      },
                      { value: WakeUpType.NAVIGATOR, label: '跳板' },
                      { value: WakeUpType.SPLASH, label: '开屏' },
                      { value: WakeUpType.VIVO_SYSTEM, label: 'VIVO系统方法' },
                      { value: WakeUpType.OPPO_SYSTEM, label: 'OPPO系统方法' },
                      { value: WakeUpType.HONOR_SENSOR, label: '荣耀传感器' },
                    ];

                    const filterWakeUpTypes = (allowedValues) => {
                      return wakeUpTypes.filter((item) =>
                        allowedValues.includes(item.value)
                      );
                    };

                    // 华为
                    if (value === 2) {
                      return filterWakeUpTypes([
                        WakeUpType.INCENTIVE_VIDEO,
                        WakeUpType.PUSH,
                        WakeUpType.INTERSTITIAL,
                        WakeUpType.SYSTEM_PAGE,
                        WakeUpType.CONTACTS,
                        WakeUpType.SHARE_PAGE,
                        WakeUpType.IMAGE,
                        WakeUpType.THIRD_PARTY_SHARE,
                        WakeUpType.NAVIGATOR,
                      ]);
                    }

                    // 小米
                    if ([4].includes(value)) {
                      return filterWakeUpTypes([
                        WakeUpType.INCENTIVE_VIDEO,
                        WakeUpType.SYSTEM_PAGE,
                        WakeUpType.CONTACTS,
                        WakeUpType.SHARE_PAGE,
                        WakeUpType.IMAGE,
                        WakeUpType.SPLASH,
                      ]);
                    }
                    // OPPO
                    if ([5].includes(value)) {
                      return filterWakeUpTypes([
                        WakeUpType.INCENTIVE_VIDEO,
                        WakeUpType.SYSTEM_PAGE,
                        WakeUpType.CONTACTS,
                        WakeUpType.SHARE_PAGE,
                        WakeUpType.IMAGE,
                        WakeUpType.OPPO_SYSTEM,
                      ]);
                    }

                    // VIVO
                    if ([6].includes(value)) {
                      return filterWakeUpTypes([
                        WakeUpType.INCENTIVE_VIDEO,
                        WakeUpType.SYSTEM_PAGE,
                        WakeUpType.CONTACTS,
                        WakeUpType.SHARE_PAGE,
                        WakeUpType.IMAGE,
                        WakeUpType.VIVO_SYSTEM,
                      ]);
                    }

                    // 荣耀
                    if (value === 7) {
                      return filterWakeUpTypes([
                        WakeUpType.INCENTIVE_VIDEO,
                        WakeUpType.PUSH,
                        WakeUpType.INTERSTITIAL,
                        WakeUpType.SYSTEM_PAGE,
                        WakeUpType.IMAGE,
                        WakeUpType.HONOR_SENSOR,
                      ]);
                    }

                    return filterWakeUpTypes([
                      WakeUpType.INCENTIVE_VIDEO,
                      WakeUpType.IMAGE,
                    ]);
                  },
                },
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
              // 配置类型
              {
                label: '配置类型',
                type: 'select',
                name: 'systemWakeType',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      value: 1,
                      label: 'WIFI',
                    },
                    {
                      value: 2,
                      label: '蓝牙',
                    },
                    {
                      value: 3,
                      label: '位置信息',
                    },
                    {
                      value: 4,
                      label: '双卡网络',
                    },
                    {
                      value: 5,
                      label: 'NFC',
                    },
                  ],
                },
                visibleOn: (data) =>
                  isEnable(data) && data.wakeUpType?.includes('4'),
                rules: [{ required: true }],
              },
              // 活动推荐toast文案
              // {
              //   label: '活动推荐toast文案',
              //   type: 'text',
              //   name: 'wakeToastText',
              //   span: 8,
              //   visibleOn: isEnable,
              // },
              {
                label: '活动页toast文案',
                type: 'text',
                name: 'actionToastText',
                span: 8,
                visibleOn: isEnable,
                disabled: true,
              },
              // {
              //   label: '阅读页toast文案',
              //   type: 'text',
              //   name: 'readToastText',
              //   span: 8,
              //   visibleOn: isEnable,
              //   disabled: true,
              // },
              // {
              //   label: '短剧页toast文案',
              //   type: 'text',
              //   name: 'skitToastText',
              //   span: 8,
              //   visibleOn: isEnable,
              //   disabled: true,
              // },
              // {
              //   label: '取消按钮显示',
              //   type: 'switch',
              //   name: 'cancelbtn',
              //   span: 8,
              //   visibleOn: isEnable,
              //   disabled: true,
              // },
              {
                // 活动推荐次数
                label: '活动推荐次数',
                type: 'number',
                name: 'wakeUpCount',
                span: 8,
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
              {
                // 活动推荐次数
                label: '开屏活动推荐次数',
                type: 'number',
                name: 'wakeUpSplashCount',
                span: 8,
                visibleOn: (data) =>
                  isEnable(data) && data.wakeUpType?.includes('10'),
                rules: [{ required: true }],
              },
              {
                // 活动推荐延迟时间
                label: '活动推荐延迟时间',
                type: 'number',
                name: 'wakeUpDelay',
                suffix: 'ms',
                span: 8,
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
            ],
          },
          // 广告点击误触
          {
            label: '广告点击误触',
            name: 'adClickMistakeConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 24,
                rules: [{ required: true }],
              },
              // 误触方式 多项选择 全屏点击误触、摇一摇触发、倒计时触发
              {
                label: '误触方式',
                type: 'select',
                name: 'errorType',
                format: 'multipleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      value: 1,
                      label: '全屏点击误触',
                    },
                    {
                      value: 2,
                      label: '摇一摇触发',
                    },
                    {
                      value: 3,
                      label: '倒计时触发',
                    },
                    {
                      value: 4,
                      label: '全屏adb',
                    },
                  ],
                },
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
              {
                label: '适用场景',
                type: 'select',
                name: 'scene',
                format: 'multipleSelect',
                span: 8,
                source: {
                  data: (field, formData, path) => {
                    // 已选的
                    const allTriggerCodes = [
                      ...getTriggerOptions({ path, formData }),
                      ...getPassiveTriggerOptions(
                        {
                          path,
                          formData,
                        },
                        'filter'
                      ),
                    ];
                    return fetchSceneSource(allTriggerCodes);
                  },
                },
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
              {
                label: '适用广告组件',
                type: 'select',
                name: 'fitAdComponent',
                format: 'multipleSelect',
                maxTagCount: 2,
                span: 8,
                source: {
                  data: async (field, formData, path) => {
                    const currentCodes = getTriggerTimeOption({
                      path,
                      formData,
                    });
                    const entireAdComponentList = await dict.adComponent({
                      appId: appId.value,
                    });
                    return entireAdComponentList.filter((item) => {
                      return currentCodes.includes(item.value);
                    });
                  },
                },
                visibleOn: isEnable,
              },
              // 摇一摇灵敏度 低灵敏度、中灵敏度、高灵敏度
              {
                label: '摇一摇灵敏度',
                type: 'select',
                name: 'shakeSensitivity',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      value: 1,
                      label: '低灵敏度',
                    },
                    {
                      value: 2,
                      label: '中灵敏度',
                    },
                    {
                      value: 3,
                      label: '高灵敏度',
                    },
                  ],
                },
                visibleOn: (data) =>
                  isEnable(data) && data.errorType?.includes?.(2),
                required: true,
              },
              // 倒计时时间
              {
                label: '倒计时时间',
                type: 'number',
                name: 'countDownTime',
                suffix: '秒',
                span: 8,
                required: true,
                visibleOn: (data) =>
                  isEnable(data) && data.errorType?.includes?.(3),
              },
              {
                label: '黑屏开关',
                name: 'blackScreenTouch',
                ...switchConfig,
                span: 24,
                visibleOn: (data) =>
                  isEnable(data) && data.errorType?.includes?.(1),
                required: true,
              },
              // 全屏adb概率
              {
                label: '全屏adb概率',
                type: 'number',
                name: 'fullScreenAdbRatio',
                suffix: '%',
                span: 8,
                max: 100,
                visibleOn: (data) =>
                  isEnable(data) && data.errorType?.includes?.(4),
                rules: [{ required: true }],
              },
            ],
          },
          // adb按钮能力
          {
            label: 'adb按钮能力',
            name: 'adbButtonConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 24,
                rules: [{ required: true }],
              },
              // 按钮模式 全透明、点矩阵居中、点矩阵居右
              {
                label: '按钮模式',
                type: 'select',
                name: 'mode',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      value: 1,
                      label: '全透明',
                    },
                    {
                      value: 2,
                      label: '点矩阵居中',
                    },
                    {
                      value: 3,
                      label: '点矩阵居右',
                    },
                    {
                      value: 4,
                      label: 'vivo站内合规诱导模式',
                    },
                  ],
                },
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
              // adb点击比例
              {
                label: 'adb点击比例',
                type: 'number',
                name: 'adbRatio',
                suffix: '%',
                span: 8,
                max: 100,
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
            ],
          },
          // 模板广告
          {
            label: '模板广告设置',
            name: 'templateAdConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              // 模板广告点击比例
              {
                label: '模板广告点击比例',
                type: 'number',
                name: 'tempClickPercent',
                suffix: '%',
                span: 8,
                max: 100,
                rules: [{ required: true }],
              },
              {
                label: '模板广告是否可见',
                name: 'tempStyleType',
                type: 'select',
                format: 'buttonRadio',
                source: {
                  data: [
                    {
                      label: '可见',
                      value: 2,
                    },
                    {
                      label: '不可见',
                      value: 1,
                    },
                  ],
                },
                span: 8,
                rules: [{ required: true }],
              },
            ],
          },
          // a拉b
          {
            label: 'a拉b设置',
            name: 'jumpAppConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              // 单次拉起次数上限： jumpAppLimitCount
              {
                label: '单次拉起次数上限',
                name: 'jumpAppSingleTimeLimitCount',
                type: 'number',
                span: 8,
                visibleOn: (data) => {
                  return isEnable(data) && data.jumpAppTiming === 6;
                },
                rules: (field, formData) => [
                  {
                    validator: (value, cb) => {
                      if (
                        value >
                        formData.behaviorTacticsConfigData[0].jumpAppConfig
                          .jumpAppLimitCount
                      ) {
                        cb('单次拉起次数上限不能大于单日拉起次数上限');
                      }
                    },
                  },
                  {
                    require: true,
                  },
                ],
              },
              // 单日拉起次数上限： jumpAppLimitCount
              {
                label: '单日拉起次数上限',
                name: 'jumpAppLimitCount',
                type: 'number',
                span: 8,
                visibleOn: (data) => {
                  return isEnable(data);
                },
                rules: [{ required: true }],
              },
              // 拉起B广告请求轮次 : jumpAppRequestCount
              {
                label: '拉起B广告请求轮次',
                name: 'jumpAppRequestCount',
                type: 'number',
                span: 8,
                visibleOn: (data) => {
                  return isEnable(data) && data.jumpAppTiming === 2;
                },
                rules: [{ required: true }],
              },
              // 拉起B产品时机  : jumpAppTiming
              {
                label: '拉起B产品时机',
                type: 'select',
                name: 'jumpAppTiming',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      label: '启动进入倒计时',
                      value: 1,
                    },
                    {
                      label: '广告请求轮次上限',
                      value: 2,
                    },
                    {
                      label: '广告点击退出',
                      value: 3,
                    },
                    {
                      label: '非广告点击退出',
                      value: 4,
                    },
                    {
                      label: '全部退出',
                      value: 5,
                    },
                    {
                      label: '激励视频倒计时',
                      value: 6,
                    },
                  ],
                },
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
              // 拉起B启动倒计时:  jumpAppCountDown
              {
                label: '激励视频倒计时',
                name: 'videoCountDown',
                type: 'number',
                suffix: '秒',
                span: 8,
                precision: 0,
                min: 1,
                visibleOn: (data) => {
                  return isEnable(data) && data.jumpAppTiming === 6;
                },
                rules: [{ required: true }],
              },
              // 拉起B启动倒计时:  jumpAppCountDown
              {
                label: '拉起B启动倒计时',
                name: 'jumpAppCountDown',
                type: 'number',
                suffix: '秒',
                span: 8,
                visibleOn: (data) => {
                  return isEnable(data) && data.jumpAppTiming === 1;
                },
                rules: [{ required: true }],
              },
              // 拉起B产品链接 :   jumpAppUrl
              {
                label: '拉起B产品链接',
                name: 'jumpAppUrl',
                type: 'text',
                span: 8,
                visibleOn: isEnable,
                rules: [{ required: true }],
              },
            ],
          },
          // 启动大退播放
          {
            label: '启动大退播放',
            name: 'startBackPlayConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启动播放开关',
                name: 'startPlaySwitch',
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
              },
              {
                label: '播放类型',
                type: 'select',
                name: 'startType',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      label: '播放激励视频',
                      value: 1,
                    },
                    {
                      label: '播放插屏',
                      value: 2,
                    },
                  ],
                },
                visibleOn: (data) => data.startPlaySwitch === 1,
                rules: [{ required: true }],
              },
              {
                label: '大退播放开关',
                name: 'backPlaySwitch',
                startNewRow: true,
                ...switchConfig,
                span: 8,
                rules: [{ required: true }],
                setter(value, formData, path) {
                  const wakeupConfigEnable = get(
                    formData.value,
                    path.replace('startBackPlayConfig', 'wakeupConfig.enable')
                  );

                  if (wakeupConfigEnable === 1 && value === 1) {
                    Message.warning(
                      '大退播放和活动推荐不可同时开启，请关闭活动推荐后再启用大退播放'
                    );
                    return 2;
                  }
                  return value;
                },
              },
              {
                label: '播放类型',
                type: 'select',
                name: 'backType',
                format: 'singleSelect',
                span: 8,
                source: {
                  data: [
                    {
                      label: '播放激励视频',
                      value: 1,
                    },
                    {
                      label: '播放插屏',
                      value: 2,
                    },
                  ],
                },
                visibleOn: (data) => data.backPlaySwitch === 1,
                rules: [{ required: true }],
              },
            ],
          },
          // 华为--模板广告点击间隔
          {
            label: '华为--模板广告点击间隔',
            name: 'templateAdClickConfig',
            type: 'object',
            viewType: 'card',
            visibleOn: () => false,
            fields: [
              {
                label: '连续点击次数',
                name: 'continueClicks',
                type: 'number',
                span: 8,
                suffix: '次',
                required: true,
                tooltip: '连续点击次数不可少于两次，低于两次则默认生效两次',
              },
              {
                label: '点击间隔',
                name: 'continueTime',
                type: 'number',
                span: 8,
                suffix: 'ms',
                required: true,
                tooltip: '点击间隔不可少于100ms，少于100ms默认生效100ms',
              },
            ],
          },
          {
            label: '无广告时拉应用商店',
            name: 'noAdPopShowCountConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 8,
                required: true,
              },
              {
                label: '无广告弹窗连续展示次数',
                name: 'noAdPopCount',
                type: 'number',
                span: 8,
                required: true,
                suffix: '次',
                min: 1,
                visibleOn: (data) => data.enable === 1,
              },
            ],
          },
          {
            label: '一次点击多个广告',
            name: 'clickManyAdConfig',
            type: 'object',
            viewType: 'card',
            tooltip: 'OPPO,VIVO厂商',
            fields: [
              {
                label: '启用开关',
                name: 'enable',
                ...switchConfig,
                span: 8,
                required: true,
              },
              {
                label: '触发范围',
                name: 'ecpmRange',
                type: 'numberRange',
                span: 6,
                required: true,
                suffix: '元',
                visibleOn: (data) => data.enable === 1,
                rules: [
                  {
                    validator: (value, cb) => {
                      if (value[0] === undefined || value[1] === undefined) {
                        cb('请输入触发范围');
                        return;
                      }
                      cb();
                    },
                  },
                ],
              },
            ],
          },
          {
            label: 'back设置',
            name: 'backConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: '拦截时长',
                name: 'time',
                type: 'number',
                span: 8,
                suffix: 'ms',
                tooltip: 'OPPO,VIVO厂商',
                min: 0,
              },
              {
                label: '拦截次数',
                name: 'backCount',
                type: 'number',
                precision: 0,
                span: 8,
                suffix: '次',
                tooltip: '小米,荣耀,华为厂商',
                min: 0,
              },
            ],
          },
          {
            label: '活动页',
            name: 'activePageConfig',
            type: 'object',
            viewType: 'card',
            tooltip: 'OPPO,VIVO厂商',
            fields: [
              {
                label: '活动ID',
                name: 'actionId',
                type: 'text',
                span: 8,
                required: true,
                visibleOn: (data) => data.autoJump === 1,
              },
              {
                label: '是否自动跳转',
                name: 'autoJump',
                ...switchConfig,
                span: 8,
                required: true,
              },
            ],
          },
          {
            label: '唤醒类广告设置',
            name: 'hxTypeAdConfig',
            type: 'object',
            viewType: 'card',
            fields: [
              {
                label: 'adc点击比例',
                type: 'number',
                name: 'adcRatio',
                suffix: '%',
                span: 8,
                max: 100,
                rules: [{ required: true }],
              },
            ],
          },
        ],
      },
    },
  ],
};

export default formSchema;
