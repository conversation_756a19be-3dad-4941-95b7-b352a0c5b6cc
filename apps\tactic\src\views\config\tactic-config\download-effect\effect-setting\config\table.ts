import { isNil, omit } from 'lodash';
import { downloadEffectApi } from '@/api-v1/config/tactic-config/download-effect';
import auth from '@/constants/auth-key-v1/config/tactic-config/download-effect';

import OverallRate from '../components/overall-rate.vue';

const getTable = (brand: string) => {
  return {
    load: {
      action: async (filter, pagination) => {
        const formattedFilter = omit(
          filter,
          Object.keys(filter).filter(
            (key) => isNil(filter[key]) || filter[key] === ''
          )
        );

        const res = await downloadEffectApi?.list({
          brand,
          ...formattedFilter,
          ...pagination,
        });
        console.log('res', res);
        return res;
      },
    },
    columns: [
      {
        title: '序号',
        dataIndex: 'id',
      },
      {
        title: '广告应用名称',
        dataIndex: 'name',
      },
      {
        title: '广告应用包名',
        dataIndex: 'pkg',
      },

      {
        title: '大盘次留率',
        dataIndex: 'overallRate',
        customRender: {
          type: OverallRate,
        },
      },
      {
        title: '目标次留率',
        dataIndex: 'targetRate',
      },
      {
        title: 'Deeplink地址',
        dataIndex: 'deeplink',
      },
      {
        title: '激活有效期',
        dataIndex: 'activateValidHour',
      },
      {
        title: '关键词',
        dataIndex: 'keyword',
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        width: 156,
      },
      {
        title: '更新人',
        dataIndex: 'updatedUname',
      },
      {
        title: '是否启用',
        dataIndex: 'isOpen',
        customRender: {
          type: 'switch',
          props: {
            auth: auth.DOWNLOAD_EFFECT_BATCH_STATUS,
            action(data, record) {
              return downloadEffectApi?.toggleStatus?.({
                id: record.id,
                isOpen: data,
              });
            },
          },
        },
      },
    ],
  };
};

export default getTable;
