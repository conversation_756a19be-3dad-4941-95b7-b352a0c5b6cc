<template>
  <div class="put-config">
    <CollapsePanel>
      <template #title>
        <PanelTitle title="投放配置" />
      </template>
      <div class="put-config-content">
        <WidgetByType
          :field="composedObjectField"
          v-model="model"
          :path="`${path}`"
        />
      </div>
    </CollapsePanel>
  </div>
</template>

<script setup lang="tsx">
  import CollapsePanel from '@/components/collapse-panel/index.vue';
  import WidgetByType from '@repo/kola/src/components/dynamic/form/widgets/widget-by-type.vue';
  import { computed, watch } from 'vue';
  import { ObjectField as ObjectFieldType } from '@repo/kola/src/components/dynamic/types/form';
  import { getGlobalAppFormSchema } from '@/utils/global-app';
  import accountsApi from '@/api/promotion/toutiao/accounts';
  import { get } from 'lodash';
  import PanelTitle from '@/views/automated-setup/common/components/panel-title/index.vue';
  import MatchAccountModal from './components/match-account-modal/index.vue';

  const props = defineProps<{
    path: string;
  }>();
  const model = defineModel<any>();
  const composedObjectField = computed<ObjectFieldType>(() => {
    return {
      type: 'object',
      fields: [
        {
          name: 'corporationNames',
          label: '账户主体',
          type: 'select',
          format: 'multipleSelect',
          span: 12,
          select: {
            allowSearch: true,
            allowClear: true,
            tooltip: true,
            openVirtual: true,
            limit: 1,
          },
          source: {
            data: () => {
              return accountsApi.getCorporationNameList?.({}).then((res) => {
                return res.data.map((item) => {
                  return {
                    label: item,
                    value: item,
                  };
                });
              });
            },
          },
          onChange() {
            model.value.accountList = [];
          },
        },
        {
          name: 'accountList',
          label: '账户',
          type: 'select',
          format: 'multipleSelect',
          select: {
            allowSearch: true,
            allowClear: true,
            limit: 100,
            showAll: true,
            maxTagCount: 1,
            valueKey: 'accountId',
            isAsyncSearch: true,
            showExtraOptions: false,
            filterOption: false,
            formatLabel: (data: any) => {
              return data.value?.accountName ?? data.label;
            },
          },
          span: 10,
          source: {
            data: async (field, formData, _, searchText) => {
              const corporationNames = model.value.corporationNames ?? [];
              if (corporationNames.length === 0) {
                return [];
              }
              // const currentAppId = model.value.appId;
              const submitData = {
                corporationNames: corporationNames.join(','),
                keyWord: searchText || '',
                pageNum: 1,
                pageSize: 500,
                // applicationId: currentAppId,
              };
              const res = await accountsApi.getAccountList?.(submitData);
              const list = get(res, 'data.list', []);
              return list.map((item) => {
                return {
                  label: item.advertiserName,
                  value: {
                    accountName: item.advertiserName,
                    accountId: item.advertiserId,
                    corporationName: item?.corporationName,
                  },
                };
              });
            },
          },
          placeholder: '请选择账户',
          innerExtra: MatchAccountModal,
        },
      ],
      hideLabel: true,
      name: '',
      label: '',
      layout: 'horizontal',
    };
  });
</script>

<style scoped lang="less">
  .put-config {
    flex: 1;

    .put-config-content {
      padding: 20px;
      padding-bottom: 0;
    }
  }
</style>
