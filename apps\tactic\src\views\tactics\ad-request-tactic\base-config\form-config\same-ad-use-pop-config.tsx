import switchConfig from '@/views/tactics/common/switch-config';

export const getSameAdUsePopConfigSchema = () => {
  return [
    {
      type: 'object',
      name: 'sameAdUsePopConfig',
      hideLabel: true,
      fields: [
        {
          type: 'viewCard',
          label: '不拿取相同广告',
          contentStyle: {
            width: '640px',
          },
          tooltip: '华为,荣耀,小米厂商',
          fields: [
            {
              label: '开关',
              name: 'enable',
              ...switchConfig,
              span: 8,
              rules: [{ required: true }],
            },
            {
              label: '相同广告点击间隔',
              name: 'adClickGapTime',
              type: 'number',
              min: 0,
              span: 8,
              suffix: 's',
              required: true,
              tooltip: '小米厂商',
              visibleOn: (data) => data.enable === 1,
            },
            {
              label: '无广告时是否拿取未到配置时间的唤醒广告',
              name: 'isUseSamePkgBeforeInterval',
              ...switchConfig,
              span: 6,
              required: true,
              visibleOn: (data) => data.enable === 1,
            },
          ],
        },
      ],
    },
  ];
};

export const getSameAdUsePopConfigData = () => {
  return {
    sameAdUsePopConfig: {
      enable: 2,
      adClickGapTime: 1,
      isUseSamePkgBeforeInterval: 1,
    },
  };
};

export default null;
