// eslint-disable-next-line import/no-cycle

import { cloneDeep, omit } from 'lodash';
import { DPage } from '@repo/seal/src/components/dynamic/types/page';
import {
  adbComponentGroup,
  adbComponent,
} from '@/api-v1/component/ad-component/adb-component';
import { hydrateDeep } from '@/utils';
import useCompanyCode from '@/hooks/company-code';
import { adbAuth } from '@/constants/auth-key-v1/component/ad-component/cy';
import { getFormData as getTemplateFormData } from '@/views/component/ad-component-template/adb-template/config/operation';
import configBase from './config-module/config-base';
import delayIntercept from './config-module/delay-intercept';
import template from './config-module/template';
import skeletonScreen from './config-module/skeleton-screen';

const companyCode = useCompanyCode();

export function getFormSchema() {
  return {
    fields: [
      configBase.getFormSchema(),
      delayIntercept.getFormSchema(),
      skeletonScreen.getFormSchema(),
      template.getFormSchema(),
    ],
  };
}

export function getFormData() {
  return {
    id: undefined,
    groupId: undefined,
    ...configBase.getFormData(),
    ...delayIntercept.getFormData(),
    ...skeletonScreen.getFormData(),
    ...template.getFormData(),
  };
}

// 格式化提交数据
export function getSubmitData(data) {
  const formData = cloneDeep(data);
  formData.template.template = formData.template.template || 0;
  Object.assign(formData, formData.configBase);
  formData.templateName = formData.createNewTemplate
    ? formData.base.name
    : undefined;
  formData.companyCode = formData.createNewTemplate
    ? formData.base.companyCode
    : 'CY';
  formData.forwardBtnConfig.styleConfig.buttons?.forEach((item) => {
    item.supportAdType = [item.supportAdType];
  });
  formData.styleConfig = formData.forwardBtnConfig.styleConfig;
  formData.forwardBtnConfig = omit(formData.forwardBtnConfig, 'styleConfig');
  delete formData.configBase;
  delete formData.base;
  return formData;
}

// 设置回显数据
function getFormatFormData(data) {
  let formData: any = Object.assign(getTemplateFormData(), getFormData());
  formData = hydrateDeep(formData, data);
  formData.configBase = hydrateDeep(formData.configBase, data);
  formData.base = hydrateDeep(formData.base, data);
  formData.base.name = '';
  if (data.template.template === 0) {
    formData.template.template = '';
  }
  data.styleConfig.buttons?.forEach((item) => {
    item.supportAdType = item.supportAdType[0];
  });
  formData.forwardBtnConfig.styleConfig = data.styleConfig;
  return formData;
}

export const fetchModifyDetail = (record) => {
  return adbComponent
    .detail?.({ id: record.id, companyCode: companyCode.value })
    .then((res) => {
      res.data.id = record.id;
      res.data.groupId = record.groupId;
      res.data = getFormatFormData(res.data);
      return res;
    });
};

const operation: DPage['operation'] = {
  batchRemove: {
    auth: adbAuth.ADB_COMPONENT_BATCH_DELETE,
    action: (data) => {
      return adbComponent.remove?.({
        ids: data,
        companyCode: companyCode.value,
      });
    },
  },
  copy: {
    auth: adbAuth.ADB_COMPONENT_COPY,
    ui: { width: '800px' },
    action: (data) => {
      data.configBase.name = data.name;
      data = getSubmitData(data);
      return adbComponent.create?.(data);
    },
    form: {
      formSchema: {
        fields: [
          {
            label: '新增样式名称',
            name: 'name',
            type: 'text',
            placeholder: '请输入样式名称',
            required: true,
          },
          {
            label: '复制至分组',
            name: 'groupId',
            type: 'select',
            placeholder: '请选择分组',
            format: 'singleSelect',
            select: {
              allowClear: true,
              allowSearch: true,
            },
            required: true,
            source: {
              labelKey: 'name',
              valueKey: 'id',
              data: () => {
                return adbComponentGroup
                  .list?.({ companyCode: companyCode.value })
                  .then((res) => {
                    return res.data;
                  });
              },
            },
          },
          {
            label: '',
            name: '',
            type: 'alert',
            title: '说明',
            content:
              '注意不同厂商支持的广告类型不同，复制后需要调整多adb组件的广告类型。',
          },
        ],
      },
      defaultFormData: {
        name: '',
        groupId: undefined,
      },
    },
    fetchDetail(record) {
      return adbComponent
        .detail?.({ id: record.id, companyCode: companyCode.value })
        .then((res) => {
          res.data = getFormatFormData(res.data);
          res.data.id = record.id;
          res.data.groupId = record.groupId;
          res.data.name = record.name;
          return res;
        });
    },
  },
  remove: {
    auth: adbAuth.ADB_COMPONENT_DELETE,
    action: (data) => {
      return adbComponent.remove?.({
        ids: [data.id],
        companyCode: companyCode.value,
      });
    },
  },
};

export default operation;
