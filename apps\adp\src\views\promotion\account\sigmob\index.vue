<template>
  <PortalPage title="Sigmob">
    <DynamicPage
      ref="dynamicPageRef"
      :operation="operation"
      :table="tableJson"
      :filter="filterJson"
      :batch-operation="batchOperations"
      row-key="advertiserId"
    ></DynamicPage>
  </PortalPage>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import PortalPage from '@/components/portal-page/index.vue';
  import { MediaType } from '@/views/promotion/hooks/authorization';
  import account from '@/constants/auth-key/promotion/account';
  import tableJson from './config/table';
  import getBatchOperation from '../utils';
  import getOperation from './config/operation';
  import getCommonFilter from '../component/commonFilter';

  const filterJson = getCommonFilter(MediaType.SIGMOB);

  function successHandle() {
    dynamicPageRef.value?.refreshTable();
  }
  const operation = getOperation(successHandle);

  const dynamicPageRef = ref();
  const getList = () => dynamicPageRef.value.tableRef.getCurrentRowsData();
  const batchOperations = getBatchOperation({
    getList,
    mediaPlatform: MediaType.SIGMOB,
    updateAuth: account.ACCOUNT_SIGMOB_BATCH_DATA_STATUS,
    deleteAuth: account.ACCOUNT_SIGMOB_BATCH_DELETE,
  });
</script>

<style lang="less"></style>
