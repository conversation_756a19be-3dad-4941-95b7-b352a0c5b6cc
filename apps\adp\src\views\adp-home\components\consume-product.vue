<template>
  <div class="consume-product">
    <HomeCard
      tip-content="展示有权限产品的所有媒体的消耗分布情况"
      :loading="loading"
    >
      <template #title>产品消耗分布</template>
      <template #extra>
        <HomeSearch
          ref="searchRef"
          :search-fields="searchFields"
          @change="loadData"
          :get-default-form-data="setDefaultSearch"
          :is-export="true"
          @download="downloadHandle"
        />
      </template>
      <div class="chart-content">
        <div class="chart-total" v-show="chartsData.total"
          >合计<br />{{ chartsData.total }}
        </div>
        <Chart :option="chartOption" />
      </div>
    </HomeCard>
  </div>
</template>

<script setup lang="ts">
  import HomeCard from '@/views/adp-home/components/common/home-card.vue';
  import HomeSearch from '@/views/adp-home/components/common/home-search.vue';
  import { onMounted, ref } from 'vue';
  import useChartOption from '@/hooks/chart-option';
  import { keyBy, round, sumBy } from 'lodash';
  import { toCamelCase } from '@/views/report/utils';
  import {
    getSearchValue,
    selectEnum,
    productEnum,
  } from '@/views/adp-home/utils';
  import { adpHomeApi } from '@/api/adp-home';
  import { downloadFile } from '@/utils/download';

  const loading = ref(false);
  const chartsData = ref<any>({
    data: [],
    total: 0,
  });
  const { chartOption } = useChartOption(() => {
    const list = chartsData.value.data;
    const dataMap = keyBy(list, 'name');
    const searchValue = getSearchValue(searchRef);
    return {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        orient: 'vertical',
        left: '50%',
        y: 'center',
        icon: 'circle',
        type: 'scroll',
        selectedMode: false,
        formatter: (name) => {
          const dataItem = dataMap[name];
          if (dataItem) {
            const dataValue = dataItem.value;
            const percentages = dataItem?.ratio;
            const qualityScore = dataItem?.[toCamelCase(searchValue.dataIndex)];
            return `${name} | ${dataValue} | ${percentages} | ${qualityScore}`;
          }
          return `${name} | -`;
        },
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['50%', '70%'],
          center: [list.length ? '25%' : '50%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: list,
        },
      ],
    };
  });
  const searchFields = [
    () => ({
      fieldName: selectEnum.dataIndex,
      placeholder: '',
      style: { width: '118px' },
    }),
    () => ({
      fieldName: selectEnum.multipleMedia,
      style: { width: '190px' },
    }),
    selectEnum.app,
    selectEnum.date,
  ];
  const searchRef = ref();

  function setDefaultSearch(searchValue) {
    return {
      ...searchValue,
      dataIndex: productEnum.cpmScore,
    };
  }

  function downloadHandle() {
    const searchValue = getSearchValue(searchRef);
    adpHomeApi.exportConsumeProduct(searchValue).then((res) => {
      downloadFile(res, `产品消耗分布.xlsx`, 'application/vnd.ms-excel');
    });
  }
  function loadData() {
    const searchValue = getSearchValue(searchRef);
    loading.value = true;
    adpHomeApi
      .consumeProduct(searchValue)
      .then((res: any) => {
        const data = res.data ?? [];
        chartsData.value = {
          total: round(
            sumBy(data, (item: any) => parseFloat(item.mediaCost) || 0),
            2
          ),
          data: data.map((item) => {
            return {
              ...item,
              name: item.appName,
              value: item.mediaCost ?? 0,
            };
          }),
        };
      })
      .finally(() => {
        loading.value = false;
      });
  }

  onMounted(() => {
    loadData();
  });
</script>

<style scoped lang="less">
  .consume-product {
    .chart-content {
      height: 295px;
      margin: 0 auto;
      position: relative;

      .chart-total {
        position: absolute;
        top: 50%;
        left: calc(25% - 100px);
        transform: translate(0, -50%);
        font-size: 16px;
        line-height: 26px;
        width: 199px;
        text-align: center;
      }
    }
  }
</style>
