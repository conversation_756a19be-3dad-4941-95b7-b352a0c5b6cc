<template>
  <div class="line-wrapper">
    <a-row :gutter="8">
      <a-col :span="7">
        <a-form-item
          label=""
          :field="`${path}[${index}].${firstOptions.field}`"
          hide-label
          :rules="[
            {
              required: firstOptions.required,
              message: firstOptions.message,
              validateTrigger: ['change'],
            },
          ]"
        >
          <a-select
            v-model="lineInfo[firstOptions.field]"
            :placeholder="firstOptions.placeholder"
            :disabled="firstOptions.disabled"
            style="color: #1d2129"
          >
            <a-option
              v-for="item in firstOptions.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></a-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="7">
        <a-form-item
          label=""
          :field="`${path}[${index}].${secondOptions.field}`"
          hide-label
          :rules="[
            {
              required: secondOptions.required,
              message: secondOptions.message,
              validateTrigger: ['change'],
            },
          ]"
        >
          <a-select
            v-model="lineInfo[secondOptions.field]"
            :placeholder="secondOptions.placeholder"
          >
            <a-option
              v-for="item in compareOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            ></a-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="7">
        <a-form-item
          label=""
          :field="`${path}[${index}].${thirdOptions.field}`"
          hide-label
          :rules="[
            {
              required: true,
              message: thirdOptions.message,
              validateTrigger: ['change'],
            },
          ]"
        >
          <a-select
            v-if="valueType === 'select'"
            v-model="lineInfo[thirdOptions.field]"
            :placeholder="thirdOptions.placeholder"
          >
            <a-option
              v-for="item in valueOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></a-option>
          </a-select>
          <a-input-number
            v-else-if="valueType === 'float' && !isTop"
            v-model="lineInfo[thirdOptions.field]"
            :placeholder="'支持两位小数,百分数请输入小数'"
            :precision="2"
            :max="1000000000"
            :min="0"
          >
          </a-input-number>
          <a-input-number
            v-else
            v-model="lineInfo[thirdOptions.field]"
            :placeholder="'请输入值'"
            :precision="0"
            :max="1000000000"
            :min="0"
          >
          </a-input-number>
        </a-form-item>
      </a-col>
      <a-col :span="2" v-if="canDelete">
        <div class="card-delete" v-show="list.length > minLength">
          <icon-delete size="16" @click="handleRemove(index)" />
        </div>
      </a-col>
    </a-row>
    <div class="content-condition-top" v-show="index > 0"> </div>
    <div class="content-condition-bottom" v-show="index > 0"> </div>
    <div class="content-condition-text" v-show="index > 0">且</div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { VALUE_MAP } from '../constant';

  const props = withDefaults(
    defineProps<{
      index: number;
      list: any;
      path: string;
      firstOptions: any;
      secondOptions: any;
      thirdOptions: any;
      canDelete?: boolean;
      minLength?: number;
    }>(),
    {
      minLength: 1,
    }
  );
  const emit = defineEmits(['remove']);
  const isTop = computed(() => {
    return lineInfo.value[props.secondOptions.field] === 'top';
  });
  const compareOptions = computed(() => {
    if (
      props.path === 'campaignFilter' &&
      lineInfo.value[props.firstOptions.field] === 'media_cost'
    ) {
      return [
        {
          label: '大于',
          value: 'gt',
        },
        {
          label: '大于等于',
          value: 'gte',
        },
      ];
    }
    const target = props.list.findIndex(
      (item: any) => item.condition === 'top'
    );
    const res = (
      props.secondOptions.options[lineInfo.value[props.firstOptions.field]] ||
      []
    ).map((item: any) => {
      return {
        ...item,
        disabled:
          target !== -1 && item.value === 'top' && target !== props.index,
      };
    });
    return res;
  });

  const valueOptions = computed(() => {
    return Array.isArray(
      props.thirdOptions.options[lineInfo.value[props.firstOptions.field]]
    )
      ? props.thirdOptions.options[lineInfo.value[props.firstOptions.field]]
      : null;
  });

  const lineInfo = defineModel<any>();
  const handleRemove = (conditionIndex: number) => {
    emit('remove', conditionIndex);
  };
  const valueType = computed(() => {
    const value = VALUE_MAP[lineInfo.value[props.firstOptions.field]];
    if (Array.isArray(value)) {
      return 'select';
    }

    return value;
  });
</script>

<style scoped lang="less">
  .line-wrapper {
    width: 100%;
    position: relative;

    .card-delete {
      cursor: pointer;
      padding-top: 5px;

      :hover {
        color: rgb(var(--primary-6));
      }
    }

    .content-condition-bottom {
      position: absolute;
      top: 3px;
      left: -50px;
      width: 40px;
      height: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        content: '';
        width: 2px;
        height: 100%;
        background-color: #165dff;
      }
    }

    .content-condition-top {
      position: absolute;
      top: -44px;
      left: -50px;
      width: 40px;
      height: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        content: '';
        width: 2px;
        height: 100%;
        background-color: #165dff;
      }
    }

    .content-condition-text {
      position: absolute;
      left: -50px;
      top: -31px;
      width: 42px;
      height: 42px;
      color: #fff;
      background-color: #165dff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 6px solid #fff;
    }
  }
</style>
