import { ref } from 'vue';
import { DTable } from '@repo/kola/src/components/dynamic/types/table';
import accountCommonApi from '@/api/promotion/common';
import { MediaType } from '@/views/promotion/hooks/authorization';
import _, { map } from 'lodash';
import { commonApi } from '@/api/common';
import account from '@/constants/auth-key/promotion/account';
import { MediaPlatform } from '@/api/promotion/types';
import { Message } from '@arco-design/web-vue';
import { getCustomLinkConfig, getTail } from '@/views/promotion/account/utils';
import { dataUpdateHeaderTooltip } from '@/views/promotion/constants';
import { getAccountOperModeColumn, getAgentSchema } from '../../common';

const applicationMap = ref(new Map());

const currentEditRecord = ref();
const updateLinkFlag = ref(false);
const curApplicationId = ref();

const tableJson: DTable = {
  load: {
    action: async (filter, pagination) => {
      return accountCommonApi
        .list?.({
          ..._.omit(filter, 'dateRange'),
          ...pagination,
          applicationId: window.$appInfo?.id,
          startDate: filter?.dateRange?.[0],
          endDate: filter?.dateRange?.[1],
          mediaPlatform: MediaType.SIGMOB,
          timeType: 1,
        })
        .then((res) => {
          return res;
        });
    },
  },
  columns: [
    {
      title: '账户ID',
      dataIndex: 'advertiserId',
      fixed: 'left',
      width: 100,
    },
    {
      title: '账户名称',
      dataIndex: 'advertiserName',
      fixed: 'left',
      width: 100,
    },
    {
      title: '投放链接',
      dataIndex: 'linkId',
      fixed: 'left',
      width: 100,
    },
    {
      title: '主体名称',
      dataIndex: 'corporationName',
      width: 100,
    },
    {
      title: '授权状态',
      dataIndex: 'isValid',
      width: 100,
      customRender: {
        type: 'code-to-name',
        props: {
          map: [
            {
              value: 1,
              label: '已授权',
            },
            {
              value: 0,
              label: '未授权',
            },
          ],
        },
      },
    },
    {
      title: '数据更新',
      width: 108,
      headerToolTip: dataUpdateHeaderTooltip,
      dataIndex: 'isOpen',
      customRender: {
        type: 'switch',
        props: {
          confirm: {
            title: '停止数据更新',
            content: '是否确认停止更新该账户数据？',
            times: [0],
          },
          action: (value, record) => {
            return accountCommonApi.batchEdit({
              mediaPlatform: MediaType.SIGMOB,
              advertiserIds: [record.advertiserId],
              isOpen: value || 0,
            });
          },
        },
      },
    },
    getAccountOperModeColumn(),
    {
      title: '代理商',
      dataIndex: 'agentName',
      width: 100,
    },
    {
      title: '所属人员',
      dataIndex: 'optimizationExpert',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createAt',
      width: 168,
    },
    {
      title: '操作',
      width: 140,
      dataIndex: 'operations',
      customRender: {
        type: 'operations',
        props: {
          operations: [
            {
              text: '编辑',
              auth: account.ACCOUNT_SIGMOB_EDIT,
              props: {
                type: 'text',
                disabled: false,
                loading: false,
              },
              clickActionType: 'drawer',
              modal: {
                props: {
                  title: '编辑账户信息',
                  width: 600,
                  fullscreen: false,
                },
                contentType: 'form',
                getDefaultValue: (record) => {
                  currentEditRecord.value = record;
                  updateLinkFlag.value = false;
                  curApplicationId.value = record.applicationId;
                  if (!record.agentName) {
                    record.agentId = null;
                  }
                  return record;
                },
                form: {
                  formSchema: {
                    fields: [
                      {
                        name: 'advertiserName',
                        label: '账户名称',
                        disabled: true,
                        type: 'text',
                      },
                      {
                        name: 'advertiserId',
                        label: '账户ID',
                        disabled: true,
                        type: 'text',
                      },
                      {
                        name: 'corporationName',
                        label: '主体名称',
                        type: 'text',
                        disabled: true,
                      },
                      {
                        name: 'optimizationExpert',
                        label: '所属人员',
                        type: 'text',
                        select: {
                          allowSearch: true,
                        },
                        disabled: true,
                        rules: [{ required: true }],
                      },
                      {
                        name: 'mediaAccountId',
                        label: 'Client ID',
                        type: 'text',
                      },
                      {
                        name: 'mediaAccountKey',
                        label: 'Secret',
                        type: 'text',
                      },
                      {
                        name: 'applicationId',
                        label: '投放产品',
                        type: 'select',
                        format: 'singleSelect',
                        select: {
                          allowSearch: true,
                        },
                        source: {
                          data() {
                            return commonApi.appList().then((res) => {
                              applicationMap.value.clear();

                              return _.map(
                                res.data,
                                ({ appName, id, packageName }) => {
                                  applicationMap.value.set(id, {
                                    appName,
                                    id,
                                    packageName,
                                  });

                                  return {
                                    label: appName,
                                    value: id,
                                  };
                                }
                              );
                            });
                          },
                        },
                        rules: [{ required: true }],
                        onChange(val, formData) {
                          formData.value.linkId = '';
                          curApplicationId.value = val;
                        },
                      },
                      {
                        name: 'linkId',
                        label: '投放链接',
                        type: 'select',
                        format: 'singleSelect',
                        span: 21,
                        select: {
                          allowSearch: true,
                          tail: getTail({
                            configFun: (callback) => {
                              return getCustomLinkConfig(
                                callback,
                                currentEditRecord.value,
                                updateLinkFlag,
                                MediaType.SIGMOB,
                                curApplicationId
                              );
                            },
                            buttonText: '更新链接',
                          }),
                        },
                        source: {
                          data(value, formData) {
                            const id = formData.applicationId;

                            const packageName =
                              applicationMap.value.get(id)?.packageName;

                            if (!packageName) {
                              return [];
                            }

                            return commonApi
                              .getMonitorUrl({
                                pkg: packageName,
                                platform: MediaPlatform.SIGMOB,
                                filterBindAccount: true,
                                applicationId: id,
                              })
                              .then((res) => {
                                if (updateLinkFlag.value) {
                                  formData.linkId =
                                    res.data[res.data.length - 1].linkId;
                                  updateLinkFlag.value = false;
                                }
                                return res.data.map((val) => {
                                  return {
                                    label: `${val.linkId} - ${val.channelTitle}`,
                                    value: val.linkId,
                                  };
                                });
                              });
                          },
                        },
                        rules: [{ required: true }],
                      },
                      getAgentSchema(MediaType.SIGMOB),
                      {
                        name: 'isOpen',
                        label: '数据更新',
                        type: 'switch',
                      },
                      {
                        name: 'remark',
                        label: '备注',
                        type: 'textarea',
                        maxLength: 30,
                        placeholder: '最多30个字符',
                        showWordLimit: true,
                      },
                    ],
                  },
                  defaultFormData: {
                    advertiserName: '',
                    advertiserId: '',
                    secretKey: '',
                    signId: '',
                    applicationId: '',
                    linkId: '',
                    isOpen: '',
                    remark: '',
                  },
                },
                action: async (data: any) => {
                  await accountCommonApi.modify(data.formData);
                  data.refreshTable();
                  Message.success('编辑成功');
                },
              },
            },
            {
              text: '删除',
              auth: account.ACCOUNT_SIGMOB_DELETE,
              props: {
                type: 'text',
                disabled: (data: any) => !!data.record?.isOpen,
              },
              clickActionType: 'modal',
              modal: {
                props: {
                  title: '提示',
                  width: 500,
                  fullscreen: false,
                },
                contentType: 'text',
                text: '是否确认删除所选账户与所属人员绑定关系？',
                action: async (data: any) => {
                  await accountCommonApi.batchRemove({
                    advertiserIds: [data.record.advertiserId],
                    mediaPlatform: MediaType.SIGMOB,
                  });
                  Message.success('删除成功');
                  data.refreshTable();
                },
              },
            },
          ],
        },
      },
    },
  ],
};

export default tableJson;
