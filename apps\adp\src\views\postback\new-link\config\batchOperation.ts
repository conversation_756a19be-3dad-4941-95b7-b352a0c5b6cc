import { ref } from 'vue';
import { pathManage<PERSON>pi } from '@/api/postback/landing';
import { postbackLinkApi, postbackLinkOtherApi } from '@/api/postback/link';
import { Message } from '@arco-design/web-vue';
import { download } from '@/utils/file';
import linkAuth<PERSON>ey from '@/constants/auth-key/postback/link';
import { commonApi } from '@/api/common';
import { getChannelList, getPullPathList } from '@/api/postback/pull-manage';
import getAppId, { getAppPackageName } from '@/hooks/app-id';
import { useAppStore } from '@/store';
import { cloneDeep, get, isNil } from 'lodash';
import { pullTypeOptions } from '@/views/postback/pull-manage/config/constants';
import {
  getDefaultPullVal,
  getPullSubmitVal,
  getQuickApps,
  pullTypeVisibleLoop,
  pullTypeVisibleReplace,
} from '@/views/postback/pull-manage/config/utils';
import postbackAuth from '@/constants/auth-key/postback/core';
import { batchUpdate } from '@/api/postback/core';
import { EditType } from '@/views/postback/core/config/operation';
import { postbackCountApi } from '@/api/postback/count';
import { postbackGeneralApi } from '@/api/postback/general';
import { postbackReplenishApi } from '@/api/postback/replenish';
import { countTypeMap } from '@/views/postback/count/constants';
import { newLinkAuth } from '@/constants/auth-key/postback/new-link';

function getHasCondition({ record, tableData, cb }) {
  const records = tableData?.filter((item) => record?.includes(item.id));
  return records?.every((item) => cb?.(item, records));
}

function getSelectList(tableData, record) {
  const records = tableData?.filter((item) => record?.includes(item.id));
  return records;
}
export const getBatchOperations = (getList: any) => {
  const appStore = useAppStore();
  const appId = getAppId();
  const packageName = getAppPackageName();
  const pullPathList = ref<
    {
      id: number;
      pathName: string;
      hapPath: string;
    }[]
  >([]);
  return [
    {
      text: '修改所属人',
      auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_USER,
      props: {
        type: 'outline',
      },
      clickActionType: 'modal',
      operationPosition: 'batch',
      modal: {
        props: {
          title: '批量修改所属人',
          width: 600,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                label: '所属人',
                name: 'ownerId',
                placeholder: '请选择',
                type: 'select',
                format: 'singleSelect',
                select: {
                  allowSearch: true,
                  allowClear: true,
                  openVirtual: true,
                },
                source: {
                  async data() {
                    const res = await commonApi.getCurrentUserList();
                    return (res?.data ?? []).map((item) => {
                      return {
                        label: item.userName,
                        value: item.id,
                      };
                    });
                  },
                },
                rules: [{ required: true, message: '请选择' }],
              },
            ],
          },
        },
        action: async (value: any) => {
          const { formData, refreshTable } = value;
          const request = { ids: value.record, ...formData };
          await postbackLinkOtherApi.batchUpdate(request);
          Message.success('批量修改成功');
          refreshTable();
        },
      },
    },
    {
      text: '修改唤醒路径',
      auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_PATH,
      props: {
        type: 'outline',
      },
      clickActionType: 'modal',
      operationPosition: 'batch',
      modal: {
        props: {
          title: '批量修改唤醒路径',
          width: 600,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                label: '唤醒路径',
                name: 'pathId',
                placeholder: '请选择',
                type: 'select',
                format: 'singleSelect',
                select: {
                  allowSearch: true,
                  allowClear: true,
                },
                source: {
                  labelKey: 'pathName',
                  valueKey: 'id',
                  data: async () => {
                    return pathManageApi
                      .mapList({ applicationId: appId.value })
                      .then((res) => {
                        return res.data;
                      });
                  },
                },
                rules: [{ required: true, message: '请选择' }],
              },
            ],
          },
        },
        action: async (value: any) => {
          const { formData, refreshTable } = value;
          const request = { ids: value.record, ...formData };
          await postbackLinkOtherApi.batchUpdate(request);
          Message.success('批量修改成功');
          refreshTable();
        },
      },
    },
    {
      text: '修改计数数字',
      auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_COUNT,
      props: {
        type: 'outline',
        disabled: ({ record, tableData }) => {
          const records = tableData?.filter((item) =>
            record?.includes(item.id)
          );
          const isValueAndNumber = records?.every((item) =>
            [countTypeMap.VALUE.value, countTypeMap.NUMBER.value].includes(
              item.countType
            )
          );
          if (!isValueAndNumber) {
            return true;
          }
          return false;
        },
      },
      operationPosition: 'batch',
      clickActionType: 'modal',
      modal: {
        props: {
          title: '修改计数数字',
          width: 500,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                label: '计数数字',
                name: 'num',
                span: 24,
                type: 'number',
                required: true,
                precision: 4,
                placeholder: '最多4位小数',
              },
            ],
          },
        },
        action: async (value) => {
          const { formData, refreshTable, record } = value;

          await postbackLinkOtherApi.batchModify({
            idList: record,
            value: `${formData.num}`,
            opType: 'countValue',
          });
          refreshTable();
        },
      },
    },
    {
      text: '删除',
      auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_DELETE,
      props: {
        type: 'outline',
        disabled: ({ record, tableData }) => {
          const res = tableData
            .filter((tableDataItem) => {
              return record.find(
                (selectedItem) => selectedItem === tableDataItem.id
              );
            })
            .every((resItem) => resItem.transmitStatus !== 1);
          return !res;
        },
        loading: false,
      },
      operationPosition: 'batch',
      clickActionType: 'modal',
      linkUrl: '',
      modal: {
        props: {
          title: '提示',
          width: 400,
          fullscreen: false,
        },
        contentType: 'text',
        text: '确认批量删除选中的链接吗?',
        action: async (value: any) => {
          const { refreshTable } = value;
          const request = { ids: value.record };
          await postbackLinkApi.remove?.(request);
          Message.success('批量删除成功');
          refreshTable();
        },
      },
    },
    {
      text: '修改拉起产品',
      auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_PULL_APP,
      props: {
        type: 'outline',
      },
      clickActionType: 'modal',
      operationPosition: 'batch',
      modal: {
        props: {
          title: '修改拉起产品',
          width: 600,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                label: '拉起方式',
                name: 'pullType',
                type: 'select',
                format: 'buttonRadio',
                required: true,
                source: {
                  data: () => pullTypeOptions,
                },
              },
              {
                label: '拉起产品',
                name: 'pullAppId',
                placeholder: '请选择',
                type: 'select',
                format: 'singleSelect',
                select: {
                  allowSearch: true,
                  allowClear: true,
                  openVirtual: true,
                },
                source: {
                  labelKey: 'appName',
                  valueKey: 'id',
                  data: () => getQuickApps({ hasCurrentApp: false }),
                },
                onChange: (value, formData) => {
                  formData.value.pullLandPath = undefined;
                  formData.value.pullChannelCode = undefined;
                },
                rules: [{ required: true, message: '请选择' }],
                visibleOn: (val, formVal) => pullTypeVisibleReplace(formVal),
              },
              {
                label: '拉起产品',
                name: 'multiplePullAppIds',
                placeholder: '请选择',
                type: 'select',
                format: 'multipleSelect',
                select: {
                  allowSearch: true,
                  allowClear: true,
                  openVirtual: true,
                  limit: 10,
                },
                source: {
                  labelKey: 'appName',
                  valueKey: 'id',
                  data: () => getQuickApps({ hasCurrentApp: false }),
                },
                rules: [{ required: true, message: '请选择' }],
                visibleOn: (val, formVal) => pullTypeVisibleLoop(formVal),
              },
              {
                label: '拉起路径',
                name: 'pullLandPath',
                placeholder: '请选择',
                type: 'select',
                format: 'singleSelect',
                select: {
                  allowSearch: true,
                  allowClear: true,
                },
                source: {
                  data: async (_value, _formData) => {
                    const productId = get(_formData, 'pullAppId');
                    if (!productId) {
                      return [];
                    }
                    const { data } = await getPullPathList({
                      applicationId: productId,
                    });
                    pullPathList.value = data;
                    return data.map((item) => {
                      return {
                        label: item.pathName,
                        value: item.pathName,
                      };
                    });
                  },
                },
                rules: [{ required: true, message: '请选择' }],
                visibleOn: (val, formVal) => pullTypeVisibleReplace(formVal),
              },
              {
                label: '拉起渠道',
                name: 'pullChannelCode',
                placeholder: '请选择',
                type: 'select',
                format: 'singleSelect',
                select: {
                  allowSearch: true,
                  allowClear: true,
                },
                source: {
                  data: async (_value, _formData) => {
                    const productId = get(_formData, 'pullAppId');
                    if (!productId) return [];
                    const pullAppPkg = appStore.appInfo.appList.find(
                      (item) => item.appId === productId
                    )?.packageName;
                    const { data } = await getChannelList({
                      pkg: pullAppPkg,
                    });
                    return data.map((item) => {
                      return {
                        label: `${item.channelName}_${item.channelCode}`,
                        value: item.channelCode,
                      };
                    });
                  },
                },
                rules: [{ required: true, message: '请选择' }],
                visibleOn: (val, formVal) => pullTypeVisibleReplace(formVal),
              },
            ],
          },
        },
        getDefaultValue: async (record: any) => {
          return getDefaultPullVal();
        },
        action: async ({ formData, refreshTable, record }: any) => {
          const tableData = getList();
          const linkIds = tableData
            .filter((item) => record.includes(item.id))
            .map((item) => item.linkId);
          const request = {
            sourceLinkIds: linkIds,
            ...getPullSubmitVal({ formData, pullPathList: pullPathList.value }),
          };
          await postbackLinkOtherApi.batchUpdatePullApp(request);
          Message.success('批量修改成功');
          refreshTable();
        },
      },
    },
    // {
    //   text: '修改计数策略',
    //   auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_COUNT_TACTIC,
    //   props: {
    //     type: 'outline',
    //     disabled: ({ record, tableData }) => {
    //       return !getHasCondition({
    //         record,
    //         tableData,
    //         cb: (item, selectTables) =>
    //           !isNil(item.channelTypeId) &&
    //           item.channelTypeId === selectTables[0].channelTypeId,
    //       });
    //     },
    //   },
    //   clickActionType: 'modal',
    //   operationPosition: 'batch',
    //   modal: {
    //     props: {
    //       title: '批量修改计数策略',
    //       width: 600,
    //       fullscreen: false,
    //     },
    //     contentType: 'form',
    //     form: {
    //       formSchema: {
    //         fields: [
    //           {
    //             label: '计数策略',
    //             name: 'countStrategyId',
    //             type: 'select',
    //             format: 'singleSelect',
    //             select: {
    //               allowSearch: true,
    //               allowClear: true,
    //               openVirtual: true,
    //             },
    //             source: {
    //               labelKey: 'strategyName',
    //               valueKey: 'id',
    //               data: () => {
    //                 return postbackCountApi
    //                   .simpleList({ appId: appId.value, status: 1 })
    //                   .then((res) => {
    //                     const list = res?.data ?? [];
    //                     return list;
    //                   });
    //               },
    //             },
    //             required: true,
    //           },
    //         ],
    //       },
    //     },
    //     action: async (value: any) => {
    //       const { formData, refreshTable } = value;
    //       await postbackLinkOtherApi.batchModify({
    //         idList: value.record,
    //         value: `${formData.countStrategyId}`,
    //         opType: 'countStrategy',
    //       });
    //       Message.success('批量修改成功');
    //       refreshTable();
    //     },
    //   },
    // },
    {
      text: '修改通用策略',
      auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_GENERAL_TACTIC,
      props: {
        type: 'outline',
        disabled: ({ record, tableData }) => {
          return !getHasCondition({
            record,
            tableData,
            cb: (item, selectTables) =>
              !isNil(item.channelTypeId) &&
              item.channelTypeId === selectTables[0].channelTypeId,
          });
        },
      },
      clickActionType: 'modal',
      operationPosition: 'batch',
      modal: {
        props: {
          title: '批量修改通用策略',
          width: 600,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                label: '通用策略',
                name: 'transmitStrategyId',
                type: 'select',
                format: 'singleSelect',
                select: {
                  allowSearch: true,
                  allowClear: true,
                  openVirtual: true,
                },
                source: {
                  labelKey: 'strategyName',
                  valueKey: 'id',
                  data: (_, form) => {
                    const channelTypeId = form.list?.[0]?.channelTypeId;
                    if (!channelTypeId) {
                      return [];
                    }
                    const { curApp } = appStore.appInfo;
                    return postbackGeneralApi
                      .simpleList({
                        status: 1,
                        appGroup: curApp?.appGroup,
                        departmentCode: curApp?.departmentCode,
                      })
                      .then((res) => {
                        return res?.data ?? [];
                      });
                  },
                },
                onChange(_, formData) {},
                required: true,
              },
            ],
          },
        },
        getDefaultValue: (record) => {
          return {
            list: getSelectList(getList(), record),
          };
        },
        action: async (value: any) => {
          const { formData, refreshTable } = value;

          await postbackLinkOtherApi.batchModify({
            idList: value.record,
            value: `${formData.transmitStrategyId}`,
            opType: 'generalStrategy',
          });
          Message.success('批量修改成功');
          refreshTable();
        },
      },
    },
    // {
    //   text: '修改补回传策略',
    //   auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_REPLENISH_TACTIC,
    //   props: {
    //     type: 'outline',
    //     disabled: ({ record, tableData }) => {
    //       return !getHasCondition({
    //         record,
    //         tableData,
    //         cb: (item, selectTables) =>
    //           !isNil(item.channelTypeId) &&
    //           item.channelTypeId === selectTables?.[0]?.channelTypeId,
    //       });
    //     },
    //   },
    //   clickActionType: 'modal',
    //   operationPosition: 'batch',
    //   modal: {
    //     props: {
    //       title: '批量修改补回传策略',
    //       width: 600,
    //       fullscreen: false,
    //     },
    //     contentType: 'form',
    //     form: {
    //       formSchema: {
    //         fields: [
    //           {
    //             label: '补回传策略',
    //             name: 'supplyList',
    //             type: 'select',
    //             format: 'multipleSelect',
    //             select: {
    //               allowSearch: true,
    //               allowClear: true,
    //               openVirtual: true,
    //               valueKey: 'id',
    //             },
    //             source: {
    //               data: (_, form) => {
    //                 const channelTypeId = form.list?.[0]?.channelTypeId;
    //                 if (!channelTypeId) {
    //                   return [];
    //                 }
    //                 return postbackReplenishApi
    //                   .simpleList({
    //                     pkg: packageName.value,
    //                     channelTypeId,
    //                     status: 1,
    //                   })
    //                   .then((res) => {
    //                     return (res?.data ?? []).map((item) => {
    //                       return {
    //                         label: item.strategyName,
    //                         value: {
    //                           name: item.strategyName,
    //                           id: item.id,
    //                         },
    //                       };
    //                     });
    //                   });
    //               },
    //             },
    //             onChange(_, formData) {},
    //             required: true,
    //           },
    //         ],
    //       },
    //     },
    //     getDefaultValue: (record) => {
    //       return {
    //         list: getSelectList(getList(), record),
    //         supplyList: [],
    //       };
    //     },
    //     action: async (value: any) => {
    //       const { formData, refreshTable } = value;
    //       await postbackLinkOtherApi.batchModify({
    //         idList: value.record,
    //         value: formData.supplyList.map((item) => item.id).join(','),
    //         opType: 'supplyStrategy',
    //       });
    //       Message.success('批量修改成功');
    //       refreshTable();
    //     },
    //   },
    // },
    {
      text: '导出',
      auth: newLinkAuth.POSTBACK_NEW_LINK_BATCH_EXPORT,
      props: {
        type: 'outline',
      },
      clickActionType: 'action',
      operationPosition: 'batch',
      action: async (data: any) => {
        download('/transmit/link/v1/export', `ids=${data.record.join(',')}`);
      },
    },
  ];
};

export default getBatchOperations;
