<template>
  <div class="home-table">
    <a-table
      class="table-instances"
      :loading="loading"
      :columns="batchColumns"
      :data="tableData"
      :scroll="scroll"
      :scroll-bar="false"
      :pagination="pagination"
      @page-size-change="handlePageSizeChange"
      @page-change="handlePageChange"
    >
      <template #toolName="{ record }">
        <div style="display: flex; align-items: center">
          <span> {{ record.toolName }}</span>
          <a-tooltip
            :content="formatAiUtilsName(record.aiLabels)"
            v-if="record.aiLabels"
          >
            <AI
              class="icon-size"
              :style="{
                width: '14px',
                height: '14px',
                marginLeft: '4px',
              }"
            />
          </a-tooltip>
        </div>
      </template>
      <template #status="{ record }">
        <StatusText :type="record.statusType" hide-text-color>
          {{ record.statusName }}
        </StatusText>
      </template>
      <template #synTime="{ record }">
        {{ record.synTime || '-' }}
      </template>
      <template #taskStatus="{ record }">
        <a-space size="medium">
          <!-- <StatusText hide-dot hide-text-color>
            总数:{{ record.commitNum || '-' }}
          </StatusText> -->
          <StatusText type="success" hide-text-color>
            已完成:{{ record.successNum || '-' }}
          </StatusText>
          <!-- <StatusText type="success" hide-text-color>
            已提审:{{ record.checkedNum || '-' }}
          </StatusText> -->
          <StatusText type="error" hide-text-color>
            失败:{{ record.failNum || '-' }}
          </StatusText>
        </a-space>
      </template>
      <template #operations="{ record }">
        <!-- 草稿状态不展示 提审中禁用，已完成可操作-->
        <a-button
          v-if="record.status !== statusTypeEnum.draft"
          @click="handleView(record)"
          type="text"
          :disabled="
            record.status === statusTypeEnum.wait ||
            record.status === statusTypeEnum.terminated
          "
        >
          查看
        </a-button>
        <a-button
          type="text"
          status="danger"
          v-if="record.status === statusTypeEnum.wait"
          @click="handleTerminate(record)"
          :loading="record.terminateLoading"
        >
          终止任务
        </a-button>
        <template v-else>
          <a-button
            v-if="
              record.status === statusTypeEnum.completed ||
              record.status === statusTypeEnum.pushDone ||
              record.status === statusTypeEnum.pushing
            "
            @click="handleTaskReview({ ...record, materialIds: [] })"
            :disabled="
              record.status === statusTypeEnum.pushing ||
              record.status === statusTypeEnum.pushDone ||
              !(record.pushFailNum > 0)
            "
            type="text"
          >
            批量推送
          </a-button>
        </template>
        <a-button
          v-if="
            record.status === statusTypeEnum.completed ||
            record.status === statusTypeEnum.pushDone ||
            record.status === statusTypeEnum.pushing
          "
          @click="handleDownload(record)"
          type="text"
          :loading="record.downloadLoading"
        >
          下载
        </a-button>
      </template>
    </a-table>
    <ViewContentDrawer
      v-model="viewVisible"
      v-bind="viewOptions"
      @refresh="loadData"
    />
    <ReviewTaskModal
      v-model="reviewTaskVisible"
      v-bind="reviewTaskOptions"
      @refresh="loadData"
    />
  </div>
</template>

<script setup lang="tsx">
  import { inject, onMounted, ref } from 'vue';
  import useTablePage from '@/hooks/use-table-page';
  import { Message } from '@arco-design/web-vue';
  import { batchCreateApi } from '@/api/sc-home/batch-create';
  import {
    batchColumns,
    homeContext,
    statusTypeEnum,
    statusTypeList,
  } from '@/views/sc-home/utils';
  import StatusText from '@/components/status-text/index.vue';
  import useLoading from '@/hooks/loading';
  import useViewTask from '@/views/sc-home/hook/view-task';
  import useReviewTask from '@/views/sc-home/hook/review-task';
  import { formatAiUtilsName } from '@/utils/ai-utils';
  import AI from '@/components/container/resource/svg/AI.svg?component';

  import ViewContentDrawer from './batch-view-content-drawer.vue';
  import ReviewTaskModal from './batch-review-task-modal.vue';

  const {
    pagination,
    currentPage,
    handlePageSizeChange,
    handlePageChange,
    resetPage,
  } = useTablePage(loadData);
  const scroll = {
    x: '100%',
    y: '100%',
  };

  const tableData = ref();

  const homeInstances = inject<any>(homeContext);

  const { loading, setLoading } = useLoading();
  function loadData() {
    if (loading.value) return;
    const search = homeInstances?.getBatchSearch?.() ?? {};
    const params = {
      ...search,
      ...currentPage.value,
    };
    setLoading(true);
    batchCreateApi
      .taskList(params)
      .then((res) => {
        tableData.value = (res.data?.list || []).map((item) => {
          const statusItem = statusTypeList.find(
            (v) => v.value === item.status
          );
          return {
            ...item,
            toolName: '导入合成/解压小说',
            statusName: statusItem?.label ?? '',
            statusType: statusItem?.type ?? '',
          };
        });
        pagination.total = res.data?.pageInfo?.total || 0;
      })
      .finally(() => {
        setLoading(false);
      });
  }

  // 查看
  const { handleView, viewVisible, viewOptions } = useViewTask();
  // 提审
  const { handleTaskReview, reviewTaskVisible, reviewTaskOptions } =
    useReviewTask();
  // 终止任务
  async function handleTerminate(record) {
    record.terminateLoading = true;
    try {
      await batchCreateApi.terminate({
        ids: [record.id],
      });
      Message.success('终止任务成功');
      loadData();
      record.terminateLoading = false;
    } catch (error) {
      loadData();
      record.terminateLoading = false;
    } finally {
      record.terminateLoading = false;
    }
  }
  const handleDownload = async (record) => {
    record.downloadLoading = true;
    try {
      const { data, code } = await batchCreateApi.download({
        taskId: record.id,
        taskName: record.taskName || undefined,
      });
      if (code === 1) {
        Message.success('提交成功');
        record.downloadLoading = false;
      } else {
        Message.error(data.message);
        record.downloadLoading = false;
      }
    } catch (error) {
      record.downloadLoading = false;
    }
  };
  onMounted(() => {
    loadData();
  });
  defineExpose({
    loadData,
    resetPage,
  });
</script>

<style scoped lang="less">
  .home-table {
    overflow: hidden;
    flex: 1;
    padding-bottom: 16px;

    .table-instances {
      height: 100%;
    }
  }
</style>
