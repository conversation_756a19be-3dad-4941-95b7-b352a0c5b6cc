import type { DButton } from '@repo/kola';
import useAppId from '@/hooks/app-id';
import accountsApi from '@/api/promotion/kuaishou/accounts';
import { targetPackageApi } from '@/api/property-manage';
import ksPackageAuth from '@/constants/auth-key/property/ks/package';
import { Message } from '@arco-design/web-vue';

const titleText = '获取快手定向包';

const appId = useAppId();
const operation: DButton[] = [
  {
    text: titleText,
    auth: ksPackageAuth.PROPERTY_KS_PACKAGE_MEDIA_CREATE,
    props: {
      type: 'outline',
    },
    clickActionType: 'modal',
    modal: {
      props: {
        title: titleText,
        width: '1000px',
        fullscreen: false,
      },
      contentType: 'form',
      form: {
        formSchema: {
          fields: [
            {
              label: '选择媒体账户',
              name: 'accountList',
              span: 24,
              type: 'transferSimple',
              sourceTitle: ({ countTotal }) => `媒体账户(${countTotal})`,
              targetTitle: ({ countTotal }) => `已选(${countTotal})`,
              required: true,
              remote: true,
              // rules: [{ maxLength, message: `最多选择${maxLength}个账户` }],
              source: {
                data: async (_field, formData, _path, text) => {
                  return accountsApi
                    .getAccountList({
                      applicationId: appId.value,
                      pageNum: 1,
                      pageSize: 100,
                      keyWord: text,
                    })
                    .then((res) => {
                      return (res.data.list || []).map((item) => {
                        return {
                          label: `${item.advertiserName}(${item.advertiserId})`,
                          value: item.advertiserId,
                        };
                      });
                    });
                },
              },
              rules: [{ required: true }],
            },
          ],
        },
      },
      action: async ({ formData, refreshTable }: any) => {
        const productId = useAppId();
        const params = {
          channelId: 100,
          productId: productId.value,
          advertiserIds: formData.accountList,
        };
        await targetPackageApi.syncData(params);
        Message.success('同步成功');
        refreshTable();
      },
    },
  },
];
export default operation;
