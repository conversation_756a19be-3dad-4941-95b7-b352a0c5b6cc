import { MediaType } from '@/views/promotion/hooks/authorization';
import { uniqueId } from 'lodash';
import { commonApi } from '@/api/common';
import { useUserStore } from '@/store';
import dayjs from 'dayjs';
import { getAgentSchema } from '../common';

// eslint-disable-next-line import/prefer-default-export
export function getFormSchema() {
  return {
    fields: [
      {
        label: '账户ID',
        name: 'advertiserId',
        type: 'text',
        required: true,
        maxLength: 100,
      },
      {
        label: '账户名称',
        name: 'advertiserName',
        type: 'text',
        required: true,
        maxLength: 100,
      },
      {
        label: 'Client ID',
        name: 'mediaAccountId',
        type: 'text',
        required: true,
        maxLength: 100,
      },
      {
        label: 'Secret',
        name: 'mediaAccountKey',
        type: 'text',
        required: true,
        maxLength: 100,
      },
      {
        label: '投放链接',
        name: 'linkId',
        type: 'select',
        format: 'singleSelect',
        select: {
          allowClear: true,
          allowSearch: true,
        },
        source: {
          data: async () => {
            const res = await commonApi.getMonitorUrl({
              pkg: window.$appInfo.packageName,
              platform: 'SIGMOB',
              filterBindAccount: true,
              applicationId: window.$appInfo?.id,
            });
            return res.data.map((item) => {
              return {
                label: `${item.linkId} - ${item.channelTitle}`,
                value: item.linkId,
              };
            });
          },
        },
      },
      {
        label: '所属人员',
        name: 'ownerId',
        type: 'text',
        required: true,
        disabled: true,
      },
      {
        label: '主体名称',
        name: 'corporationName',
        type: 'text',
        required: true,
      },
      getAgentSchema(MediaType.SIGMOB),
      {
        label: '备注',
        name: 'remark',
        type: 'text',
        maxLength: 30,
        placeholder: '请输入账户备注',
      },
    ],
  };
}

const userStore = useUserStore();

export function getFormData() {
  return {
    advertiserId: '',
    advertiserName: '',
    mediaAccountKey: '',
    mediaAuthKey: '',
    linkId: '',
    agentId: '',
    ownerId: userStore.userName,
    remark: '',
    applicationId: window.$appInfo?.id,
    mediaPlatform: MediaType.SIGMOB,
    sessionKey: uniqueId(`sigmob_${dayjs().unix()}_`),
  };
}
