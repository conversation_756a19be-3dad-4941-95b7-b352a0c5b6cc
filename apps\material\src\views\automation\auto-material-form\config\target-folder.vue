<template>
  <div>
    <TreeSelectField
      v-if="formData.source === 2"
      v-model="formData.targetDir"
      :field="treeConfig"
      :path="''"
    />
    <a-form-item
      label="素材目录"
      field="targetSelectCatalog"
      :rules="rule"
      v-if="formData.source === 3"
    >
      <DirSelect
        v-model="formData.targetSelectCatalog"
        :params="{ disabledCreate: true, multiple: true }"
      />
    </a-form-item>
    <div
      class="filter-box"
      v-if="formData.source === 3 || formData.source === 2"
    >
      <div class="label">素材指标筛选</div>
      <div class="content">
        <a-form-item label="选择创建时间" field="createdDateRange">
          <a-range-picker
            v-model="formData.createdDateRange"
            style="width: 360px"
            format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            shortcuts-position="right"
            :shortcuts="rangeShortcuts"
          />
        </a-form-item>
        <a-form-item
          label="选择媒体渠道"
          field="mediaCode"
          :rules="[
            {
              required: true,
              message: '请选择媒体渠道',
            },
          ]"
        >
          <a-select
            v-model="formData.mediaCode"
            style="width: 360px"
            placeholder="请选择媒体渠道"
            :options="CONSTANT.mediaCodeOptions"
            @change="handleChange"
          />
        </a-form-item>
        <ConditionList
          v-model="formData.folderFilter"
          :first-options="planFirstOptions"
          :second-options="secondOptions"
          :third-options="thirdOptions"
          path="folderFilter"
          :min-length="1"
          can-add
          can-delete
        ></ConditionList>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
  import { nextTick, provide, computed } from 'vue';
  import dayjs from 'dayjs';
  import { Message } from '@arco-design/web-vue';
  import { useTrees } from '@/hooks/material-tree';
  import TreeSelectField from '@repo/kola/src/components/dynamic/form/widgets/tree-select-field/index.vue';
  import DirSelect from './components/dir-select.vue';
  import ConditionList from './components/condition-list.vue';
  import {
    CONSTANT,
    VALUE_MAP,
    COMPARE_MAP,
    FOLDER_FILTER_OPTIONS_MAP,
  } from './constant';

  const rangeShortcuts = [
    {
      label: '今天',
      value: () => [dayjs().startOf('day'), dayjs().endOf('day')],
    },
    {
      label: '昨天',
      value: () => [
        dayjs().subtract(1, 'day').startOf('day'),
        dayjs().subtract(1, 'day').endOf('day'),
      ],
    },
    {
      label: '近7天',
      value: () => [
        dayjs().subtract(6, 'day').startOf('day'),
        dayjs().endOf('day'),
      ],
    },
    {
      label: '近一个月',
      value: () => [
        dayjs().subtract(1, 'month').startOf('day'),
        dayjs().endOf('day'),
      ],
    },
    {
      label: '近三个月',
      value: () => [
        dayjs().subtract(3, 'month').startOf('day'),
        dayjs().endOf('day'),
      ],
    },
    {
      label: '近半年',
      value: () => [
        dayjs().subtract(6, 'month').startOf('day'),
        dayjs().endOf('day'),
      ],
    },
    {
      label: '近一年',
      value: () => [
        dayjs().subtract(1, 'year').startOf('day'),
        dayjs().endOf('day'),
      ],
    },
  ];

  const formData = defineModel<any>();
  provide('formData', formData);
  const { trees } = useTrees();
  const rule = [
    {
      required: true,
      validator: async (value, cb) => {
        await nextTick();
        const { albumId, albumName, folder } = value;

        if (
          albumId &&
          albumName &&
          folder.length > 0 &&
          folder[0].label &&
          folder[0].value
        ) {
          cb();
        } else {
          cb('请选择素材目录');
        }
      },
      trigger: 'change',
    },
  ];
  const treeConfig = {
    label: '素材目录',
    name: 'targetDir',
    type: 'treeSelect',
    placeholder: '请选择',
    select: {
      allowSearch: true,
      labelInValue: true,
      fullPath: true,
      multiple: true,
      checkable: true,
      treeCheckStrictly: true,
      customTitle: (data) => {
        return <span>{data.title}</span>;
      },
    },
    source: {
      valueKey: 'key',
      labelKey: 'path',
      data: trees,
    },
    setter: (val, _form) => {
      if (val.length > 10) {
        Message.warning('最多选择10个目录');
        return val.splice(0, 10);
      }
      return val;
    },
    rules: [
      {
        required: true,
        validator: (val, cb) => {
          if (!val || val.length === 0) {
            cb('请选择素材目录');
          } else {
            cb();
          }
        },
        trigger: 'change',
      },
    ],
  };
  const planFirstOptions = computed(() => {
    return {
      field: 'field',
      placeholder: '指标维度下拉选择',
      options: FOLDER_FILTER_OPTIONS_MAP[formData.value.mediaCode],
      required: true,
      message: '请选择维度',
    };
  });
  function disabledDate(current: any) {
    const today = dayjs();
    const currentDate = dayjs(current);
    const oneYearAgo = today.subtract(1, 'year');
    // 禁用大于今天的日期和小于一年前的日期
    return (
      currentDate &&
      (currentDate.isAfter(today, 'day') ||
        currentDate.isBefore(oneYearAgo, 'day'))
    );
  }
  const secondOptions = {
    field: 'condition',
    placeholder: '选择关系',
    options: COMPARE_MAP,
    required: true,
    message: '请选择关系',
  };
  const thirdOptions = {
    field: 'value',
    placeholder: '选择筛选值',
    options: VALUE_MAP,
    required: true,
    message: '筛选值必填',
  };
  function handleChange() {
    formData.value.folderFilter = [
      {
        field: '',
        condition: '',
        value: '',
      },
    ];
  }
</script>

<style lang="less" scoped>
  .filter-box {
    display: flex;

    .label {
      width: 120px;
      font-size: 13px;
      color: rgb(78 89 105);
      text-align: left;
    }
    // .label::before {
    //   content: '*';
    //   color: rgb(245, 63, 63);
    //   font-size: 16px;
    //   vertical-align: middle;
    // }
    .content {
      width: 100%;
    }
  }
</style>
