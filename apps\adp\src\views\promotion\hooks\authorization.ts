// eslint-disable-next-line no-shadow
import promotionApi from '@/api/promotion';
import useAppStore from '@/store/modules/app';
import useMediaRedirectState from '@/hooks/media-redirect-state';

// eslint-disable-next-line no-shadow
export enum MediaType {
  KUAI_SHOU = 100,
  TOU_TIAO = 101,
  GDT = 102,
  BAI_DU = 103,
  HUA_WEI = 104,
  VIVO = 105,
  OPPO = 106,
  XIAOMI = 107,
  UC = 108,
  QU_TOU_TIAO = 109,
  WEI_BO = 111,
  ALIPAY = 110,
  WIFI = 112,
  HONOR = 114,
  IQIYI = 118,
  SIGMOB = 116,
}

export const mediaPlatformMap = {
  [MediaType.TOU_TIAO]: 'TOUTIAO',
  [MediaType.KUAI_SHOU]: 'KUAISHOU',
  [MediaType.GDT]: 'GDT',
  [MediaType.BAI_DU]: 'BAIDU',
  [MediaType.HUA_WEI]: 'HUAWEI',
  [MediaType.VIVO]: 'VIVO',
  [MediaType.OPPO]: 'OPPO',
  [MediaType.XIAOMI]: 'XIAOMI',
  [MediaType.UC]: 'UC',
  [MediaType.QU_TOU_TIAO]: 'QUTOUTIAO',
  [MediaType.WEI_BO]: 'WEIBO',
  [MediaType.ALIPAY]: 'ALIPAY',
  [MediaType.WIFI]: 'WIFI',
  [MediaType.HONOR]: 'HONOR',
  [MediaType.IQIYI]: 'IQIYI',
  [MediaType.SIGMOB]: 'SIGMOB',
};
const { parseMediaStateUrl } = useMediaRedirectState();

export const useAuthorization = () => {
  const openAuthPage = async (mediaType: MediaType, type?: number) => {
    let platform = 'TOUTIAO';
    const otherQuery: any = {};
    const appStore: any = useAppStore();
    switch (mediaType) {
      case MediaType.TOU_TIAO:
        platform = 'TOUTIAO';
        break;
      case MediaType.KUAI_SHOU:
        otherQuery.groupName =
          appStore.appInfo?.appList.find(
            (appItem: any) => appItem.id === window.$appInfo?.id
          )?.departmentCode ?? '';
        otherQuery.appId = window.$appInfo?.id;
        otherQuery.departmentCode = window.$appInfo?.departmentCode;
        otherQuery.oauthType = type === 0 ? 'advertiser' : 'agent';
        platform = 'KUAISHOU';
        break;
      case MediaType.GDT:
        platform = 'GDT';
        break;
      case MediaType.BAI_DU:
        platform = 'BAIDU';
        break;
      case MediaType.HUA_WEI:
        platform = 'HUAWEI';
        break;
      case MediaType.VIVO:
        platform = 'VIVO';
        break;
      case MediaType.OPPO:
        platform = 'OPPO';
        break;
      case MediaType.XIAOMI:
        platform = 'XIAOMI';
        break;
      case MediaType.UC:
        platform = 'UC';
        break;
      case MediaType.QU_TOU_TIAO:
        platform = 'QUTOUTIAO';
        break;
      case MediaType.WEI_BO:
        platform = 'WEIBO';
        break;
      case MediaType.IQIYI:
        platform = 'IQIYI';
        break;
      default:
        break;
    }
    const query = {
      platformEnum: platform,
      companyCode: appStore.appInfo?.curApp?.companyCode,
      ...otherQuery,
    };
    const { data: url } = await promotionApi.getAuthUrl(query);
    const parseUrl = parseMediaStateUrl({
      url,
      platform,
    });
    window.open(parseUrl);
  };

  return { openAuthPage };
};
