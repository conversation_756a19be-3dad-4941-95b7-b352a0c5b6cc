import { computed } from 'vue';
import { columnCodeMap } from '@/views/tool/auto-rule/rule-manage/constants';
import { find, isNil } from 'lodash';
import {
  getQualityReportColumns,
  getApkReportColumns,
  getFictionReportColumns,
} from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/utils/field-columns';

export function useFieldColumns({
  allFormData,
  quantityFieldsCodes,
  modelValue,
  currentPathMap,
  checkOnlyMedia,
  checkMediaMap,
  checkMedias,
}) {
  const apkColumns = computed(() => {
    const { ruleTarget } = allFormData.value;
    return getApkReportColumns({
      ruleTarget,
    });
  });
  const qualityColumns = computed(() => {
    const { ruleTarget } = allFormData.value;
    const list = getQualityReportColumns({
      ruleTarget,
      quantityFieldsCodes: quantityFieldsCodes.value,
      beforeDay: modelValue.value.beforeDay,
      currentPathMap: currentPathMap.value,
      checkMediaMap: checkMediaMap.value,
    });
    const adCloseTimeColumn = find(list, {
      value: columnCodeMap.adCloseTime,
    });
    if (!isNil(adCloseTimeColumn)) {
      adCloseTimeColumn.disabled = modelValue.value.conditionItems.some(
        (item) => item.columnCode === columnCodeMap.adCloseTime
      );
    }
    return list;
  });

  const fictionColumns = computed(() => {
    const { ruleTarget } = allFormData.value;
    return getFictionReportColumns({
      currentPathMap: currentPathMap.value,
      checkOnlyMedia: checkOnlyMedia.value,
      ruleTarget,
      beforeDay: modelValue.value.beforeDay,
      checkMedias: checkMedias.value,
    });
  });

  return {
    apkColumns,
    qualityColumns,
    fictionColumns,
  };
}
