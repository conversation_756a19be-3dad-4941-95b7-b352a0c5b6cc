import { Image } from '@arco-design/web-vue';
import { ref, h } from 'vue';
import { commonApi } from '@/api/common';
import { NodeType } from '@/constants/material';
import folderIconUrl from '@/assets/images/folder.png';
import { useCacheInfo } from '@/views/sc-home/hook/cache-info';

const { getCacheInfo } = useCacheInfo();
const trees = ref([]);
const albumList = ref<any[]>([]);
const currentAlbumId = ref('');
export const useClTree = () => {
  let defaultList: any = [];
  const root = {
    title: '全部专辑',
    key: NodeType.All,
    type: NodeType.All,
  };

  const icon = () =>
    h(Image, { src: folderIconUrl, width: 16, preview: false });

  const recursionTreeNode = (
    originNodes,
    targetNodes: Node[] = [],
    parent,
    path = ''
  ) => {
    originNodes.forEach((node) => {
      const { type, name, id } = node;
      const nodePath = path ? `${path}/${name}` : name; // 新增路径逻辑

      const nodeData: any = {
        title: name,
        icon,
        key: id,
        children: [],
        type,
        parent,
        path: nodePath,
        meta: node,
      };
      targetNodes.push(nodeData);
      if (node.child) {
        recursionTreeNode(node.child, nodeData.children, nodeData, nodePath);
      }
    });
  };
  const updateTrees = async () => {
    if (currentAlbumId.value) {
      const { data } = await commonApi.getFoldersList(currentAlbumId.value);
      const treeList = [];
      recursionTreeNode(data, treeList, root);
      trees.value = treeList;
    } else {
      trees.value = [];
    }
  };
  const updateFolderList = async (search: string | undefined = undefined) => {
    const {
      path: { albumId: defaultId, albumName: defaultName },
    } = getCacheInfo();
    defaultList = [
      {
        id: defaultId,
        name: defaultName,
      },
    ];
    if (defaultId && defaultName && !search) {
      albumList.value = [
        {
          id: defaultId,
          name: defaultName,
        },
      ];
      return [
        {
          id: defaultId,
          name: defaultName,
        },
      ];
    }
    if (albumList.value.length === 0) {
      const { data } = await commonApi.getAlbumsList();
      albumList.value = data;
    }
    if (search) {
      return albumList.value.filter((item: any) => item.name.includes(search));
    }
    return [];
  };

  const findNodeByKey = (key: string, tree) => {
    for (let i = 0; i < tree.length; i += 1) {
      const node = tree[i];
      if (node.key === key) return node;
      const find = findNodeByKey(key, node.children);
      if (find) return find;
    }

    return null;
  };

  const findTreeNodeByKey = (key: string) => {
    return findNodeByKey(key, trees.value);
  };

  return {
    trees,
    albumList,
    currentAlbumId,
    defaultList,
    updateTrees,
    updateFolderList,
    findTreeNodeByKey,
  };
};
export default useClTree;
