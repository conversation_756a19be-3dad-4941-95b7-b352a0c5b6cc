<template>
  <PortalPage
    class="adp-home"
    title="首页"
    :auto-height="true"
    :layout-style="layoutStyle"
  >
    <div class="home-content">
      <a-space direction="vertical" fill size="medium">
        <template v-if="!isBusiness()">
          <AdData />
          <ProductAnalyses />
          <ConsumeProduct />
          <ConsumeMedia />
          <ConsumeBrand />
          <OptimiserAnalyses />
          <ConsumeOptimiser />
        </template>
        <a-row :gutter="16" v-else
          ><a-col :span="12">
            <ConsumeMedia />
          </a-col>
        </a-row>
      </a-space>
    </div>
  </PortalPage>
</template>

<script setup lang="ts">
  import PortalPage from '@/components/portal-page/index.vue';
  import { provide, ref } from 'vue';
  import {
    getMediaList,
    getNormList,
    getProductConsume,
    HomeInjectKey,
  } from '@/views/adp-home/utils';
  import { commonApi } from '@/api/common';
  import { useAppStore } from '@/store';
  import AdData from './components/ad-data.vue';
  import ProductAnalyses from './components/product-analyses.vue';
  import ConsumeProduct from './components/consume-product.vue';
  import ConsumeMedia from './components/consume-media.vue';
  import ConsumeBrand from './components/consume-brand.vue';
  import OptimiserAnalyses from './components/optimiser-analyses.vue';
  import ConsumeOptimiser from './components/consume-optimiser.vue';
  import useViewRule from './hooks/view-rule';

  const { isBusiness } = useViewRule();
  const layoutStyle = {
    padding: '0',
    backgroundColor: 'rgba(22,93,255,0.05)',
  };

  const appStore = useAppStore();
  // 搜索选项下拉框数据
  const searchMaps = ref<any>({
    // 产品下拉
    appList: appStore.companyPkgApps,
    // 优化师下拉
    optimiserList: [],
    // 指标下拉
    normList: getNormList(),
    // 媒体下拉
    mediaList: getMediaList(),
    // 产品消耗分布指标
    productConsume: getProductConsume(),
  });

  function getSearchMaps({ key }: { key: string }) {
    return searchMaps.value[key];
  }

  function loadMap() {
    commonApi.getCurrentUserList().then((res) => {
      searchMaps.value.optimiserList = res.data.map((item: any) => {
        return {
          ...item,
          label: item.userName,
          value: item.id,
        };
      });
    });
  }

  loadMap();
  provide(HomeInjectKey, {
    getSearchMaps,
  });
</script>

<style scoped lang="less">
  .adp-home {
    //background: url('../../assets/images/home-bg.png') no-repeat;
    //background-size: 100% 180px;
    .home-content {
      position: relative;
      width: 100%;
      flex: 1;
      max-width: 1440px;
      box-sizing: border-box;
      margin: 0 auto;
      padding: 17px 24px;
    }
  }
</style>
