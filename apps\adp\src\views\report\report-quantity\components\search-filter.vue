<template>
  <a-card class="search-filter" :bordered="false">
    <a-form :model="formData" auto-label-width label-align="left">
      <div style="display: flex; flex-wrap: wrap">
        <a-form-item
          style="margin-right: 16px; margin-bottom: 8px"
          field="mediaCode"
        >
          <template #label>
            <span style="font-weight: 500">投放媒体</span>
          </template>
          <a-radio-group
            type="button"
            v-model="formData.mediaCode"
            @change="handleChangeMedia"
          >
            <a-radio
              v-for="item in MEDIA_RADIO_GROUP"
              :key="item.value"
              :value="item.value"
              >{{ item.label }}</a-radio
            >
          </a-radio-group>
        </a-form-item>
        <FilterButton
          v-if="!openFilter"
          :open-filter="openFilter"
          @search="search"
          @reset="reset"
          @change-expand="changExpand"
        ></FilterButton>
      </div>
      <a-row v-show="openFilter" style="height: 30px; margin-top: 14px">
        <a-col :span="24">
          <a-form-item field="dataDim">
            <template #label>
              <span style="font-weight: 500">数据维度</span>
            </template>
            <a-radio-group
              type="button"
              v-model="formData.dataDim"
              @change="handleChangeDim"
            >
              <a-radio
                v-for="item in DIMENSION_RADIO_GROUP"
                :key="item.value"
                :value="item.value"
                >{{ item.label }}</a-radio
              >
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <div class="basic-filter" v-if="openFilter">
        <div class="label-text">筛选条件</div>
        <div class="basic-content">
          <a-row :gutter="16">
            <a-col :span="8" v-if="formData.dataDim !== 'app_id'">
              <a-form-item
                field="dimSearch"
                label="搜索"
                tooltip="单个搜索支持模糊，多个精确匹配，需按回车键确认输入值"
              >
                <a-input-group style="width: 100%" class="search-wrap">
                  <a-select
                    v-model="formData.dimSearch.dim"
                    style="width: 150px"
                    :options="DIMENSION_KEYWORD_OPTIONS[formData.dataDim]"
                    :show-extra-options="false"
                  />
                  <a-input-tag
                    v-model="formData.dimSearch.search"
                    placeholder="请输入关键词，按回车添加"
                    allow-clear
                    style="
                      max-height: 100px;
                      overflow: hidden auto;
                      display: inline-block;
                      min-height: 32px;
                    "
                  ></a-input-tag>
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="['app_id'].includes(formData.dataDim)">
              <a-form-item field="brandCodes" label="投放厂商">
                <a-select
                  v-model="formData.brandCodes"
                  placeholder="请选择"
                  allow-clear
                  allow-search
                  multiple
                  :max-tag-count="3"
                >
                  <a-option
                    v-for="item in SIMPLE_BRAND_OPTIONS"
                    :key="item.value"
                    :value="item.value"
                  >
                    <a-tooltip :content="item.label"
                      ><span>{{ item.label }}</span>
                    </a-tooltip>
                  </a-option></a-select
                >
              </a-form-item>
            </a-col>
            <a-col
              :span="8"
              v-if="
                [
                  'account_id',
                  'campaign_id',
                  'creative_id',
                  'optimizer_id',
                  'channel_code',
                ].includes(formData.dataDim)
              "
            >
              <a-form-item field="optimizers" label="优化师">
                <a-select
                  v-model="formData.optimizers"
                  placeholder="请选择"
                  allow-clear
                  allow-search
                  multiple
                  :max-tag-count="3"
                  :options="userArray"
                  :virtual-list-props="{ height: 200 }"
                >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col
              :span="8"
              v-if="
                [
                  'account_id',
                  'campaign_id',
                  'creative_id',
                  'channel_code',
                  'optimizer_id',
                ].includes(formData.dataDim)
              "
            >
              <SelectField
                v-model="formData.channelCodes"
                path="channelCodes"
                :field="selectFieldConfig"
              />
            </a-col>
            <a-col :span="8" v-if="['channel_code'].includes(formData.dataDim)">
              <a-form-item field="channelTypes" label="投放范围">
                <a-select
                  v-model="formData.channelTypes"
                  placeholder="请选择"
                  allow-clear
                  allow-search
                  multiple
                  :max-tag-count="3"
                >
                  <a-option
                    v-for="item in SCOPE_GROUP"
                    :key="item.value"
                    :value="item.value"
                  >
                    <a-tooltip :content="item.label"
                      ><span>{{ item.label }}</span>
                    </a-tooltip></a-option
                  ></a-select
                >
              </a-form-item>
            </a-col>
            <a-col
              :span="8"
              v-if="
                [
                  'account_id',
                  'campaign_id',
                  'creative_id',
                  'channel_code',
                  'optimizer_id',
                ].includes(formData.dataDim)
              "
            >
              <a-form-item
                field="operationMode"
                label="运营模式"
                :disabled="isDisabledOperationMode"
              >
                <a-select
                  v-model="formData.operationMode"
                  placeholder="请选择运营模式"
                  allow-clear
                  allow-search
                  :options="operationModeOptions"
                >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="['campaign_id'].includes(formData.dataDim)">
              <a-form-item field="origin" label="数据来源">
                <a-select
                  v-model="formData.origin"
                  placeholder="请选择数据来源"
                  allow-clear
                  allow-search
                  :options="[
                    { label: '优量批量', value: '优量批量' },
                    { label: '优量自动', value: '优量自动' },
                    { label: '其他', value: '其他' },
                  ]"
                >
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="['campaign_id'].includes(formData.dataDim)">
              <DateRangeField
                :field="dateFieldConfig"
                v-model="formData.planCreateTime"
                :show-time="true"
                path="planCreateTime"
              />
            </a-col>
            <a-col
              :span="8"
              v-if="['account_id', 'campaign_id'].includes(formData.dataDim)"
            >
              <a-form-item field="origin" label="回传方式">
                <a-select
                  v-model="formData.putType"
                  placeholder="请选择回传方式"
                  allow-clear
                  allow-search
                  :options="linkPutTypeList"
                  @clear="() => (formData.putType = undefined)"
                >
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item
            field="customCondition"
            label="自定义搜索"
            style="margin-bottom: -4px"
          >
            <CustomFilter
              v-model="formData.customCondition"
              v-show="openFilter"
              :data-dim="formData.dataDim"
            />
          </a-form-item>
        </div>
      </div>
      <FilterButton
        v-if="openFilter"
        style="position: absolute; bottom: 20px; right: 16px"
        :open-filter="openFilter"
        @search="search"
        @reset="reset"
        @change-expand="changExpand"
      ></FilterButton>
    </a-form>
  </a-card>
</template>

<script setup lang="ts">
  import {
    ref,
    onMounted,
    watch,
    inject,
    computed,
    Ref,
    shallowRef,
    isVNode,
  } from 'vue';
  import { cloneDeep, map, uniqBy } from 'lodash';
  import { commonApi } from '@/api/common';
  import { reportCommonApi } from '@/api/report/report-common';
  import { operationModeOptions } from '@/views/postback/channel/product/config/constants';
  import { toCamelCase } from '@/views/report/utils';
  import { SIMPLE_BRAND_OPTIONS } from '@/constants/brand';
  import DateRangeField from '@repo/kola/src/components/dynamic/form/widgets/date-range-field/index.vue';
  import SelectField from '@repo/kola/src/components/dynamic/form/widgets/select-field/index.vue';
  import dayjs from 'dayjs';
  import { linkPutTypeList } from '@/views/postback/link/config/utils';
  import {
    MEDIA_RADIO_GROUP,
    DIMENSION_RADIO_GROUP,
    SCOPE_GROUP,
    DIMENSION_KEYWORD_OPTIONS,
    quantityColumnMap,
  } from './constants';
  import CustomFilter from './custom-filter.vue';
  import FilterButton from './filter-button.vue';

  const props = defineProps<{
    topForm: any;
  }>();
  const emits = defineEmits(['search', 'reset', 'filterFormChange', 'change']);
  const openFilter = ref(true);

  const getDefaultFormData = () => {
    return {
      mediaCode: 0,
      dataDim: 'account_id',
      channelCodes: [],
      customCondition: {
        conditionItems: [
          {
            columnCode: 'media_cost',
            operator: 'gt',
            comparisonValue: undefined,
            comparisonValueStr: '',
          },
        ],
        conditionType: 'and',
      },
      dimSearch: {
        dim: 'link_id',
        search: [],
      },
    };
  };

  const formData = ref<any>(cloneDeep(getDefaultFormData()));
  watch(
    formData,
    () => {
      emits('filterFormChange', cloneDeep(formData.value));
    },
    { immediate: true, deep: true }
  );

  function changExpand() {
    openFilter.value = !openFilter.value;
  }
  function handleChangeMedia(value) {
    if (value !== 'toutiao2') {
      formData.value.origin = undefined;
    }
  }
  const handleChangeDim = () => {
    const { dataDim } = formData.value;

    if (dataDim === 'app_id') {
      formData.value.operationMode = undefined;
      formData.value.channelCodes = [];
    } else {
      formData.value.brandCodes = undefined;
    }
    if (dataDim !== 'campaign_id') {
      formData.value.origin = undefined;
      formData.value.planCreateTime = null;
    }
    if (!['account_id', 'campaign_id'].includes(dataDim)) {
      formData.value.putType = undefined;
    }
    formData.value.dimSearch = {
      dim: DIMENSION_KEYWORD_OPTIONS[dataDim]?.[0].value,
      search: [],
    };
  };
  const tableContentRef = inject<Ref<any>>('tableContentRef', ref(null));

  const defaultColumns = computed<string[]>(() => {
    return (
      tableContentRef.value?.defaultColumns?.map((item) => item.dataIndex) ?? []
    );
  });
  const isDisabledOperationMode = computed(() => {
    return (
      formData.value.dataDim === 'optimizer_id' &&
      !defaultColumns.value.includes(
        toCamelCase(quantityColumnMap.operation_mode)
      )
    );
  });
  watch(
    () => defaultColumns.value,
    () => {
      if (isDisabledOperationMode.value) {
        formData.value.operationMode = undefined;
      }
    }
  );
  function search() {
    emits('search');
  }

  const dateFieldConfig = {
    path: 'planCreateTime',
    label: '计划创建时间',
    type: 'dateRange',
    allowClear: true,
    valueFormat: 'YYYY-MM-DD HH:mm',
    showTime: true,
    timePickerProps: {
      format: 'HH:mm',
      defaultValue: ['00:00', '23:59'],
    },
    disabledDate: (current) => {
      const fortyFiveDay = dayjs().subtract(2, 'year');
      const nextDay = dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss');
      return (
        dayjs(current).isBefore(fortyFiveDay) || dayjs(current).isAfter(nextDay)
      );
    },
    style: {
      width: '100%',
    },
  };
  const selectFieldConfig = {
    label: '渠道',
    name: 'channelCodes',
    type: 'select',
    format: 'multipleSelect',
    select: {
      allowClear: true,
      allowSearch: true,
      maxTagCount: 3,
      openVirtual: true,
      tooltip: true,
      showAll: true,
    },
    source: {
      data: async () => {
        return channelList.value ?? [];
      },
    },
  };
  const userArray = shallowRef<any[]>([]);
  const channelList = shallowRef<any[]>([]);
  const getCreateUser = async () => {
    const res = await commonApi.getCurrentUserList();
    userArray.value = map(res.data, ({ userName, id }) => ({
      label: userName,
      value: id,
    }));
  };
  const getChannelList = async (pkgArr: any[]) => {
    if (!pkgArr || !pkgArr.length) {
      channelList.value = [];
      return;
    }
    const { data } = await reportCommonApi.postChannelList({
      pkgStrs: pkgArr && pkgArr.length ? pkgArr.join(',') : undefined,
    });
    channelList.value = uniqBy(data || [], 'channelCode').map((item: any) => {
      return {
        label: `${item.channelName}(${item.channelCode})`,
        value: item.channelCode,
      };
    });
  };
  function reset() {
    formData.value = getDefaultFormData();
    emits('reset');
  }
  watch(
    () => props.topForm.pkgNames,
    (value) => {
      // formData.value.channelCodes = [];
      getChannelList(value);
    },
    { deep: true }
  );
  defineExpose({
    getValue: () => cloneDeep(formData.value),
    reset,
  });
  onMounted(() => {
    getCreateUser();
    // getChannelList();
  });
</script>

<style scoped lang="less">
  .search-filter {
    :deep(.dynamic-form) {
      > .arco-form-item {
        // margin-bottom: 0;
        &:last-child {
          margin-right: 0;
        }
      }
    }

    .search-wrap {
      align-items: self-start;
    }

    .basic-filter {
      width: 100%;
      display: flex;
      margin-top: 24px;

      .label-text {
        width: 74px;
        line-height: 32px;
        font-weight: 500 !important;
        font-size: 13px;
      }

      .basic-content {
        flex: 1;
      }
    }
  }
</style>
