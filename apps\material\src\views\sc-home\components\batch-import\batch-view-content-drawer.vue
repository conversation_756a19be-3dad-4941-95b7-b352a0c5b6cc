<template>
  <div class="view-drawer">
    <a-drawer
      title="任务详情"
      :visible="visible"
      width="1100px"
      :render-to-body="false"
      @cancel="handleCancel"
      @close="handleClose"
      :footer="false"
    >
      <a-spin class="view-drawer-content">
        <div class="content-top" v-if="info">
          <div class="content-top-title">基本信息</div>
          <div class="content-top-body">
            <div class="content-body-row">
              <div class="content-body-item"> 批次ID：{{ info.id }}</div>
              <div
                class="content-body-item"
                style="display: flex; align-items: center"
              >
                工具名称：{{ info?.toolName }}
                <a-tooltip
                  :content="formatAiUtilsName(info.aiLabels)"
                  v-if="info.aiLabels"
                >
                  <AI
                    class="icon-size"
                    :style="{
                      width: '14px',
                      height: '14px',
                      marginLeft: '4px',
                    }"
                  />
                </a-tooltip>
              </div>
              <div class="content-body-item">
                任务状态：{{ info?.statusName }}
              </div>
            </div>
            <div class="content-body-row">
              <div class="content-body-item text" :title="info?.taskName">
                批次名称：
                {{ info?.taskName || '-' }}
              </div>
              <div class="content-body-item">
                更新时间：{{ info?.updatedAt }}
              </div>
              <div class="content-body-item">
                <a-space size="medium">
                  任务状态：
                  <StatusText type="success" hide-text-color>
                    已完成:{{ info?.successNum || '-' }}
                  </StatusText>
                  <StatusText type="error" hide-text-color>
                    失败:{{ info?.failNum || '-' }}
                  </StatusText>
                </a-space>
              </div>
            </div>
          </div>
        </div>
        <div class="content-table">
          <div class="content-table-title"
            >详细数据
            <a-select
              v-model="status"
              style="width: 120px; margin-left: 16px"
              :options="detailStatusOptions"
              placeholder="状态筛选"
              allow-clear
              @change="handleStatusChange"
            />
            <!-- <DynamicButton :config="config" style="margin-left: auto" /> -->
          </div>
          <div class="content-table-instances">
            <!-- :row-selection="rowSelection"
            v-model:selected-keys="selectedKeys" -->
            <a-table
              :loading="loading"
              :columns="columns"
              row-key="id"
              :data="tableData"
              :scroll="scroll"
              :scroll-bar="false"
              :pagination="pagination"
              @page-size-change="handlePageSizeChange"
              @page-change="handlePageChange"
            >
              <template #novelName="{ record }">
                <a-link
                  style="display: inline"
                  type="text"
                  @click="handlePreviewMaterial(record)"
                  :disabled="
                    !(
                      record.status === detailStatusEnum.success ||
                      record.status === detailStatusEnum.submitted
                    )
                  "
                >
                  {{ record.name || '-' }}
                </a-link>
              </template>
              <template #materialName="{ record }">
                <a-link
                  style="display: inline"
                  type="text"
                  @click="handlePreviewMaterial(record)"
                  :disabled="
                    !(
                      record.status === detailStatusEnum.success ||
                      record.status === detailStatusEnum.submitted
                    )
                  "
                >
                  {{ record.materialName || '-' }}
                </a-link>
              </template>
              <template #status="{ record }">
                <StatusText :type="record.statusType" hide-text-color>
                  {{ record.statusName }}
                </StatusText>
              </template>
              <template #statusName="{ record }">
                <StatusText
                  :type="record.statusType"
                  :tooltip="record.errMsg"
                  hide-text-color
                  >{{ record.statusName }}
                </StatusText>
              </template>
              <template #operations="{ record }">
                <a-button
                  type="text"
                  @click="handleDelete(record)"
                  :disabled="record.status !== detailStatusEnum.success"
                  :loading="record.delLoading"
                >
                  删除
                </a-button>
                <!-- <a-button
                  type="text"
                  @click="handleEdit(record)"
                  :disabled="
                    record.status !== detailStatusEnum.success ||
                    record.materialType !== 2
                  "
                  :loading="record.delLoading"
                >
                  编辑
                </a-button> -->
              </template>
            </a-table>
          </div>
        </div>
      </a-spin>
    </a-drawer>
    <PreviewMaterialModal
      v-model="previewMaterialVisible"
      v-model:options="previewMaterialOptions"
      @refresh="loadInfo"
      :material-num="info?.commitNum ?? 0"
      @delete-success="handleDeleteSuccess"
      :hide-edit="true"
    />
    <ReviewTaskModal
      v-model="reviewTaskVisible"
      v-bind="reviewTaskOptions"
      @refresh="refresh"
    />
  </div>
</template>

<script setup lang="ts">
  import StatusText from '@/components/status-text/index.vue';
  import { DButton } from '@repo/kola/src/components/dynamic/types/button';
  import { reactive, ref, watch } from 'vue';
  import useTablePage from '@/hooks/use-table-page';
  import { batchCreateApi } from '@/api/sc-home/batch-create';
  import useLoading from '@/hooks/loading';
  import usePreviewMaterial from '@/views/sc-home/hook/preview-material';
  import {
    detailStatusEnum,
    batchDetailStatusList,
    statusTypeList,
  } from '@/views/sc-home/utils';
  import { Message } from '@arco-design/web-vue';
  import { formatAiUtilsName } from '@/utils/ai-utils';
  import useReviewTask from '@/views/sc-home/hook/review-task';
  import AI from '@/components/container/resource/svg/AI.svg?component';
  import PreviewMaterialModal from './batch-preview-material-modal.vue';
  import ReviewTaskModal from './batch-review-task-modal.vue';

  const visible = defineModel<boolean>();
  const scroll = {
    x: '100%',
    y: '100%',
  };
  const props = defineProps({
    record: {
      type: Object,
    },
  });
  const detailStatusOptions = [...batchDetailStatusList];
  const { handleTaskReview, reviewTaskVisible, reviewTaskOptions } =
    useReviewTask();

  const info = ref<any>();

  const tableData = ref([]);
  const selectedKeys = ref([]);
  const status = ref<number>();
  const pushStatus = ref<number>();
  const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    onlyCurrent: false,
    fixed: true,
  });
  const config: DButton = {
    text: '提交审核',
    props: {
      type: 'outline',
      disabled: () => {
        return selectedKeys.value.length === 0;
      },
    },
    clickActionType: 'action',

    action: () => {
      handleTaskReview({
        ...info.value,
        materialIds: selectedKeys.value,
      });
    },
  };
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      width: 100,
    },
    {
      title: '小说名称',
      dataIndex: 'novelName',
      slotName: 'novelName',
      ellipsis: true,
      tooltip: true,
      bodyCellStyle: (record) => {
        const isDisabled = record.status !== detailStatusEnum.success;
        return {
          color: isDisabled ? '#98c3ff' : '#2166ff',
        };
      },
    },
    {
      title: '素材名称',
      dataIndex: 'materialName',
      slotName: 'materialName',
      ellipsis: true,
      tooltip: true,
      bodyCellStyle: (record) => {
        const isDisabled = record.status !== detailStatusEnum.success;
        return {
          color: isDisabled ? '#98c3ff' : '#2166ff',
        };
      },
    },
    {
      title: '任务状态',
      dataIndex: 'statusName',
      slotName: 'statusName',
    },
    {
      title: '开始合成时间',
      dataIndex: 'createdAt',
      width: 168,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 168,
    },
    {
      title: '操作',
      dataIndex: 'operations',
      slotName: 'operations',
      fixed: 'right',
    },
  ];
  const {
    pagination,
    currentPage,
    handlePageSizeChange,
    handlePageChange,
    resetPage,
  } = useTablePage(loadInfo);
  const { loading, setLoading } = useLoading();
  const handleStatusChange = (value) => {
    selectedKeys.value = [];
    resetPage();
    if (value === '') {
      status.value = undefined;
      pushStatus.value = undefined;
    } else if (value > 100) {
      status.value = undefined;
      pushStatus.value = value - 100;
    } else {
      status.value = value;
    }
    loadInfo();
  };

  const refresh = () => {
    selectedKeys.value = [];
    loadInfo();
  };

  function loadInfo() {
    setLoading(true);
    const taskId = props.record?.id;
    batchCreateApi
      .materialList({
        taskId,
        ...currentPage.value,
        status: status.value,
        pushStatus: pushStatus.value,
      })
      .then((res) => {
        tableData.value = (res.data?.list || []).map((item) => {
          if (item.pushStatus > 1) {
            item.status = item.pushStatus + 100;
          }
          const data = batchDetailStatusList.find(
            (v) => v.value === item.status
          );
          return {
            ...item,
            materialName: item.material_name,
            materialType: 2,
            name: item.name,
            statusName: data?.label ?? '',
            statusType: data?.type ?? '',
            taskId,
            disabled: item.status !== detailStatusEnum.success,
          };
        });
        pagination.total = res.data?.pageInfo?.total || 0;
        const statusItem = statusTypeList.find(
          (v) => v.value === props.record?.status
        );
        info.value = {
          ...(res.data?.totalData ?? {}),
          id: taskId,
          // toolName:
          //   nameTypeList.find((v) => v.value === props.record?.nameType)
          //     ?.label ?? '',
          toolName: '导入合成/解压小说',
          // aiLabels: props.record?.aiLabels,
          statusName: statusItem?.label ?? '',
          statusType: statusItem?.type ?? '',
          taskName: props.record?.taskName,
        };
      })
      .finally(() => {
        setLoading(false);
      });
  }

  watch(
    () => visible.value,
    (val) => {
      if (val) {
        loadInfo();
      }
    }
  );
  function handleDeleteSuccess() {
    loadInfo();
  }

  const { previewMaterialOptions, previewMaterialVisible, openMaterial } =
    usePreviewMaterial();

  function handlePreviewMaterial(record) {
    const list = tableData.value
      .filter(
        (item: any) =>
          item.status === detailStatusEnum.success ||
          item.status === detailStatusEnum.submitted
      )
      .map((item: any) => {
        return {
          ...item,
          url: item.synUrl,
        };
      });
    const openIndex = list.findIndex((item) => item.id === record.id);
    openMaterial({
      record,
      index: openIndex,
      list,
    });
  }

  function handleDelete(record) {
    record.delLoading = true;
    batchCreateApi
      .deleteMaterial({
        ids: [record.id],
      })
      .then((res) => {
        Message.success('删除成功');
        handleDeleteSuccess();
      })
      .finally(() => {
        record.delLoading = false;
      });
  }

  function handleEdit(record) {
    const { taskId, id } = record;
    window.open(
      `/editor?from=edit&taskId=${taskId}&materialId=${id}`,
      '_blank'
    );
  }

  function handleCancel() {
    visible.value = false;
  }

  const emits = defineEmits(['refresh']);

  function handleClose() {
    emits('refresh');
    tableData.value = [];
    status.value = undefined;
    selectedKeys.value = [];
    setLoading(false);
    resetPage();
  }
</script>

<style scoped lang="less">
  .view-drawer {
    width: 100%;
    height: 100%;

    :deep(.arco-drawer-body) {
      padding: 0;
      background: rgb(0 0 0 / 4%);
    }

    .view-drawer-content {
      height: 100%;
      overflow: hidden;
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 14px 24px;

      .content-top {
        background-color: #fff;
        border-radius: 2px;

        .content-top-title {
          display: flex;
          align-items: center;
          padding-left: 24px;
          height: 64px;
          font-weight: 400;
          font-size: 16px;
          color: #000;
          line-height: 24px;
          border-bottom: 1px solid rgb(0 0 0 / 8%);
        }

        .content-top-body {
          padding: 16px 0;

          .content-body-row {
            display: flex;
            flex-wrap: wrap;
            padding-left: 24px;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .content-body-item {
              flex: 0 0 33%;
              font-size: 14px;
              color: rgb(0 0 0 / 84%);
              line-height: 22px;
              white-space: nowrap; /* 禁止文本换行 */
              overflow: hidden; /* 隐藏溢出内容 */
              text-overflow: ellipsis; /* 使用省略号表示溢出 */

              &.long {
                flex: 0 0 66%;
              }

              &.text {
                padding-right: 16px;
              }
            }
          }
        }
      }

      .content-table {
        background-color: #fff;
        border-radius: 2px;
        margin-top: 16px;
        padding: 0 24px;
        padding-bottom: 24px;
        display: flex;
        flex-direction: column;
        height: 100%;
        flex: 1;
        overflow: hidden;

        .content-table-title {
          display: flex;
          align-items: center;
          height: 64px;
          font-weight: 400;
          font-size: 16px;
          color: #000;
          line-height: 24px;
        }

        .content-table-instances {
          flex: 1;
          overflow: hidden;
          height: 100%;
        }
      }
    }
  }
</style>
