<template>
  <PortalPage
    :title="`${pageTitle}素材自动策略`"
    show-back
    :custom-back="handleBack"
    auto-height
    class="create-tactic-form"
  >
    <a-spin :loading="loading" class="create-loading">
      <a-steps :current="step" @change="setCurrent" class="step-container">
        <a-step>策略配置 </a-step>
        <a-step>执行规则</a-step>
      </a-steps>
      <div v-show="step === 1">
        <a-form
          ref="startRef"
          :model="formData"
          auto-label-width
          label-align="left"
        >
          <collapse-card
            :style="{ width: '100%', marginBottom: '15px' }"
            title="创意启动策略"
          >
            <a-space direction="vertical" :style="{ width: '100%' }">
              <a-radio-group
                type="button"
                v-model="formData.startType"
                :options="CONSTANT.StartStrategy"
                @change="handleStartTypeChange"
              />
              <a-form-item
                :style="{ marginTop: '10px' }"
                label="新创意来源："
                field="source"
                :rules="[
                  {
                    required: true,
                    message: '请选择新创意来源',
                  },
                ]"
                v-if="formData.startType === CONSTANT.StartStrategy[0].value"
              >
                <a-select
                  v-model="formData.source"
                  placeholder="请选择创意来源"
                  :options="CONSTANT.sourceOptions"
                  @change="sourceChange"
                >
                </a-select>
              </a-form-item>
              <targetFolder
                v-if="formData.startType === CONSTANT.StartStrategy[0].value"
                v-model="formData"
              ></targetFolder>
              <LinkCondition
                v-if="formData.startType === CONSTANT.StartStrategy[1].value"
                v-model="formData"
              ></LinkCondition>
              <a-form-item
                :style="{ marginTop: '10px' }"
                label="选择支持的三方平台："
                v-if="formData.startType === CONSTANT.StartStrategy[2].value"
              >
                <a-radio-group v-model="formData.thirdPlatform">
                  <a-space direction="vertical">
                    <div style="display: flex; align-items: center"
                      ><a-radio :value="ThirdPlatform.fx.value">{{
                        ThirdPlatform.fx.label
                      }}</a-radio
                      ><a-link
                        target="_blank"
                        :href="CONSTANT.PluginInstallDocPath"
                        >去安装</a-link
                      ></div
                    >
                    <span style="color: #a7a9aa"
                      >选择三方平台后，需要在三方平台的页面通过插件发起自动化，比如在风行千推的后台导入书籍，即可自动制作素材</span
                    >
                    <!-- <a-radio :value="ThirdPlatform.manual.value">{{
                      ThirdPlatform.manual.label
                    }}</a-radio> -->
                  </a-space>
                </a-radio-group>
              </a-form-item>
            </a-space>
          </collapse-card>
        </a-form>
        <DynamicForm
          ref="baseFormRef"
          :form-schema="baseFormSchema"
          v-model="formData"
        />
      </div>
      <div v-show="step === 2">
        <a-form
          ref="ruleRef"
          :model="formData"
          auto-label-width
          label-align="left"
        >
          <collapse-card
            :style="{ width: '100%', marginBottom: '15px' }"
            title="执行规则"
          >
            <a-space direction="vertical">
              <a-form-item
                label="基础规则"
                field="processType"
                :rules="[
                  {
                    required: true,
                    message: '请选择基础规则',
                  },
                  { validator: baseRuleValidator },
                ]"
              >
                <a-radio-group
                  v-model="formData.processType"
                  @change="handleProcessTypeChange"
                >
                  <a-space direction="vertical">
                    <div style="display: flex; align-items: center"
                      ><a-radio :value="ExecuteRule.FixedFrequency.value">{{
                        ExecuteRule.FixedFrequency.label
                      }}</a-radio
                      ><span
                        >每1天的
                        <a-time-picker
                          format="HH:mm"
                          :style="{ width: '100px', margin: '0 5px' }"
                          v-model="formData.processTime"
                        />
                        开始执行</span
                      ></div
                    >
                    <a-radio :value="ExecuteRule.CreativeTrigger.value">{{
                      ExecuteRule.CreativeTrigger.label
                    }}</a-radio>
                  </a-space>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="追加规则" field="maxNum">
                <span
                  >策略停止规则<span style="margin: 0 5px">素材生成上限：</span
                  ><a-input-number
                    v-model="formData.maxNum"
                    placeholder="请输入"
                    :style="{ width: '100px', margin: '0 5px' }"
                    :min="1"
                    :precision="0"
                    :allow-clear="true"
                /></span>
              </a-form-item>
            </a-space>
          </collapse-card>
          <a-form-item
            field="name"
            label="策略名称"
            :rules="[{ required: true, message: '策略名称为必填' }]"
            :validate-trigger="['change', 'input']"
          >
            <a-input v-model="formData.name" />
          </a-form-item>
        </a-form>
      </div>
    </a-spin>
    <template #footer>
      <div class="view-footer">
        <a-button @click="handleBack">取消</a-button>
        <a-button v-show="[2].includes(step)" @click="handlePrev">
          上一步
        </a-button>
        <a-button
          v-show="[1].includes(step)"
          type="primary"
          @click="handleNext"
          :loading="nextLoading"
        >
          下一步
        </a-button>
        <a-button
          v-show="step === 2"
          type="primary"
          @click="onSubmit"
          :loading="loading || submitLoading"
        >
          确定
        </a-button>
      </div>
    </template>
  </PortalPage>
</template>

<script setup lang="ts">
  import usePageFrom from '@/hooks/page-from';
  import CollapseCard from '@repo/kola/src/components/dynamic/form/widgets/collapse-card/index.vue';
  import PortalPage from '@/components/portal-page/index.vue';
  import { onBeforeUnmount, onMounted, ref, useTemplateRef } from 'vue';
  import router from '@/router';
  import { cloneDeep, isNil } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import { useTrees } from '@/hooks/material-tree';
  import { autoMaterial } from '@/api/automation/auto-material';
  import {
    getBaseFormValues,
    getBaseFormSchema,
  } from './config/strategy-config';
  import { CONSTANT, ThirdPlatform, ExecuteRule } from './config/constant';
  import LinkCondition from './config/link-condition.vue';
  import targetFolder from './config/target-folder.vue';
  import { useAccountOptions } from './config/use-account-options';

  const { trees, updateTrees } = useTrees();
  const { getAccountOptions, getChannelOptions, channelOptions } =
    useAccountOptions();
  const { pageTitle, pageFromQuery, pageFrom } = usePageFrom();

  const loading = ref(false);
  const submitLoading = ref(false);
  const nextLoading = ref(false);

  const formData = ref<any>(getFormValue());

  function getFormValue() {
    return {
      ...getBaseFormValues(),
    };
  }
  if (pageFrom.value.isModify) {
    autoMaterial
      .list({
        id: Number(pageFromQuery.value.id),
      })
      .then(async ({ data }: any) => {
        if (isNil(data) || data.list.length === 0) {
          Message.error('获取策略详情失败');
          return;
        }
        const info = data.list[0] || {};
        const params: any = {};
        if (info.pushPlatform === 1) {
          params.dir = {
            label: info.commitAlbumName,
            value: info.commitAlbumId,
          };
        } else if (info.pushPlatform === 2) {
          params.selectCatalog = {
            albumName: info.commitAlbumName,
            albumId: info.commitAlbumId,
            folder: {
              label: info.commitFolderName,
              value: info.commitFolderId,
            },
          };
        }
        formData.value = {
          ...getBaseFormValues(),
          ...data.list[0],
          ...params,
          auditStrategy: {
            checkType: info.checkType,
          },
        };
        if (formData.value.startType === 3) {
          const {
            processTemplate,
            campaignConfig: {
              media,
              app,
              accountIds,
              channelCodes = [],
              campaignFilter,
              materialFilter,
            },
          } = info;
          formData.value.templateList.template = processTemplate;
          formData.value.media = media;
          formData.value.app = typeof app === 'string' ? app.split(',') : [app];
          formData.value.channelCodes = channelCodes
            ? channelCodes.map((item: any) => item.id)
            : [];
          formData.value.accountIds = accountIds;
          if (formData.value.channelCodes.length) {
            formData.value.accountType = 'channelCodes';
          } else if (formData.value.accountIds.length) {
            formData.value.accountType = 'appId';
          }
          formData.value.campaignFilter = campaignFilter;
          formData.value.materialFilter = materialFilter;
          await getAccountOptions(formData.value.app, formData.value.media);
          await getChannelOptions(formData.value.app, formData.value.media);
        } else if (
          formData.value.startType === 1 &&
          formData.value.campaignConfig?.folderFilter?.type &&
          formData.value.campaignConfig?.folderFilter?.type !== 1
        ) {
          const {
            campaignConfig: {
              folderFilter: { type, folderIds, albumIds },
              materialFilter,
              media,
              startCreateTime,
              endCreateTime,
            },
            processTemplate,
          } = info;
          formData.value.templateList.template =
            processTemplate && processTemplate.length
              ? processTemplate
              : [
                  {
                    templateType: '',
                    processNum: '',
                  },
                ];

          formData.value.source = type;
          if (type === 2) {
            formData.value.targetDir = folderIds;
          } else {
            formData.value.targetSelectCatalog = {
              albumId: albumIds[0].albumId,
              albumName: albumIds[0].albumName,
              folder: [...folderIds],
            };
          }
          formData.value.folderFilter = materialFilter;
          formData.value.mediaCode = media;
          if (startCreateTime && endCreateTime) {
            formData.value.createdDateRange = [
              startCreateTime.split(' ')[0],
              endCreateTime.split(' ')[0],
            ];
          }
          formData.value.templateList.processNum = info.processNum;
        } else {
          formData.value.templateList.processNum = info.processNum;
        }
        // 如果maxNum为0，删除该字段(0为不限制素材生成上限)
        if (formData.value.maxNum === 0) {
          delete formData.value.maxNum;
        }
      });
  }

  const baseFormSchema = getBaseFormSchema();

  const startRef = useTemplateRef<any>('startRef');
  const baseFormRef = useTemplateRef<any>('baseFormRef');
  const ruleRef = useTemplateRef<any>('ruleRef');

  const step = ref(1);
  function setCurrent(current: number) {
    step.value = current;
  }

  function handlePrev() {
    step.value -= 1;
  }
  async function handleNext() {
    if (step.value === 1) {
      const result = await startRef.value?.validate();
      await baseFormRef.value?.validate();
      if (result) return;
      step.value += 1;
    }
  }
  function handleBack() {
    router.push({
      name: 'materialAutomationStrategy',
    });
  }
  async function onSubmit() {
    // 校验表单
    try {
      const result = await ruleRef.value?.validate();
      if (result) return;

      const submitValue = cloneDeep(formData.value);

      if (formData.value.pushPlatform === 1) {
        submitValue.commitAlbumId = formData.value.dir.value;
        submitValue.commitAlbumName = formData.value.dir.label;
        delete submitValue.commitFolderId;
        delete submitValue.commitFolderName;
      }
      if (formData.value.pushPlatform === 2) {
        submitValue.commitAlbumId = formData.value.selectCatalog.albumId;
        submitValue.commitAlbumName = formData.value.selectCatalog.albumName;
        submitValue.commitFolderId = formData.value.selectCatalog.folder.value;
        submitValue.commitFolderName =
          formData.value.selectCatalog.folder.label;
      }
      delete submitValue.dir;
      delete submitValue.selectCatalog;
      submitValue.processNum = formData.value.templateList.processNum;

      delete submitValue.templateList;
      submitValue.checkType = formData.value.auditStrategy.checkType;
      delete submitValue.auditStrategy;
      if (submitValue.startType === 3) {
        const {
          media,
          app,
          accountIds,
          channelCodes,
          campaignFilter,
          materialFilter,
        } = formData.value;
        submitValue.processTemplate = formData.value.templateList.template;
        submitValue.campaignConfig = {
          media,
          app: app.join(','),
          accountIds,
          channelCodes: channelOptions.value.filter((item: any) =>
            channelCodes.includes(item.id)
          ),
          campaignFilter,
          materialFilter,
        };
      } else if (submitValue.startType === 1 && submitValue.source !== 1) {
        submitValue.campaignConfig = {};
        submitValue.processTemplate = [];
        const {
          targetDir,
          targetSelectCatalog,
          createdDateRange,
          mediaCode,
          folderFilter,
          source,
        } = formData.value;
        submitValue.campaignConfig = {
          folderFilter: {
            type: source,
            folderIds: source === 2 ? targetDir : targetSelectCatalog.folder,
            albumIds:
              source === 2
                ? []
                : [
                    {
                      albumId: targetSelectCatalog.albumId,
                      albumName: targetSelectCatalog.albumName,
                    },
                  ],
          },
          materialFilter: folderFilter,
          media: mediaCode,
          startCreateTime: createdDateRange[0]
            ? `${createdDateRange[0]} 00:00:00`
            : null,
          endCreateTime: createdDateRange[1]
            ? `${createdDateRange[1]} 23:59:59`
            : null,
        };
        if (
          formData.value.templateList.template &&
          formData.value.templateList.template[0].templateType
        ) {
          submitValue.processTemplate = formData.value.templateList.template;
        }

        delete submitValue.targetDir;
        delete submitValue.targetSelectCatalog;
        delete submitValue.createdDateRange;
        delete submitValue.mediaCode;
        delete submitValue.folderFilter;
        delete submitValue.source;
      } else {
        submitValue.campaignConfig = {};
        submitValue.processTemplate = [];
      }

      delete submitValue.media;
      delete submitValue.app;
      delete submitValue.accountIds;
      delete submitValue.channelCodes;
      delete submitValue.accountType;
      delete submitValue.campaignFilter;
      delete submitValue.materialFilter;
      delete submitValue.targetDir;
      delete submitValue.targetSelectCatalog;
      delete submitValue.createdDateRange;
      delete submitValue.mediaCode;
      delete submitValue.folderFilter;
      delete submitValue.source;
      let api = autoMaterial.add;
      if (pageFrom.value.isModify) {
        submitValue.id = Number(pageFromQuery.value.id);
        api = autoMaterial.edit;
      }
      // 提交表单
      console.log('submit', submitValue);
      const res = await api(submitValue);
      if (res) {
        Message.success('提交成功');
        handleBack();
      }
    } catch (error) {
      console.log(error);
    }
  }

  onMounted(() => {
    updateTrees();
  });
  onBeforeUnmount(() => {});

  function baseRuleValidator(value: any, callback: any) {
    if (isNil(value)) {
      callback('请选择基础规则');
    } else if (value === ExecuteRule.FixedFrequency.value) {
      if (isNil(formData.value.processTime)) {
        callback('请选择执行时间');
      }
      callback();
    }
    callback();
  }

  function handleProcessTypeChange(value: any) {
    if (value === ExecuteRule.FixedFrequency.value) {
      formData.value.processTime = null;
    } else if (value === ExecuteRule.CreativeTrigger.value) {
      formData.value.processTime = null;
    }
  }
  function handleStartTypeChange(value: any) {
    formData.value.source = 1;
    if (value !== 3) {
      formData.value.templateList.template = [
        {
          templateType: '',
          processNum: '',
        },
      ];
      formData.value.media = '';
      formData.value.app = '';
      formData.value.accountIds = [];
      formData.value.campaignFilter = [
        {
          field: 'media_cost',
          condition: '',
          value: '',
        },
        {
          field: 'create_time',
          condition: 'equal',
          value: '',
        },
        {
          field: 'ad_click_income_roi',
          condition: '',
          value: '',
        },
      ];
      formData.value.materialFilter = [
        {
          field: '',
          condition: '',
          value: '',
        },
      ];
      formData.value.processingType = 1;
      formData.value.productType = 1;
    } else {
      formData.value.processingType = 2;
      formData.value.productType = 2;
    }
  }
  function sourceChange(value: any) {
    if (value === 1) {
      formData.value.processingType = 1;
      formData.value.productType = 1;
    } else {
      formData.value.processingType = 2;
      formData.value.productType = 2;
    }
    formData.value.targetDir = [];
    formData.value.targetSelectCatalog = [];
  }
</script>

<style scoped lang="less">
  .create-tactic-form {
    background-color: #fff;
    min-height: 100%;

    .create-loading {
      display: block;
      padding: 16px 24px;
    }

    .step-container {
      width: 80%;
      margin: 0 auto 30px;
    }
  }

  .view-footer {
    display: flex;
    align-items: center;
    gap: 8px;
  }
</style>
