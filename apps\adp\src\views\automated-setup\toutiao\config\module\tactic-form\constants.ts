import { uniqueId } from 'lodash';
import { filedMap } from '@/views/automated-setup/common/constants';

export const operatorEnum = {
  equal: 'equal',
  gt: 'gt',
  lt: 'lt',
  gte: 'gte',
  lte: 'lte',
  range: 'range',
  top: 'top',
};

export const volumeMaterialFiltrateOperatorOptions = [
  { value: operatorEnum.gte, label: '大于等于' },
  { value: operatorEnum.gt, label: '大于' },
  { value: operatorEnum.lte, label: '小于等于' },
  { value: operatorEnum.lt, label: '小于' },
  { value: operatorEnum.top, label: 'Top' },
  { value: operatorEnum.equal, label: '等于' },
];
export const getVolumeMaterialFiltrateItem = () => {
  return {
    rowKey: uniqueId('VolumeMaterialFiltrate_'),
    field: '',
    op: operatorEnum.gt,
    value: undefined,
  };
};

export const constStatusOptions = [
  { label: '未使用', value: 0 },
  { label: '无消耗', value: 1 },
  { label: '有消耗', value: 2 },
];
export const volumeMaterialFiltrateFieldOptions = [
  {
    label: '近3日消耗',
    value: 'three_day_cost',
  },
  {
    label: '近7日消耗',
    value: 'seven_day_cost',
  },
  {
    label: '近3日点击率',
    value: 'three_day_click_ratio',
  },
  {
    label: '近7日点击率',
    value: 'seven_day_click_ratio',
  },
  {
    label: '近3日转化率',
    value: 'three_day_convert_ratio',
  },
  {
    label: '近7日转化率',
    value: 'seven_day_convert_ratio',
  },
  {
    label: '媒体拒审次数',
    value: filedMap.ttMediaFailNum,
  },
  {
    label: '优量累计关联广告数',
    value: filedMap.usedCount,
  },
  {
    label: '素材状态',
    value: filedMap.costStatus,
  },
];
export const volumeCopywriterFiltrateOperatorOptions = [
  { value: operatorEnum.gte, label: '大于等于' },
  { value: operatorEnum.gt, label: '大于' },
  { value: operatorEnum.lte, label: '小于等于' },
  { value: operatorEnum.lt, label: '小于' },
  { value: operatorEnum.top, label: 'Top' },
];
export const getVolumeCopywriterFiltrateItem = () => {
  return {
    rowKey: uniqueId('VolumeCopywriterFiltrate_'),
    field: '',
    op: operatorEnum.gt,
    value: undefined,
  };
};
export const volumeCopywriterFiltrateFieldOptions = [
  {
    label: '近3日点击率',
    value: 'recent_three_days_click_rate',
  },
  {
    label: '近3日消耗',
    value: 'recent_three_days_cost',
  },
  {
    label: '近30日点击率',
    value: 'recent_thirty_days_click_rate',
  },
  {
    label: '近30日消耗',
    value: 'recent_thirty_days_cost',
  },
];

export const ruleTypeMap = {
  num: 0,
  percent: 1,
};
export const ruleTypeOptions = [
  { label: '设置数量上限', value: ruleTypeMap.num },
  { label: '设置配额百分比', value: ruleTypeMap.percent },
];
