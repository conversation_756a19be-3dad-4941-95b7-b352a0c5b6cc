import { uniqueId } from 'lodash';

export type formModeType = 'create' | 'modify' | 'copy';
export const formMode = {
  create: 'create',
  modify: 'modify',
  copy: 'copy',
};

// 大于 | 大于等于 | 小于 | 小于等于 | 等于 | 介于
// ('gt | gte | lt | lte | equal | in');
// eslint-disable-next-line no-shadow
export enum operatorEnum {
  gte = 'gte',
  lte = 'lte',
  in = 'in',
  notin = 'notin',
  equal = 'equal',
  neq = 'neq',
  between = 'between',
  gt = 'gt',
  lt = 'lt',
}
export const operatorOptions = [
  {
    label: '大于等于',
    value: operatorEnum.gte,
  },
  {
    label: '小于等于',
    value: operatorEnum.lte,
  },
  {
    label: '包含',
    value: operatorEnum.in,
  },
  {
    label: '不包含',
    value: operatorEnum.notin,
  },
  {
    label: '等于',
    value: operatorEnum.equal,
  },
  {
    label: '不等于',
    value: operatorEnum.neq,
  },
  {
    label: '大于',
    value: operatorEnum.gt,
  },
  {
    label: '小于',
    value: operatorEnum.lt,
  },
  {
    label: '介于',
    value: operatorEnum.between,
  },
];

export function getTriggerConditionItem() {
  return {
    code: '',
    // columnName: '',
    operator: undefined,
    // comparisonValue: null,
    value: null,
    maxValue: '',
    rowKey: uniqueId('trigger_'),
    // rangeValue: {
    //   max: null,
    //   min: null,
    // },
  };
}

export function getTacticConfigItem() {
  return {
    columnCode: '',
    operator: operatorEnum.gte,
    comparisonValue: null,
    rowKey: uniqueId('tactic_'),
    rangeValue: {
      max: null,
      min: null,
    },
  };
}

// eslint-disable-next-line no-shadow
export enum replenishColumnEnum {
  create_time = 'create_time',
  media_cost = 'media_cost',
  convert_ratio = 'convert_ratio',
  max_report_num = 'max_report_num',
  convert_cost = 'convert_cost',
}

// eslint-disable-next-line no-shadow
export enum typeEnum {
  clickNum = 'clickNum',
  clickPkg = 'clickPkg',
  installNum = 'installNum',
  installPkg = 'installPkg',
  sourcePkg = 'sourcePkg',
}

export const inputNumberCodes = [typeEnum.clickNum, typeEnum.installNum];

export const inputCodes = [
  typeEnum.clickPkg,
  typeEnum.installPkg,
  typeEnum.sourcePkg,
];
export function getReplenishColumns() {
  return [
    {
      label: '时间范围',
      value: 'time',
    },
    {
      label: '点击次数',
      value: 'clickNum',
    },
    {
      label: '点击包名',
      value: 'clickPkg',
    },
    {
      label: '安装次数',
      value: 'installNum',
    },
    {
      label: '安装包名',
      value: 'installPkg',
    },
    {
      label: '来源包',
      value: 'sourcePkg',
    },
  ];
}

// eslint-disable-next-line no-shadow
export enum replenishPutTypeEum {
  LID = 'lid',
  AID = 'aid',
  CID = 'cid',
}
export const replenishPutTypeOptions = [
  {
    label: '链接/LID',
    value: replenishPutTypeEum.LID,
  },
  {
    label: '计划/AID',
    value: replenishPutTypeEum.AID,
  },
  {
    label: '创意/CID',
    value: replenishPutTypeEum.CID,
  },
];

export const timeList = [
  {
    label: '当天',
    value: 'TODAY',
  },
  {
    label: '昨天',
    value: 'LAST_1_DAY',
  },
  {
    label: '近3天',
    value: 'LAST_3_DAY',
  },
];

export const snatchTacticsOptions = [
  {
    label: '优先点击次数',
    value: 'clickNum',
    disabled: true,
  },
  {
    label: '优先安装次数',
    value: 'installNum',
    disabled: true,
  },
  {
    label: '优先时间',
    value: 'time',
    disabled: false,
  },
];

export default null;
