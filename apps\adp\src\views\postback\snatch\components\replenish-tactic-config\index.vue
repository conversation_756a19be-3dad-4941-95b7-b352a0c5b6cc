<template>
  <div class="replenish-tactic-condition">
    <ConditionList
      v-model:list="model.quotaList"
      @add="handleAdd"
      row-key="rowKey"
      :is-toggle-relation="false"
      :singer-relation="true"
      :visible-on-del="visibleOnDel"
    >
      <template #title>
        <span>用户类型</span>
      </template>
      <template #content="{ listItem, listIndex }">
        <div class="list-item">
          <a-form-item
            hide-label
            :style="{
              width: listItem.code === 'time' ? '248px' : '160px',
            }"
            :field="getFieldPath({ listIndex, field: 'code' })"
            :rules="[{ required: true, message: '请选择' }]"
            :disabled="listItem.code === 'time'"
          >
            <a-select
              v-model="listItem.code"
              placeholder="请选择"
              :style="{
                width: listItem.code === 'time' ? '248px' : '160px',
              }"
              allow-clear
              allow-search
              :options="columnOptions"
              @clear="(e) => handleClear({ listItem, e })"
              @change="(code) => handleColumnChange({ code, listItem })"
            >
            </a-select>
          </a-form-item>
          <a-form-item
            hide-label
            :rules="[{ required: true, message: '请选择' }]"
            :field="getFieldPath({ listIndex, field: 'operator' })"
            :disabled="isDisableOperator(listItem)"
            v-show="listItem.code !== 'time'"
          >
            <a-select
              v-model="listItem.operator"
              placeholder="请选择"
              style="width: 160px"
              allow-clear
              :options="getOperationList(listItem.code)"
              @change="(opValue) => handleOpChange({ opValue, listItem })"
            >
            </a-select>
          </a-form-item>
          <ReplenishConditionValue
            v-model:value="listItem.value"
            v-model:max-value="listItem.maxValue"
            :field="listItem.code"
            :op="listItem.operator"
            :base-path="getBasePath(listIndex)"
            :suffix-options="replenishColumns"
            :get-num-props="getConditionValueNumberProps"
            :disabled="fixedOperator.includes(listItem.code)"
          />
        </div>
      </template>
    </ConditionList>
  </div>
</template>

<script setup lang="ts">
  import ConditionList from '@/components/condition-list/index.vue';
  import { computed, inject, ref, watch } from 'vue';
  import ReplenishConditionValue from '../replenish-condition-value/index.vue';
  import {
    getTriggerConditionItem,
    operatorEnum,
    operatorOptions,
    replenishColumnEnum,
    typeEnum,
  } from '../../constants';

  import useReplenishColumnOptions from '../../hooks/use-replenish-column-options';

  const allFormData: any = inject<any>('formData');

  const props = defineProps({
    path: String,
  });
  const model = defineModel<any>();
  const listField = 'quotaList';

  const operationMap = {
    clickNum: [operatorEnum.gt],
    installNum: [operatorEnum.gt],
    clickPkg: [
      operatorEnum.equal,
      operatorEnum.neq,
      operatorEnum.in,
      operatorEnum.notin,
    ],
    installPkg: [
      operatorEnum.equal,
      operatorEnum.neq,
      operatorEnum.in,
      operatorEnum.notin,
    ],
    sourcePkg: [
      operatorEnum.equal,
      operatorEnum.neq,
      operatorEnum.in,
      operatorEnum.notin,
    ],
  };

  function getOperationList(code) {
    if (!code || !Object.keys(operationMap).includes(code)) return [];

    const result = operatorOptions.filter((item) => {
      return operationMap[code].includes(item.value);
    });
    return result;
  }

  function getConditionValueNumberProps(field) {
    if (field === replenishColumnEnum.max_report_num) {
      return {
        precision: 0,
      };
    }
    return null;
  }
  function getBasePath(listIndex) {
    return `${props.path}.${listField}[${listIndex}]`;
  }
  function getFieldPath({ listIndex, field }) {
    return `${getBasePath(listIndex)}.${field}`;
  }
  const { replenishColumns, dynamicReplenishOptions }: any =
    useReplenishColumnOptions({
      modelList: model.value[listField],
    });

  const columnOptions = computed(() => {
    return dynamicReplenishOptions.value;
  });

  function clearVal(listItem) {
    listItem.value = null;
    listItem.maxValue = null;
  }
  function handleClear({ listItem }) {
    listItem.code = null;
  }

  function visibleOnDel(item, index) {
    return index >= 1;
  }
  function handleOpChange({ opValue, listItem }) {
    clearVal(listItem);
  }

  // 点击次数、安装次数：固定大于，值固定0
  const fixedOperator = [typeEnum.clickNum, typeEnum.installNum];

  const disableOperator = computed(() => {
    return [
      replenishColumnEnum.convert_ratio,
      replenishColumnEnum.convert_cost,
      replenishColumnEnum.max_report_num,
    ];
  });
  function isDisableOperator(listItem) {
    return (
      disableOperator.value.includes(listItem.code) ||
      fixedOperator.includes(listItem.code)
    );
  }
  function handleColumnChange({ code, listItem }) {
    allFormData.value.strategyConfig = undefined;
    if (fixedOperator.includes(code)) {
      listItem.operator = operatorEnum.gt;
      listItem.value = 0;
    } else {
      listItem.operator = undefined;
      clearVal(listItem);
    }
  }
  function handleAdd() {
    model.value[listField].push(getTriggerConditionItem());
  }

  watch(
    () => model.value[listField].length,
    () => {
      allFormData.value.strategyConfig = undefined;
    }
  );
</script>

<style scoped lang="less">
  .replenish-tactic-condition {
    flex: 1;

    .list-item {
      display: flex;
      gap: 16px;
    }
  }
</style>
