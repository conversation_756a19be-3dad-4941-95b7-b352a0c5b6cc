<template>
  <a-modal
    :visible="visible"
    title="批量导入合成"
    width="800px"
    :mask-closable="false"
    body-style="max-height:calc(100vh - 150px); overflow-y: auto; padding: 24px"
    @ok="handleOk"
    :ok-text="okText"
    @before-ok="handleBeforeOk"
    :ok-loading="uploadLoading || okLoading"
    @cancel="handleCancel"
    v-if="visible"
    class="add-wrapper"
  >
    <DynamicForm v-model="formData" :form-schema="formSchema" ref="formRef" />
  </a-modal>
</template>

<script setup lang="tsx">
  import { ref, watch } from 'vue';
  import { cloneDeep } from 'lodash';
  import { Message } from '@arco-design/web-vue';
  import batchCreateApi from '@/api/sc-home/batch-create';
  import { formSchema, defaultFormData } from './form-config';

  import useUploadLoading from './hooks/use-upload-loading';

  const visible = defineModel<boolean>('visible');
  const formData = ref(cloneDeep(defaultFormData()));
  const { uploadLoading, setUploadLoading } = useUploadLoading();
  const okLoading = ref(false);
  const formRef = ref();
  const okText = ref('确认');
  const handleCancel = () => {
    visible.value = false;
    setUploadLoading(false);
  };
  const handleBeforeOk = async () => {
    const isValid = await formRef.value.validate();

    if (isValid) return true;
    return false;
  };
  function removeFileExtension(filename) {
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return filename;
    }
    return filename.substring(0, lastDotIndex);
  }
  const handleOk = async () => {
    if (uploadLoading.value) {
      Message.warning('请等待文件上传完成');
      return;
    }
    okLoading.value = true;
    const fileList = formData.value.file.files.filter((item: any) => {
      return item.status === 'done' && item.url;
    });
    if (!fileList.length) {
      Message.warning('请上传有效文件');
      okLoading.value = false;
      return;
    }
    const { noMusic, taskCount, showSubtitle } = formData.value;
    const taskItem =
      fileList.find(
        (item: any) =>
          !item.name.includes('片头') && !item.name.includes('片尾')
      ) || fileList[0];
    const params = {
      type: 1,
      noMusic,
      taskCount,
      showSubtitle,
      taskName: taskItem.folderName || removeFileExtension(taskItem.name),
      files: fileList.map((item: any) => item.url),
    };
    try {
      const { code, message } = await batchCreateApi.addMaterial(params);
      if (code === 1) {
        Message.success('提交成功');
        visible.value = false;
      } else {
        Message.error(message);
      }
    } finally {
      okLoading.value = false;
    }
  };

  watch(
    () => visible.value,
    (val) => {
      const handleBeforeUnload = (event) => {
        const confirmationMessage = '确定离开此页面？内容可能不会保存。';
        event.returnValue = confirmationMessage; // 对于兼容性，设置 returnValue
        return confirmationMessage; // 对于现代浏览器，返回提示信息
      };

      if (val) {
        window.addEventListener('beforeunload', handleBeforeUnload);
        formData.value = cloneDeep(defaultFormData());
      } else {
        formRef.value?.reset();
        okText.value = '确认';
        window.removeEventListener('beforeunload', handleBeforeUnload);
      }
    },
    {
      immediate: true,
    }
  );
</script>

<style scoped>
  .local-upload {
    position: fixed;
    bottom: 100px;
    right: 100px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.2);
    }
  }

  :deep(.arco-form-layout-inline .arco-form-item) {
    flex: 1;
    margin-bottom: 0;
  }

  :deep(.arco-radio-group) {
    display: flex;
  }
</style>
