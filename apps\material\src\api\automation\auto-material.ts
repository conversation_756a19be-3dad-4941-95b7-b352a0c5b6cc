import { maHttp, http } from '@/utils/http';

export const autoMaterial = {
  list: (data) => {
    return maHttp.post('/auto-tactics/list', data);
  },
  add: (data) => {
    return maHttp.post('/auto-tactics/add', data);
  },
  edit: (data) => {
    return maHttp.post('/auto-tactics/edit', data);
  },
  detail: (data) => {
    return maHttp.post('/auto-tactics/detail', data);
  },
  switch: (data) => {
    return maHttp.post('/auto-tactics/open', data);
  },
  query: (data) => {
    return maHttp.post('/auto-tactics/material-doing-num', data);
  },
  stop: (data) => {
    return maHttp.post('/auto-tactics/material-stop', data);
  },
  mediaList: (data) => {
    return http.post('/auto-tactics/media-list', data);
  },
  productList: (data) => {
    return http.get('/common/adApp/v1/listAllGroupByCompany', data);
  },
  accountList: (data) => {
    return http.post('/adplan/account/getAccountListByPermission', data);
  },
  status: (data) => {
    return http.post('/material/auto-tactics/material-check', data);
  },
  channelList: (data) => {
    return http.post('/transmit/channel/v1/list/post', data);
  },
};
export default null;
