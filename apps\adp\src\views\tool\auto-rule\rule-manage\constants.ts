import { mediaList } from '@/views/tool/common';
import { uniqueId } from 'lodash';

export const reportOriginMap = {
  quality: 1,
  apk: 2,
  fiction: 3,
};
export const reportOriginOptions = [
  { label: '质量分报表', value: reportOriginMap.quality },
  { label: 'APK报表', value: reportOriginMap.apk },
  { label: '代投报表', value: reportOriginMap.fiction },
];

export const DAILY_ACTION_COUNT_MAP = [
  { value: 1, label: '当日单次' },
  { value: 0, label: '持续监控' },
];

export const ruleTypeEnum = {
  allMedia: 1,
  singleMedia: 2,
};
export const RULE_TYPE_MAP = [
  { label: '多个媒体一套规则', value: ruleTypeEnum.allMedia },
  { label: '每个媒体一套规则', value: ruleTypeEnum.singleMedia },
];

export enum ruleTargetEnum {
  account = 'account',
  ad = 'ad',
}

export const RULE_TARGET_ENUM = [
  { label: '账户', value: ruleTargetEnum.account },
  { label: '广告', value: ruleTargetEnum.ad },
];

export const CHECK_TIME_MAP = [
  {
    label: '每天',
    value: 2,
  },
  {
    label: '每小时',
    value: 60,
  },
  {
    label: '每40分钟',
    value: 40,
  },
  {
    label: '每20分钟',
    value: 20,
  },
  {
    label: '每10分钟',
    value: 10,
  },
];

export function getTimeMap() {
  const resultTime: any = [];
  for (let i = 0; i < 24; i += 1) {
    resultTime.push({
      value: `${i < 10 ? `0${i}` : i}:00`,
      label: `${i < 10 ? `0${i}` : i}:00`,
    });
  }
  return resultTime;
}
export const operatorCodeMap = {
  gt: 'gt',
  lt: 'lt',
  eq: 'eq',
  ge: 'ge',
  le: 'le',
};
export const OPERATOR_MAP = [
  {
    label: '大于',
    value: operatorCodeMap.gt,
  },
  {
    label: '小于',
    value: operatorCodeMap.lt,
  },
  {
    label: '等于',
    value: operatorCodeMap.eq,
  },
  {
    label: '大于等于',
    value: operatorCodeMap.ge,
  },
  {
    label: '小于等于',
    value: operatorCodeMap.le,
  },
];

export const columnCodeMap = {
  timeRoi: 'real_time_ad_show_income_roi',
  adCloseTime: 'ad_close_time',
  mediaBalance: 'media_balance',
  campaignCreateTime: 'campaign_create_time',
};

export const actionMaps = {
  justNotify: 'just_notify',
  openAd: 'open_ad',
  closeAd: 'close_ad',
  deleteAd: 'delete_ad',
  openAndNotify: 'open&notify',
  closeAndNotify: 'close&notify',
  deleteAndNotify: 'delete&notify',
  updateBudget: 'update_budget',
  updateAndNotify: 'update&notify',
  recharge: 'recharge',
  rechargeAndNotify: 'recharge&notify',
};
export const actionRechargeVals = [
  actionMaps.recharge,
  actionMaps.rechargeAndNotify,
];

export const CONDITION_TYPE_MAP = [
  {
    label: '满足全部条件',
    value: 'all',
  },
  {
    label: '满足任一条件',
    value: 'any',
  },
];

export const REACH_NUM_MAP = [
  {
    label: '1',
    value: 1,
  },
  {
    label: '2',
    value: 2,
  },
  {
    label: '3',
    value: 3,
  },
  {
    label: '4',
    value: 4,
  },
  {
    label: '5',
    value: 5,
  },
];

export const getConditionListItem = () => {
  return {
    _rowKey: uniqueId('ConditionColumn'),
    columnCode: 'media_cost',
    operator: 'gt',
    comparisonValue: undefined,
  };
};

// 获取触发条件
export function getDefaultAutoRuleCondition() {
  return {
    conditionItems: [getConditionListItem()],
    conditionType: 'all',
    reachNum: 1,
    action: undefined,
    actionValue: undefined,
    beforeDay: 0,
  };
}
// all 媒体
export function getDefaultAutoRuleTargetItem() {
  const getDefaultAutoRuleMediaItem = () => {
    return {
      channelIds: [],
      brandCodes: [],
      isAllTarget: 1,
      mediaTarget: [],
      autoRuleCondition: getDefaultAutoRuleCondition(),
    };
  };
  return mediaList.map((item) => ({
    mediaPlatform: item.value,
    ...getDefaultAutoRuleMediaItem(),
    isCheck: false,
  }));
}

export function getRuleDefaultFormData() {
  return {
    ruleName: '',
    ruleType: ruleTypeEnum.allMedia,
    ruleTarget: ruleTargetEnum.account,
    applicationIds: [],
    status: 1,
    dataSource: reportOriginMap.quality,
    autoRuleTargetItem: getDefaultAutoRuleTargetItem(),
    autoRuleCondition: getDefaultAutoRuleCondition(),
    actionTime: 2,
    actionRepeat: 1,
    actionStartTime: '10:00',
    actionTimeRange: {
      actionStartTime: undefined,
      actionEndTime: undefined,
    },
    actionTimeValue: 0,
    autoRuleNotify: [
      {
        webHook: '',
        secret: '',
        userType: undefined,
      },
    ],
  };
}

export const beforeDayMap = {
  today: 0,
  yesterday: 1,
  threeDays: 3,
  sevenDays: 7,
};
export const beforeDayOptions = [
  { label: '当天', value: beforeDayMap.today },
  { label: '昨天', value: beforeDayMap.yesterday },
  { label: '近3天', value: beforeDayMap.threeDays },
  { label: '近7天', value: beforeDayMap.sevenDays },
];

export enum planMonitoringFromEnum {
  // 普通规则
  ordinary = 0,
  // 重点监控
  keyPoint = 1,
}
export const planMonitoringFromOptions = [
  {
    label: '普通规则',
    value: planMonitoringFromEnum.ordinary,
  },
  {
    label: '重点监控',
    value: planMonitoringFromEnum.keyPoint,
  },
];
