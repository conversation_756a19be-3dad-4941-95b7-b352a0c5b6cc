import ttAuth from '@/constants/auth-key/promotion/tt';
import ksAuth from '@/constants/auth-key/promotion/ks';
import gdtAuth from '@/constants/auth-key/promotion/gdt';
import adTaskAuth from '@/constants/auth-key/task-center/ad-task';
import IconAd from '@/components/icons/ad.vue';
import { automatedSetupTouTiaoAuth } from '@/constants/auth-key/automated-setup/toutiao';
import { automatedKsFictionAuth } from '@/constants/auth-key/automated-setup/ks-fiction';
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const PROMOTION: AppRouteRecordRaw = {
  path: '/promotion',
  name: 'promotion',
  component: DEFAULT_LAYOUT,
  meta: {
    title: '推广管理',
    requiresAuth: true,
    // icon: 'icon-sun',
    order: 0,
  },
  children: [
    {
      path: '/promotion/account',
      name: 'promotionAccount',
      // component: () => import('@/views/promotion/account/index.vue'),
      meta: {
        title: '账户管理',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-user-group',
      },
      children: [
        {
          path: '/promotion/account/tt',
          name: 'promotionAccountTt',
          component: () =>
            import('@/views/promotion/account/toutiao/index.vue'),
          meta: {
            title: '头条',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/ks',
          name: 'promotionAccountKs',
          component: () =>
            import('@/views/promotion/account/kuaishou/index.vue'),
          meta: {
            title: '快手',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/gdt',
          name: 'promotionAccountGdt',
          component: () => import('@/views/promotion/account/gdt/index.vue'),
          meta: {
            title: '广点通3.0',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/bd',
          name: 'promotionAccountBaidu',
          component: () => import('@/views/promotion/account/baidu/index.vue'),
          meta: {
            title: '百度',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/baidu',
          redirect: '/promotion/account/bd',
        },
        {
          path: '/promotion/account/huawei',
          name: 'promotionAccountHuawei',
          component: () => import('@/views/promotion/account/huawei/index.vue'),
          meta: {
            title: '华为',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/vivo',
          name: 'promotionAccountVivo',
          component: () => import('@/views/promotion/account/vivo/index.vue'),
          meta: {
            title: 'vivo',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/oppo',
          name: 'promotionAccountOppo',
          component: () => import('@/views/promotion/account/oppo/index.vue'),
          meta: {
            title: 'oppo',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/xiaomi',
          name: 'promotionAccountXiaomi',
          component: () => import('@/views/promotion/account/xiaomi/index.vue'),
          meta: {
            title: '小米',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/uc',
          name: 'promotionAccountUc',
          component: () => import('@/views/promotion/account/uc/index.vue'),
          meta: {
            title: 'UC',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/qqt',
          name: 'promotionAccountQtt',
          component: () => import('@/views/promotion/account/qtt/index.vue'),
          meta: {
            title: '趣头条',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/weibo',
          name: 'promotionAccountWeibo',
          component: () => import('@/views/promotion/account/weibo/index.vue'),
          meta: {
            title: '微博',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/alipay',
          name: 'promotionAccountAlipay',
          component: () => import('@/views/promotion/account/alipay/index.vue'),
          meta: {
            title: '支付宝',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/wifi',
          name: 'promotionAccountWifi',
          component: () => import('@/views/promotion/account/wifi/index.vue'),
          meta: {
            title: 'WIFI万能钥匙',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/aqy',
          name: 'promotionAccountAQY',
          component: () => import('@/views/promotion/account/aqy/index.vue'),
          meta: {
            title: '爱奇艺',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/honor',
          name: 'promotionAccountHonor',
          component: () => import('@/views/promotion/account/honor/index.vue'),
          meta: {
            title: '荣耀',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/account/sigmob',
          name: 'promotionAccountSigmob',
          component: () => import('@/views/promotion/account/sigmob/index.vue'),
          meta: {
            title: 'Sigmob',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
      ],
    },

    {
      path: '/promotion/ad',
      name: 'promotionAd',
      meta: {
        title: '广告管理',
        requiresAuth: true,
        roles: ['admin'],
        icon: IconAd as unknown as string,
      },
      children: [
        {
          path: '/promotion/ad/tt',
          name: 'promotionAdTt',
          component: () => import('@/views/promotion/toutiao/index.vue'),
          meta: {
            title: '头条',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/ad/tt/batch-create',
          name: 'promotionAdTtBatchCreate',
          component: () => import('@/views/batch-create-ad/toutiao/index.vue'),
          meta: {
            title: '头条创建',
            hideInMenu: true,
            requiresAuth: true,
            bindButton: ttAuth.PROMOTION_TT_BATCH_CREATE,
            roles: ['admin'],
            hideLeftMenu: true,
          },
        },
        {
          path: '/promotion/ad/tt/batch-preview',
          name: 'promotionAdTtBatchPreview',
          component: () =>
            import('@/views/batch-create-ad/toutiao-preview/index.vue'),
          meta: {
            title: '头条创建预览',
            hideInMenu: true,
            requiresAuth: true,
            bindButton: adTaskAuth.TASK_CREATE_AD_TT_DETAIL,
            roles: ['admin'],
            hideLeftMenu: true,
          },
        },
        {
          path: '/promotion/ad/ks',
          name: 'promotionAdKs',
          component: () => import('@/views/promotion/kuaishou/index.vue'),
          meta: {
            title: '快手',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/ad/ks/batch-create',
          name: 'promotionAdKsBatchCreate',
          component: () => import('@/views/batch-create-ad/kuaishou/index.vue'),
          meta: {
            title: '快手创建',
            hideInMenu: true,
            bindButton: ksAuth.PROMOTION_KS_BATCH_CREATE,
            requiresAuth: true,
            roles: ['admin'],
            hideLeftMenu: true,
          },
        },

        {
          path: '/promotion/ad/ks/batch-preview',
          name: 'promotionKsTtBatchPreview',
          component: () =>
            import('@/views/batch-create-ad/kuaishou-preview/index.vue'),
          meta: {
            title: '快手创建预览',
            hideInMenu: true,
            requiresAuth: false,
            bindButton: adTaskAuth.TASK_CREATE_AD_KS_DETAIL,
            roles: ['admin'],
            hideLeftMenu: true,
          },
        },
        {
          path: '/promotion/ad/gdt',
          name: 'promotionAdTencent',
          component: () => import('@/views/promotion/gdt/index.vue'),
          meta: {
            title: '广点通3.0',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/promotion/ad/tencent/batch-create',
          name: 'promotionAdTencentBatchCreate',
          component: () => import('@/views/batch-create-ad/tencent/index.vue'),
          meta: {
            title: '广点通创建',
            hideInMenu: true,
            bindButton: gdtAuth.PROMOTION_GDT_BATCH_CREATE,
            requiresAuth: true,
            roles: ['admin'],
            hideLeftMenu: true,
          },
        },
      ],
    },
    {
      path: '/automated-setup',
      name: 'automatedSetup',
      meta: {
        title: '自动创建',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-relation',
      },
      children: [
        {
          path: '/automated-setup/toutiao',
          name: 'automatedSetupToutiao',
          component: () => import('@/views/automated-setup/toutiao/index.vue'),
          meta: {
            title: '头条',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/automated-setup/toutiao/config',
          name: 'automatedSetupToutiaoConfig',
          component: () =>
            import('@/views/automated-setup/toutiao/config/index.vue'),
          meta: {
            title: '头条策略',
            requiresAuth: true,
            hideInMenu: true,
            roles: ['admin'],
            bindButton: [
              automatedSetupTouTiaoAuth.AUTOMATED_SETUP_TOUTIAO_CREATE,
              automatedSetupTouTiaoAuth.AUTOMATED_SETUP_TOUTIAO_MODIFY,
              automatedSetupTouTiaoAuth.AUTOMATED_SETUP_TOUTIAO_COPY,
            ],
          },
        },
        {
          path: '/automated-setup/ks-fiction',
          name: 'automatedSetupKsFiction',
          component: () =>
            import('@/views/automated-setup/ks-fiction/index.vue'),
          meta: {
            title: '快手-付费小说',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/automated-setup/ks-fiction/config',
          name: 'automatedSetupKsFictionConfig',
          component: () =>
            import('@/views/automated-setup/ks-fiction/config/index.vue'),
          meta: {
            title: '快手-付费小说-策略',
            requiresAuth: true,
            roles: ['admin'],
            hideInMenu: true,
            bindButton: [
              automatedKsFictionAuth.AUTOMATED_SETUP_KS_FICTION_CREATE,
              automatedKsFictionAuth.AUTOMATED_SETUP_KS_FICTION_MODIFY,
              automatedKsFictionAuth.AUTOMATED_SETUP_KS_FICTION_COPY,
            ],
          },
        },
      ],
    },
    {
      path: '/promotion/agentManage',
      name: 'agentManage',
      component: () => import('@/views/promotion/agent-manage/index.vue'),
      meta: {
        title: '代理商管理',
        requiresAuth: true,
        icon: 'icon-storage',
        roles: ['admin'],
      },
    },
  ],
};

export default PROMOTION;
