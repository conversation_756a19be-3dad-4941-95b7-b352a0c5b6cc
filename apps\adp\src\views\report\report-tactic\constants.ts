export const ellipsisOption = {
  tooltip: true,
  ellipsis: true,
};

export const reportTacticContext = 'REPORT_TACTIC_CONTEXT';
export const reportTacticBrandOptions = [
  {
    label: '华为',
    value: 'huawei',
  },
  {
    label: '小米',
    value: 'xiaomi',
  },
  {
    label: 'VIVO',
    value: 'vivo',
  },
  {
    label: 'OPPO',
    value: 'oppo',
  },
  {
    label: '荣耀',
    value: 'honor',
  },
  {
    label: '其他',
    value: 'other',
  },
];
export const TACTIC_DIM_ALL = 'ALL';
export const reportTacticDimensionOptions = [
  {
    label: '不限',
    value: TACTIC_DIM_ALL,
    tableColumns: [],
  },
  {
    label: '账户',
    value: 'account',
    tableColumns: [
      { title: '账户ID', dataIndex: 'account_id' },
      { title: '账户名称', dataIndex: 'account_name' },
    ],
  },
  {
    label: '链接',
    value: 'link',
    tableColumns: [{ title: '链接ID', dataIndex: 'link_id' }],
  },
  // {
  //   label: '创意',
  //   value: 'creative',
  //   tableColumns: [{ title: '创意ID', dataIndex: 'creative_id' }],
  // },
  // {
  //   label: '计划',
  //   value: 'campaign_id',
  //   tableColumns: [{ title: '计划ID', dataIndex: 'campaign_id' }],
  // },
  {
    label: '策略应用',
    value: 'strategy_apply',
    tableColumns: [{ title: '策略应用名称', dataIndex: 'strategy_apply' }],
  },
  {
    label: '厂商维度',
    value: 'brand',
    tableColumns: [{ title: '厂商名称', dataIndex: 'brand_code' }],
  },
  {
    label: '广告策略',
    value: 'ad_placement_config',
    tableColumns: [{ title: '广告策略名称', dataIndex: 'ad_strategy' }],
  },
  {
    label: '行为策略',
    value: 'action_strategy',
    tableColumns: [{ title: '行为策略名称', dataIndex: 'action_strategy' }],
  },
  {
    label: '设备型号',
    value: 'device_model',
    tableColumns: [{ title: '设备型号', dataIndex: 'device_model' }],
  },
  {
    label: '首次访问状态',
    value: 'newer_state',
    tableColumns: [{ title: '首次访问状态', dataIndex: 'newer_state' }],
  },
  {
    label: '当前启动渠道ID',
    value: 'channel_code',
    tableColumns: [{ title: '当前启动渠道ID', dataIndex: 'channel_code' }],
  },
  {
    label: '系统版本',
    value: 'os_version',
    tableColumns: [{ title: '系统版本', dataIndex: 'os_version' }],
  },
  // {
  //   label: '资源ID',
  //   value: 'source_id',
  //   tableColumns: [{ title: '资源ID', dataIndex: 'source_id' }],
  // },
  {
    label: '应用版本',
    value: 'app_version',
    tableColumns: [{ title: '应用版本', dataIndex: 'app_version' }],
  },
  {
    label: 'sdk版本',
    value: 'lib_version',
    tableColumns: [{ title: 'sdk版本', dataIndex: 'lib_version' }],
  },
  {
    label: '首次拉取产品',
    value: 'first_pull_product',
    tableColumns: [{ title: '首次拉取产品', dataIndex: 'first_pull_product' }],
  },
  {
    label: '人群分组',
    value: 'segmentation',
    tableColumns: [{ title: '人群分组', dataIndex: 'segmentation' }],
  },
];

export const tacticConstantsColumns = [
  {
    title: '日期',
    dataIndex: 'date',
    width: 100,
    // sortable: {
    //   sortDirections: ['ascend', 'descend'],
    //   sorter: true,
    // },
  },
  { title: '产品名称', dataIndex: 'app_name', width: 100 },
];
export const tacticEndColumns = [
  {
    title: '优数更新时间',
    dataIndex: 'bury_update_time',
    width: 170,
  },
];
export const tacticReportType = {
  reportType: 3,
};

// eslint-disable-next-line no-shadow
export enum tableFromEnum {
  table = 'table',
  total = 'total',
  export = 'export',
}
export default null;
