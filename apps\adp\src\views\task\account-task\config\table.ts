import { operationTask } from '@/api/task/operation-task';
import useAppId from '@/hooks/app-id';
import { platformAccountType } from '@/views/task/account-task/constants';
import { mediaPlatformMap } from '@/views/promotion/hooks/authorization';
import { h } from 'vue';
import DetailContent from '../components/detail-content.vue';

export function getTableJson(currentPage: platformAccountType) {
  const appId = useAppId();
  const detailsOperation = {
    text: '详情',
    auth: `task_account_${currentPage.value}_detail`,
    props: {
      type: 'text',
    },
    clickActionType: 'drawer',
    linkUrl: '',
    modal: {
      props: {
        title: '详情',
        width: 880,
        hideCancel: true,
      },
      contentType: 'custom',
      custom: h(DetailContent, {
        currentPage,
      }),
    },
  };

  const operationColumns = {
    title: '操作',
    dataIndex: 'operations',
    customRender: {
      type: 'operations',
      props: {
        operations: [detailsOperation],
      },
    },
  };

  return {
    load: {
      action: async (filter, pagination) => {
        return operationTask.list?.({
          mediaPlatform: mediaPlatformMap[currentPage.mediaType],
          applicationId: appId.value,
          taskSource: 1,
          ...filter,
          ...pagination,
        });
      },
    },
    columns: [
      {
        title: '任务名称',
        dataIndex: 'taskName',
      },
      {
        title: '操作类型',
        dataIndex: 'taskTypeName',
      },
      {
        title: '提交数量',
        dataIndex: 'operationCount',
      },
      {
        title: '创建时间',
        dataIndex: 'createAt',
      },
      {
        title: '操作结果',
        dataIndex: 'statusName',
        render: ({ record }) => {
          return record.statusName;
        },
      },
      operationColumns,
    ],
  };
}
