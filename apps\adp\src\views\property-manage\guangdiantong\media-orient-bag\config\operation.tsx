import { Notification, Message } from '@arco-design/web-vue';
import { DButton } from '@repo/kola/src/components/dynamic/types/button';
import useAppId from '@/hooks/app-id';
import { targetPackageApi, getGDTAccountList } from '@/api/property-manage';
import gdtPackageAuth from '@/constants/auth-key/property/gdt';

const titleText = '获取媒体定向模板';

const appId = useAppId();
const operation: DButton[] = [
  {
    text: titleText,
    auth: gdtPackageAuth.PROPERTY_GDT_PACKAGE_MEDIA_ACQUIRE,
    props: {
      type: 'outline',
    },
    clickActionType: 'modal',
    modal: {
      props: {
        title: titleText,
        width: '1000px',
        fullscreen: false,
      },
      contentType: 'form',
      form: {
        formSchema: {
          fields: [
            {
              label: '选择媒体账户',
              name: 'accountList',
              span: 24,
              type: 'transferSimple',
              sourceTitle: ({ countTotal }) => `媒体账户(${countTotal})`,
              targetTitle: ({ countTotal }) => `已选(${countTotal})`,
              required: true,
              remote: true,
              // rules: [{ maxLength, message: `最多选择${maxLength}个账户` }],
              source: {
                data: async (_field, formData, _path, text) => {
                  return getGDTAccountList({
                    applicationId: appId.value,
                    pageNum: 1,
                    pageSize: 100,
                    keyWord: text,
                  }).then((res) => {
                    return (res.data.list || []).map((item) => {
                      return {
                        label: `${item.advertiserName}(${item.advertiserId})`,
                        value: item.advertiserId,
                      };
                    });
                  });
                },
              },
              rules: [{ required: true }],
            },
          ],
        },
      },
      action: async ({ formData, refreshTable }: any) => {
        const productId = useAppId();
        const params = {
          channelId: 102,
          productId: productId.value,
          advertiserIds: formData.accountList,
        };

        const {
          data: { status, failList },
        } = await targetPackageApi.syncData(params);
        if (status === 'SUCCESS') {
          Message.success('同步成功');
        } else if (status === 'PARTIAL_SUCCESS') {
          Message.warning('部分同步成功');
          const contentHtml = (
            <div>
              {failList.map((errItem, index) => (
                <p key={index}>
                  {errItem.item}:{errItem.errorMsg}
                </p>
              ))}
            </div>
          );
          Notification.warning({
            title: '同步失败账户',
            content: contentHtml,
            closable: true,
            duration: 6000,
          });
        } else {
          Message.error('同步失败');
        }

        refreshTable();
      },
    },
  },
];
export default operation;
