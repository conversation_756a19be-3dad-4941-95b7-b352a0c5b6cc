import { computed, nextTick, onMounted, ref, Ref, unref, watch } from 'vue';

import {
  columnCodeMap,
  REACH_NUM_MAP,
} from '@/views/tool/auto-rule/rule-manage/constants';
import { useTriggerPath } from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/hooks/use-trigger-path';
import { useCurrentReport } from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/hooks/use-current-report';
import { useFieldColumns } from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/hooks/use-field-columns';
import { useDateMatch } from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/hooks/use-date-match';
import { useCheckMedias } from './use-check-medias';
import { useRuleType } from './use-rule-type';

import { useShowActionRepeat } from './use-show-action-repeat';
import { useGetQuantityFields } from './use-get-quantity-fields';
import { useActionManagement } from './use-action-management';

interface TriggerModeOptions {
  allFormData: Ref<any>;
  modelValue: Ref<any>;
  path: string;
}

export function useTriggerRule({
  allFormData,
  modelValue,
  path,
}: TriggerModeOptions) {
  // 1. 获取已选中的媒体
  const { checkMedias, checkOnlyMedia, checkMediaMap } =
    useCheckMedias(allFormData);

  // 2. 规则类型
  const { ruleTypeFlag } = useRuleType(allFormData);

  // 4. 路径映射.
  const { currentPathMap, pathMap } = useTriggerPath({ path });
  // 5. 展示充值相关
  const showRecharge = computed(() => {
    return (
      checkOnlyMedia.value.toutiao &&
      (currentPathMap.value?.toutiao || currentPathMap.value?.all)
    );
  });

  const { actionOptions, isRechargeAction, resetActionVal } =
    useActionManagement({
      allFormData,
      modelValue,
      showRecharge,
    });

  // 7. 是否显示重复操作
  const { showActionRepeat } = useShowActionRepeat({
    modelValue,
    checkMediaMap,
    currentPathMap,
    cb: (value) => {
      if (!value) {
        allFormData.value.actionRepeat = undefined;
      }
    },
  });
  // 10. 获取质量分报表自定义字段
  const { quantityFieldsCodes, getReportQuantityFields } = useGetQuantityFields(
    {
      ruleTarget: allFormData.value.ruleTarget,
    }
  );
  const { currentReport } = useCurrentReport({
    allFormData,
  });

  const { dateOptions } = useDateMatch({
    currentReport,
    ruleTarget: computed(() => allFormData.value.ruleTarget),
    currentPathMap,
    checkOnlyMedia,
  });

  // 获取对应报表 columns

  const { apkColumns, qualityColumns, fictionColumns } = useFieldColumns({
    allFormData,
    quantityFieldsCodes,
    modelValue,
    currentPathMap,
    checkOnlyMedia,
    checkMediaMap,
    checkMedias,
  });

  const fieldColumns = computed(() => {
    const curReport = unref(currentReport.value);
    if (curReport.apk) {
      return apkColumns.value;
    }
    if (curReport.quality) {
      return qualityColumns.value;
    }
    if (curReport.fiction) {
      return fictionColumns.value;
    }
    return [];
  });

  // 12. 连续满足次数映射
  const computedRetchNumMap = computed(() => {
    const { actionTime } = allFormData.value;
    return REACH_NUM_MAP.map((item) => ({
      ...item,
      disabled: actionTime === 2 && item.value !== 1,
    }));
  });

  // 禁用满足条件
  const disableConditionType = computed(() => {
    if (
      modelValue.value.conditionItems.some(
        (item) => item.columnCode === columnCodeMap.campaignCreateTime
      )
    ) {
      return true;
    }
    return undefined;
  });

  // 监听规则目标变化
  watch(
    () => [allFormData.value.ruleTarget, currentReport.value],
    () => {
      nextTick(() => {
        if (currentReport.value.quality) {
          getReportQuantityFields();
        }
      });
    },
    {
      immediate: true,
    }
  );
  watch(
    () => checkMedias.value,
    () => {
      if (!showRecharge.value) {
        if (isRechargeAction.value) {
          // 是充值
          modelValue.value.action = undefined;
          resetActionVal();
        }
      }
    }
  );
  return {
    actionOptions,
    showActionRepeat,
    ruleTypeFlag,
    isRechargeAction,
    fieldColumns,
    computedRetchNumMap,
    resetActionVal,
    currentPathMap,
    dateOptions,
    disableConditionType,
  };
}
