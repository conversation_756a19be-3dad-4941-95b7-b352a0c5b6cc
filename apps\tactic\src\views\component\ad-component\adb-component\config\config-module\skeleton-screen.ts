import switchConfig from '@/views/tactics/common/switch-config';

export default {
  getFormSchema() {
    return {
      label: '骨架屏配置',
      name: 'skeletonScreenConfig',
      type: 'object',
      viewType: 'card',
      fields: [
        {
          label: '启用开关',
          name: 'enable',
          ...switchConfig,
          span: 8,
          rules: [{ required: true }],
        },
        {
          label: '骨架屏背景不透明度',
          name: 'skeletonScreenOpaque',
          type: 'number',
          span: 8,
          suffix: '%',
          tooltip: '骨架屏黑色蒙版透明度调节',
          visibleOn: (data) => {
            return data.enable === 1;
          },
        },
        {
          label: '自定义骨架屏样式',
          name: 'customStyle',
          type: 'file',
          accept: '.jpg,.png,.gif,.json',
          span: 8,
          visibleOn: (data) => {
            return data.enable === 1;
          },
          tooltip: '填充的素材不论像素多大都会被渲染至150*150居中对齐摆放',
        },
        {
          label: '骨架屏展示时间',
          name: 'displayTime',
          type: 'number',
          require: true,
          span: 8,
          suffix: 'ms',
          tooltip:
            '非首个弹窗在触发时机触发后先展示骨架屏一定时间后，再展示有广告弹窗',
          visibleOn: (data) => {
            return data.enable === 1;
          },
        },
      ],
    };
  },
  getFormData() {
    return {
      skeletonScreenConfig: {
        enable: 2,
        customStyle: '',
        skeletonScreenOpaque: 80,
        displayTime: 0,
      },
    };
  },
};
