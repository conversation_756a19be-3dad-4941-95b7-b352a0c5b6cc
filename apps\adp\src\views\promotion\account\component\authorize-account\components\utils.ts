import { find } from 'lodash';

export function linkMatchLinkType({ adCountList, linkOptions }) {
  if (Array.isArray(adCountList) && Array.isArray(linkOptions)) {
    adCountList.forEach((adCountItem) => {
      if (adCountItem.linkId) {
        const linkItem = find(linkOptions, { value: adCountItem.linkId });
        if (linkItem) {
          adCountItem.transmitStatus = linkItem.linkType === 1 ? 1 : null;
        }
      }
    });
  }
}
