import { operationTask } from '@/api/task/operation-task';
import { keys, map } from 'lodash';
import {
  platformAccountType,
  taskTableStatus,
} from '@/views/task/account-task/constants';
import { mediaPlatformMap } from '@/views/promotion/hooks/authorization';

export function getFilter(currentPage: platformAccountType) {
  return {
    formSchema: {
      fields: [
        {
          label: '操作结果',
          name: 'status',
          type: 'select',
          format: 'multipleSelect',
          source: {
            data: taskTableStatus,
          },
        },
        {
          label: '操作类型',
          name: 'taskType',
          type: 'select',
          format: 'multipleSelect',
          style: {
            width: '260px',
          },
          select: {
            allowClear: true,
            allowSearch: true,
          },
          source: {
            async data(field, formData) {
              const res = await operationTask.getOperationType({
                mediaPlatform: mediaPlatformMap[currentPage.mediaType],
                taskSource: 1,
              });
              return map(keys(res.data), (item) => {
                return {
                  value: item,
                  label: res.data[item],
                };
              });
            },
          },
        },
        {
          label: '任务名称',
          name: 'name',
          placeholder: '任务名称',
          type: 'text',
        },
      ],
    },
    defaultFormData: {
      status: [],
      taskType: [],
      name: undefined,
    },
  };
}
