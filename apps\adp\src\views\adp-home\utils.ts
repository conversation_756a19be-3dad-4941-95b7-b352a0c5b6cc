import { InjectionKey } from 'vue';

export const selectEnum = {
  // 指标
  dataIndex: 'dataIndex',
  // 产品
  app: 'app',
  // 单选媒体
  singleMedia: 'singleMedia',
  // 多选媒体
  multipleMedia: 'multipleMedia',
  // 优化师
  optimiser: 'optimiser',
  // date
  date: 'date',
};
export interface HomeContext {
  getSearchMaps: ({ key }: { key: string }) => Record<string, any>;
}

export const HomeInjectKey: InjectionKey<HomeContext> = Symbol('AdpHome');

export const getMediaList = () => [
  { label: '今日头条', value: 'toutiao2' },
  { label: '快手', value: 'kuaishou' },
  { label: '广点通3.0', value: 'gdt3' },
  { label: '百度', value: 'baidu' },
  { label: '华为', value: 'huawei' },
  { label: 'VIVO', value: 'vivo' },
  { label: 'OPPO', value: 'oppo' },
  { label: '小米', value: 'xiaomi' },
  { label: '趣头条', value: 'qtt' },
  { label: 'UC', value: 'uc' },
  { label: '支付宝', value: 'alipay' },
  { label: 'WIFI万能钥匙', value: 'wifi' },
  { label: '微博', value: 'weibo' },
  { label: '荣耀', value: 'honor' },
  { label: '爱奇艺', value: 'iqiyi' },
  { label: 'Sigmob', value: 'sigmob' },
];

export const normEnum = {
  mediaCost: 'media_cost',
  mediaConvertRatio: 'media_convert_ratio',
  mediaConvertCost: 'media_convert_cost',
  qualityScore: 'quality_score',
};
export const hideSuffixList = [
  {
    fields: [normEnum.mediaConvertRatio],
    suffix: '%',
  },
];
export const getNormList = () => [
  { label: '消耗', value: normEnum.mediaCost },
  { label: '转化率', value: normEnum.mediaConvertRatio },
  { label: '转化成本', value: normEnum.mediaConvertCost },
  { label: '质量分', value: normEnum.qualityScore },
];

export const productEnum = {
  cpmScore: 'cpm_score',
  qualityScore: 'quality_score',
  buyROI: 'buy_roi',
  arpu: 'arpu',
  costPerUv: 'cost_per_uv',
  cpmArpu: 'cpm_arpu',
};

export const getProductConsume = () => [
  { label: 'cpm质量分', value: productEnum.cpmScore },
  { label: '质量分', value: productEnum.qualityScore },
  { label: '快应用变现ROI', value: productEnum.buyROI },
  // { label: '优数arpu', value: productEnum.arpu },
  // { label: 'cpm arpu', value: productEnum.cpmArpu },
  { label: 'uv成本', value: productEnum.costPerUv },
];

export function getHideSuffix(field: string) {
  const hideItem = hideSuffixList.find((item) => item.fields.includes(field));
  return hideItem ? hideItem.suffix : '';
}

export function getSearchValue(searchRef) {
  return searchRef.value?.getValue?.() ?? {};
}

export const getTooltipPosition = (point, params, dom, rect, size) => {
  // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
  // 提示框位置
  let x = 0; // x坐标位置
  let y = 0; // y坐标位置

  // 当前鼠标位置
  const pointX = point[0];
  const pointY = point[1];

  // 外层div大小
  // var viewWidth = size.viewSize[0];
  // var viewHeight = size.viewSize[1];

  // 提示框大小
  const boxWidth = size.contentSize[0];
  const boxHeight = size.contentSize[1];

  // boxWidth > pointX 说明鼠标左边放不下提示框
  if (boxWidth > pointX) {
    x = 5;
  } else {
    // 左边放的下
    x = pointX - boxWidth;
  }

  // boxHeight > pointY 说明鼠标上边放不下提示框
  if (boxHeight > pointY) {
    y = 5;
  } else {
    // 上边放得下
    y = pointY - boxHeight;
  }

  return [x, y];
};
export default null;
