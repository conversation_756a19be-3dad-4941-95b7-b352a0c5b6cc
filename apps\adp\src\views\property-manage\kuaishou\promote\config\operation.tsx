import { DButton } from '@repo/kola/src/components/dynamic/types/button';
import { Message, Notification } from '@arco-design/web-vue';
import useAppId from '@/hooks/app-id';
import { getKSAccountList } from '@/api/property-manage';
import { promoteApi } from '@/api/property-manage/promote';

const titleText = '拉取资产';

const appId = useAppId();

export const getLandingConfig: DButton = {
  text: titleText,
  props: {
    type: 'primary',
  },
  clickActionType: 'modal',
  modal: {
    props: {
      title: titleText,
      width: '1000px',
      fullscreen: false,
    },
    contentType: 'form',
    form: {
      formSchema: {
        fields: [
          {
            label: '选择媒体账户',
            name: 'accountList',
            span: 24,
            type: 'transferSimple',
            sourceTitle: ({ countTotal }) => `媒体账户(${countTotal})`,
            targetTitle: ({ countTotal }) => `已选(${countTotal})`,
            required: true,
            remote: true,
            // rules: [{ maxLength, message: `最多选择${maxLength}个账户` }],
            source: {
              data: async (_field, formData, _path, text) => {
                return getKSAccountList({
                  applicationId: appId.value,
                  isValid: 1,
                  isOpen: 1,
                  timeType: 1,
                  pageNum: 1,
                  pageSize: 1000,
                  keyWord: text,
                }).then((res) => {
                  return (res.data.list || []).map((item) => {
                    return {
                      label: `${item.advertiserName}(${item.advertiserId})`,
                      value: item.advertiserId,
                    };
                  });
                });
              },
            },
            rules: [{ required: true }],
          },
        ],
      },
    },
    action: async ({ formData, refreshTable }: any) => {
      const params = {
        advertiserIds: formData.accountList,
        assetTypes: ['KS_UID', 'DPA'],
      };
      const { msg } = await promoteApi.sync(params);

      if (msg === 'success') {
        Message.success('同步成功');
      } else {
        Message.error('同步失败');
      }

      refreshTable();
    },
  },
};

const operation: DButton[] = [getLandingConfig];

export default operation;
