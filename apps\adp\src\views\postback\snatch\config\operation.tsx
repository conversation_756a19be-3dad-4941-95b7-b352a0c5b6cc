import postbackSnatchAuth from '@/constants/auth-key/postback/postback-snatch';
import { cloneDeep, compact, find, isEmpty, isNil, omit } from 'lodash';
import { Message } from '@arco-design/web-vue';
import { postbackChannelOtherApi } from '@/api/postback/channel/category';

import { postbackProductChannelOtherApi } from '@/api/postback/channel/product';
import { useAppStore } from '@/store';
import { hydrateDeep } from '@/utils';
import { postbackSnatchApi } from '@/api/postback/snatch';
import { commonApi } from '@/api/common';
import {
  getGlobalAppFormSchema,
  getOneCompanyAppValues,
  valueMatchApps,
} from '@/utils/global-app';
import {
  formMode,
  formModeType,
  getTriggerConditionItem,
  operatorEnum,
  snatchTacticsOptions,
  typeEnum,
  inputNumberCodes,
} from '../constants';
import ReplenishTacticConfig from '../components/replenish-tactic-config/index.vue';

export function getAttrByPkg(pkgName, attr) {
  const appStore = useAppStore();
  return appStore.appInfo?.appList.find(
    (item) => item.packageName === pkgName
  )?.[attr];
}

function getFormSchema({ mode }: { mode: formModeType }) {
  const appStore = useAppStore();
  const currentApp = appStore.curApp;
  const isModify = mode === formMode.modify;

  return {
    fields: [
      {
        type: 'viewCard',
        label: '基础配置',
        fields: [
          getGlobalAppFormSchema(
            {
              label: '应用名称',
              name: 'pkg',
              hideLevel1Multiple: true,
              disabled: isModify,
              onChange(_, formData) {
                formData.value.channelIdList = [];
                formData.value.linkId = [];
                formData.value.pkg = getOneCompanyAppValues({
                  value: formData.value.pkg,
                  field: 'packageName',
                  diffField: 'departmentCode',
                });
              },
            },
            'packageName'
          ),
          {
            label: '策略名称',
            name: 'strategyName',
            type: 'text',
            required: true,
            maxLength: 50,
          },
          {
            label: '渠道分类',
            name: 'channelTypeId',
            type: 'select',
            format: 'singleSelect',
            select: {
              allowSearch: true,
              allowClear: true,
            },
            source: {
              data: async (_, form) => {
                return postbackChannelOtherApi
                  .mapList({
                    filterStatus: 0,
                  })
                  .then((res) => {
                    return res.data?.map((item) => {
                      return {
                        label: item.channelTypeName,
                        value: item.channelTypeId,
                      };
                    });
                  });
              },
            },
            required: true,
            disabled: isModify,
            onChange(_, formData) {
              formData.value.channelIdList = [];
              formData.value.linkList = [];
            },
          },
          {
            label: '渠道名称',
            name: 'channelIdList',
            type: 'select',
            format: 'multipleSelect',
            select: {
              allowSearch: true,
              allowClear: true,
              showAll: true,
            },
            source: {
              data: async (_, formData) => {
                const { channelTypeId, pkg } = formData;
                if (!channelTypeId || isNil(pkg) || pkg === '') {
                  return [];
                }
                return postbackProductChannelOtherApi
                  .mapList({
                    channelTypeId,
                    appIdList: valueMatchApps({
                      value: pkg,
                      field: 'packageName',
                    })
                      .map((item) => item.appId)
                      .join(','),
                    filterStatus: 0,
                  })
                  .then((res) => {
                    return res.data.map((item) => {
                      const appName = getAttrByPkg(item.pkg, 'appName');
                      return {
                        label: `${item.channelName}_${item.channelCode}_${appName}`,
                        value: item.id,
                      };
                    });
                  });
              },
            },
            onChange(_, formData) {
              formData.value.linkList = [];
            },
          },
          {
            label: '链接ID',
            name: 'linkList',
            type: 'select',
            format: 'multipleSelect',
            select: {
              allowSearch: true,
              allowClear: true,
              allowPaste: true,
              showAll: true,
              maxTagCount: 3,
              valueKey: 'idPkg',
            },
            source: {
              data: async (_field, form) => {
                const channelIdList = form?.channelIdList;
                if (channelIdList.length <= 0) {
                  return [];
                }
                const { data } = await commonApi.getChannelAllLinkList({
                  channelIds: channelIdList,
                });
                return data.map((item) => {
                  const appName = getAttrByPkg(item.pkg, 'appName');
                  return {
                    label: `${item.linkId}_${appName}`,
                    value: { ...item, idPkg: item.linkId + item.pkg },
                  };
                });
              },
            },
          },
        ],
      },
      {
        type: 'viewCard',
        label: '策略配置',
        fields: [
          {
            name: 'conditionConfigInfo',
            label: '用户类型',
            hideLabel: true,
            type: 'custom',
            component: ReplenishTacticConfig,
          },
          {
            label: '回传策略',
            name: 'strategyConfig',
            type: 'select',
            format: 'singleSelect',
            select: {
              allowSearch: true,
              allowClear: true,
            },
            required: true,
            source: {
              data: (value, formData) => {
                const selectedCode = compact(
                  formData.conditionConfigInfo.quotaList
                    .map((item) => {
                      return item.code;
                    })
                    .filter((item) => {
                      return item !== 'time';
                    })
                );
                const options = cloneDeep(snatchTacticsOptions);
                for (let i = 0; i < selectedCode.length; i += 1) {
                  if (
                    [typeEnum.clickNum, typeEnum.clickPkg].includes(
                      selectedCode[i]
                    )
                  ) {
                    return options.map((item) => {
                      item.disabled = item.value === 'installNum';
                      return item;
                    });
                  }
                  if (
                    [typeEnum.installNum, typeEnum.installPkg].includes(
                      selectedCode[i]
                    )
                  ) {
                    return options.map((item) => {
                      item.disabled = item.value === typeEnum.clickNum;
                      return item;
                    });
                  }
                }
                return snatchTacticsOptions;
              },
            },
          },
        ],
      },
    ],
  };
}
function getDefaultFormData() {
  return {
    pkg: undefined,
    strategyName: '',
    channelTypeId: undefined,
    channelIdList: [],
    linkList: [],
    conditionConfigInfo: {
      quotaList: [
        {
          ...getTriggerConditionItem(),
          operator: operatorEnum.equal,
          code: 'time',
          value: 'LAST_3_DAY',
        },
        getTriggerConditionItem(),
      ],
    },
    strategyConfig: undefined,
  };
}
export function createOperation({ mode }: { mode: formModeType }) {
  const baseText = '回传策略';
  const { text, title, auth, icon, type, api } = {
    [formMode.create]: {
      text: '配置回传策略',
      title: '新增',
      auth: postbackSnatchAuth.POSTBACK_SNATCH_CREATE,
      icon: 'icon-plus',
      type: 'primary',
      api: postbackSnatchApi.create,
    },
    [formMode.modify]: {
      text: '编辑',
      title: '编辑',
      auth: postbackSnatchAuth.POSTBACK_SNATCH_MODIFY,
      api: postbackSnatchApi.modify,
    },
    [formMode.copy]: {
      text: '复制',
      title: '复制',
      auth: postbackSnatchAuth.POSTBACK_SNATCH_COPY,
      api: postbackSnatchApi.create,
    },
  }[mode];
  const isModify = mode === formMode.modify;
  const isCopy = mode === formMode.copy;
  return {
    text,
    auth,
    props: {
      type: type || 'text',
    },
    icon,
    clickActionType: 'drawer',
    modal: {
      props: {
        title: `${title}${baseText}`,
        width: 900,
      },
      contentType: 'form',
      form: {
        formSchema: getFormSchema({ mode }),
      },
      async getDefaultValue(record) {
        const defaultData = getDefaultFormData();
        if (isModify || isCopy) {
          const res = await postbackSnatchApi.detail({
            strategyId: record.id,
          });
          res.data.linkList = res.data.linkList.map((item) => {
            return {
              ...item,
              idPkg: item.linkId + item.pkg,
            };
          });
          const data: any = formatApiData({
            defaultData,
            apiData: res.data ?? {},
          });
          if (isModify) {
            data.id = record.id;
            data.status = record.status;
          }
          if (isCopy) {
            data.strategyName = '';
          }
          return data;
        }
        return defaultData;
      },
      action: async (data) => {
        const { formData, refreshTable } = data;
        const submitData = {
          ...formatSubmitData(cloneDeep(formData)),
          updateUid: window.$userInfo.id,
          createUid: window.$userInfo.id,
        };
        await api?.(submitData);
        Message.success(`${title}${baseText}成功`);
        refreshTable();
      },
    },
  };
}

function formatSubmitData(originFormData) {
  originFormData.conditionConfigInfo.quotaList = formatSubmitConditionItems({
    list: originFormData.conditionConfigInfo.quotaList,
  });
  originFormData.pkg = originFormData.pkg.join(',');
  return originFormData;
}

function formatSubmitConditionItems({ list }) {
  return list.map((item) => {
    const data = omit(item, ['rowKey']);
    return data;
  });
}
function formatApiData({ defaultData, apiData }) {
  if (!apiData) {
    return defaultData;
  }
  const data: any = hydrateDeep(defaultData, apiData);
  if (Array.isArray(apiData.conditionConfigInfo?.quotaList)) {
    data.conditionConfigInfo.quotaList =
      apiData.conditionConfigInfo.quotaList.map((item) => {
        const defaultItem = getTriggerConditionItem();
        const result = formatItemVal({ item, defaultItem });
        return result;
      });
  }
  data.pkg = data.pkg.split(',');

  return data;
}

function formatItemVal({ item, defaultItem }) {
  const result = {
    ...defaultItem,
    ...item,
  };
  if (
    result.operator === operatorEnum.between ||
    inputNumberCodes.includes(result.code)
  ) {
    result.value = Number(result.value);
    result.maxValue = result.maxValue ? Number(result.maxValue) : '';
  }

  return result;
}

export default null;
