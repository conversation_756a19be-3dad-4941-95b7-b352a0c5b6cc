/* eslint-disable no-shadow */
export interface CustomFieldSetsRes {
  data: CustomFieldSetsRes[];
}

export interface CustomFieldSetsResData {
  id: number;
  fieldKey: string;
  fieldShow: string;
  children: CustomFieldSetConfig[];
}

export interface CustomFieldSetConfig {
  id: number;
  fieldKey: string;
  fieldShow: string; // 展示文本
  parentFieldId: number;
  isFixed: number; // 0或者1，1表示固定显示
  frozen: number; // 0或者1，1表示列冻结
}

export type CustomFieldConfig = {
  mediaPlatform: string;
  tabPage: string;
  fieldKey: string;
  frozen: number;
  parentFieldId: number;
  sheet: string;
  sort: number;
};

export enum MediaPlatform {
  TOUTIAO = 'TOUTIAO',
  KUAISHOU = 'KUAISHOU',
  GDT = 'GDT',
  BAIDU = 'BAIDU',
  HUAWEI = 'HUAWEI',
  VIVO = 'VIVO',
  OPPO = 'OPPO',
  XIAOMI = 'XIAOMI',
  UC = 'UC',
  QUTOUTIAO = 'QUTOUTIAO',
  WEIBO = 'WEIBO',
  ALIPAY = 'ALIPAY',
  WIFI = 'WIFI',
  HONOR = 'HONOR',
  IQIYI = 'IQIYI',
  SIGMOB = 'SIGMOB',
}

export enum ToutiaoTabPage {
  ACCOUNT = 'ACCOUNT',
  PROJECT = 'PROJECT',
  ADVERTISEMENT = 'ADVERTISEMENT',
}

export enum KuaishouTabPage {
  ACCOUNT = 'ACCOUNT',
  AD_PLANS = 'AD_PLANS',
  AD_GROUPS = 'AD_GROUPS',
}

export enum GdtTabPage {
  MEDIA_ACCOUNT = 'ACCOUNT',
  ADVERTISING = 'ADGROUP',
  CREATIVITY = 'CREATIVE',
}
