import TemplateImport from '@/components/template-import/index.vue';
import { REPORT_BRANDS } from '@/constants/brand';
import { useAppStore } from '@/store';
import dayjs from 'dayjs';
import { getGlobalAppFormSchema } from '@/utils/global-app';
import { h } from 'vue';
import unitPrice from '../components/unitPrice.vue';
import CreateTimeScope from '../components/create-time-scope.vue';

const appStore = useAppStore();

// eslint-disable-next-line no-shadow
export const enum FIELD_MODE {
  add = 'add',
  edit = 'edit',
  batchEdit = 'batchEdit',
}

export const caliberField = (mode: FIELD_MODE) => {
  const isEdit = mode === FIELD_MODE.edit;
  const isBatchEdit = mode === FIELD_MODE.batchEdit;
  return {
    label: '质量分口径',
    name: 'qualityScoreCaliberList',
    type: 'array',
    format: 'list',
    tooltip:
      '质量分系统默认取广告位的预估单价，如需调整，请按需配置！若删除配置，系统仍会取预估单价进行计算',
    maxLength: 50,
    item: {
      type: 'object',
      label: '',
      name: '',
      fields: [
        {
          label: '',
          hideLabel: true,
          span: 10,
          name: 'caliberType',
          type: 'select',
          format: 'singleSelect',
          placeholder: '选择质量分口径',
          source: {
            data: [
              { value: 0, label: '使用单价' },
              { value: 1, label: '实时单价' },
            ],
          },
          disabled: (_field, value, path) => {
            const currentIndex = JSON.parse(
              path.replace('qualityScoreCaliberList', '')
            );

            if (isEdit) {
              return dayjs(
                value.qualityScoreCaliberList[currentIndex]?.date
              ).isBefore(dayjs().subtract(1, 'day'));
            }

            return false;
          },
          rules: () => [{ required: isBatchEdit, message: '必填' }],
        },
        {
          label: '',
          hideLabel: true,
          span: 14,
          name: 'date',
          type: 'datePicker',
          timeFormat: 'YYYY-MM-DD',
          disabledDate(current) {
            return (
              dayjs(current).isBefore(dayjs().subtract(1, 'day')) ||
              dayjs(current).isAfter(dayjs().add(1, 'year'))
            );
          },
          disabled: (value) => {
            if (isEdit) {
              return dayjs(value.value).isBefore(dayjs().subtract(1, 'day'));
            }
            return false;
          },
          rules: () => [{ required: isBatchEdit, message: '必填' }],
        },
      ],
    },
    canRemoveFn: (value, formData, index) => {
      if (isEdit) {
        if (dayjs(value[index]?.date).isBefore(dayjs().subtract(1, 'day'))) {
          return false;
        }
        return true;
      }
      return true;
    },
    rules: () => [
      {
        validator: async (value, cb) => {
          // 先过滤掉 date 为空的数据
          const validItems = value.filter(
            (item) => item.date && item.date !== ''
          );
          const existed =
            validItems.length > 1 &&
            validItems.some((item, index) => {
              return (
                validItems.findIndex((i) =>
                  dayjs(i.date).isSame(item.date, 'day')
                ) !== index
              );
            });
          if (existed) {
            cb('同一天不能存在重复的口径');
          } else {
            cb();
          }
        },
      },
    ],
  };
};
export const advertisingFormSchema = (mode: FIELD_MODE) => [
  getGlobalAppFormSchema({
    name: 'applicationId',
    multiple: false,
  }),
  {
    label: '厂商',
    name: 'brand',
    type: 'select',
    format: 'singleSelect',
    source: {
      data: REPORT_BRANDS,
    },
    select: {
      allowSearch: true,
    },
    rules: [{ required: true }],
  },
  {
    name: 'adId',
    label: '广告位ID',
    type: 'text',
    placeholder: '请输入广告位ID',
    maxLength: 100,
    showWordLimit: true,
    rules: [
      { required: true, message: '请输入广告位ID' },
      { maxLength: 100, message: '广告位ID不能超过100个字符' },
      {
        validator: (value, cb) => {
          if (!value.trim()) {
            cb('请输入广告位ID');
          }
        },
      },
    ],
  },
  {
    label: '使用ecpm',
    name: 'ecpm',
    type: 'number',
    min: 0,
    max: 100,
    precision: 4,
    placeholder: '请输入ecpm',
    rules: [{ required: true, message: '请输入ecpm' }],
  },
  {
    label: '使用点击单价',
    name: 'clickPrice',
    type: 'number',
    min: 0,
    max: 100,
    precision: 4,
    placeholder: '请输入点击单价',
    rules: [{ required: true, message: '请输入点击单价' }],
  },
  {
    type: 'object',
    label: '',
    name: 'batchConfig',
    // hideLabel: true,
    fields: [
      {
        label: '',
        hideLabel: true,
        span: 9,
        name: 'caliberType',
        type: 'select',
        format: 'singleSelect',
        placeholder: '批量质量分口径',
        source: {
          data: [
            { value: 0, label: '使用单价' },
            { value: 1, label: '实时单价' },
          ],
        },
      },
      {
        label: '',
        hideLabel: true,
        span: 15,
        name: 'date',
        type: 'dateRange',
        timeFormat: 'YYYY-MM-DD',
        disabledDate(current) {
          return (
            dayjs(current).isBefore(dayjs().subtract(1, 'day')) ||
            dayjs(current).isAfter(dayjs().add(1, 'year'))
          );
        },
        innerExtra: h(CreateTimeScope, {
          mode,
        }),
      },
    ],
  },
  caliberField(mode),
];
export const unitPriceForm = [
  {
    label: '',
    name: 'clickPrice',
    type: 'custom',
    component: unitPrice,
  },
];
export const ecpmForm = [
  {
    label: '',
    name: 'ecpm',
    type: 'custom',
    component: unitPrice,
    params: {
      columns: [
        {
          title: '广告位ID',
          dataIndex: 'adId',
          width: 200,
        },
        {
          title: '使用ecpm',
          dataIndex: 'number',
          width: 120,
        },
        {
          title: '修改后点击单价',
          dataIndex: 'updatedNumber',
          slotName: 'updatedNumber',
          width: 150,
        },
        {
          title: '修改结果',
          dataIndex: 'updatedState',
          slotName: 'updatedState',
          width: 100,
        },
      ],
      filterSchema: {
        fields: [
          {
            name: 'number',
            label: '使用ecpm',
            type: 'number',
            min: 0,
            max: 100,
            precision: 4,
            placeholder: '请输入点击单价',
            rules: [{ required: true, message: '请输入点击单价' }],
          },
        ],
      },
    },
  },
];

export const exportForm = [
  {
    label: '产品',
    name: 'applicationId',
    type: 'select',
    format: 'singleSelect',
    select: { placeholder: '请选择', allowSearch: true },
    source: {
      labelKey: 'appName',
      valueKey: 'id',
      data: () => [...appStore.appInfo.appList],
    },
    rules: [{ required: true, message: '请选择产品' }],
  },
  {
    name: 'file',
    type: 'custom',
    label: '导入媒体位',
    component: TemplateImport,
    rules: [{ required: true, message: '请上传广告位' }],
    params: {
      maxSize: 1 * 1024 * 1024,
      accept: '.xlsx',
      templateName: '广告位上传-批量.xlsx',
      tips: '请上传广告位,仅支持xlsx格式文件,最大1 MB',
      templateApi: '/apis/transmit/adreportadsense/v1/getTemplate',
    },
  },
];
export default advertisingFormSchema;
