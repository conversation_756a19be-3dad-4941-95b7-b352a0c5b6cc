<template>
  <a-form-item
    :tooltip="field.tooltip"
    :label="field.label"
    :field="`${path ? `${path}.` : ''}${field.name}`"
    :key="field.name"
    :hide-label="field.hideLabel"
    :rules="field.rules"
  >
    <a-range-picker
      :show-time="field.showTime"
      :time-picker-props="timePickerProps"
      :style="field.style"
      :mode="field.mode"
      :format="field.format"
      :value-format="field.valueFormat"
      :allow-clear="field.allowClear"
      v-model="value"
      :disabled="disabled"
      :disabled-date="
        (current) => {
          if (!field.disabledDate) return false;
          return field.disabledDate?.(current, selectedDate);
        }
      "
      :shortcuts="shortcuts"
      shortcuts-position="left"
      @select="
        (value, date, dateString) => {
          selectedDate = dateString ?? [];
        }
      "
    />
    <component :is="field.innerExtra" v-if="field.innerExtra" />
    <template #extra v-if="extra">
      <component :is="extra" />
    </template>
  </a-form-item>
</template>

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { computed, inject, ref, watch } from 'vue';
  import { isFunction, orderBy } from 'lodash';
  import { DateRangeField } from '../../../types/form';

  const props = defineProps<{
    field: DateRangeField;
    path: string;
  }>();

  const extra = props.field.extra as unknown as string;
  const selectedDate = ref<(string | undefined)[]>([]);
  const formData = inject<Record<string, any>>('formData', {}) || {};

  const timePickerProps = computed(() => {
    // 此处要判断 showTime 是否为 true
    // 否则直接给 defaultValue 会出现不是时间选择的  禁用今天之后的时间 导致今天也无法选择
    if (props.field.showTime) {
      return {
        defaultValue: ['00:00:00', '23:59:59'],
        ...(props.field.timePickerProps ?? {}),
      };
    }
    return undefined;
  });

  const value = defineModel<[string, string] | undefined>({
    default: undefined,
    set(val) {
      props.field.onChange?.(val, formData);
      return val;
    },
  });

  const disabled = computed(() => {
    return isFunction(props.field.disabled)
      ? props.field.disabled(props.field, formData, props.path)
      : props.field.disabled || undefined;
  });

  const shortcutsMap = [
    {
      id: 0,
      label: '今天',
      value: getTimeDateVal(0, 0),
    },
    {
      id: -1,
      label: '昨天',
      value: getTimeDateVal(1, 1),
    },
    {
      id: -7,
      label: '近7天',
      value: getTimeDateVal(7, 1),
    },
    {
      id: -30,
      label: '最近一个月',
      value: getTimeDateVal(30, 1),
    },
  ];
  // 月纬度
  const monthShortcutsMap = [
    {
      id: 30,
      label: '本月',
      value: () => [
        dayjs().startOf('month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
    },
    {
      id: 31,
      label: '上月',
      value: () => [
        dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
        dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
      ],
    },
  ];
  const shortcuts = computed(() => {
    if (!props.field.shortCutsList) return shortcutsMap;

    const allShortcuts = [...shortcutsMap, ...monthShortcutsMap];
    const filteredShortcuts = allShortcuts.filter((item) =>
      props.field.shortCutsList?.includes(item.id)
    );

    // 按id降序排序
    return orderBy(filteredShortcuts, ['id'], ['desc']);
  });

  function getTimeDateVal(startDay, endDay) {
    if (props.field.showTime) {
      return [
        dayjs().subtract(startDay, 'day').startOf('day').minute(0).second(0),
        dayjs().subtract(endDay, 'day').endOf('day'),
      ];
    }
    return [dayjs().subtract(startDay, 'day'), dayjs().subtract(endDay, 'day')];
  }

  watch(
    value,
    (newVal) => {
      if (!newVal) {
        selectedDate.value = [];
      }
    },
    { immediate: true }
  );
</script>
