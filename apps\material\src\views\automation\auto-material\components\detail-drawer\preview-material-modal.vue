<template>
  <div class="review-material-modal">
    <a-modal
      v-model:visible="visible"
      @close="handleClose"
      @open="handleOpen"
      :hide-cancel="true"
      title="预览素材"
      width="716px"
    >
      <div class="review-content" v-if="isOpen">
        <VideoCarousel
          v-model:curren-index="currentIndex"
          :list="list"
          ref="videoCarouselRef"
        />
        <div class="material-info">
          <a-tooltip :content="currenData?.materialName ?? '-'">
            <span class="g-ellipsis"
              >素材名称：{{ currenData?.materialName ?? '-' }}</span
            >
          </a-tooltip>
        </div>
      </div>
      <template #footer>
        <a-button
          :disabled="!showButton"
          type="outline"
          @click="handleButtonClicl"
          :loading="delLoading"
        >
          终止
        </a-button>
        <a-button
          :disabled="!canPass"
          type="outline"
          v-auth="'automation_update_status'"
          @click="handleButtonPass(2)"
          :loading="passLoading"
        >
          通过
        </a-button>
        <a-button
          :disabled="!canUnPass"
          v-auth="'automation_update_status'"
          type="outline"
          status="danger"
          @click="handleButtonPass(3)"
          :loading="passLoading"
        >
          不通过
        </a-button>
        <!-- 下面的span不要删 不然会展示弹窗默认的按钮-->
        <span></span>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import VideoCarousel from '@/components/video-carousel/index.vue';
  import { Message } from '@arco-design/web-vue';
  import { autoMaterial } from '@/api/automation/auto-material';
  import { getPreviewMaterialDefaultOptions } from '@/views/sc-home/hook/preview-material';

  const visible = defineModel<boolean>();
  const options = defineModel<any>('options');
  const props = defineProps<{
    materialNum?: number;
    hideEdit?: boolean;
  }>();
  const showButton = computed(() => {
    return currenData.value?.status === 1;
  });
  const list = computed(() => {
    return (
      options.value?.list.map((item: any) => {
        return {
          ...item,
          materialType: item?.material_type || 2,
        };
      }) || []
    );
  });
  const currenData = computed(() => {
    return list.value[currentIndex.value - 1] ?? {};
  });
  const currentIndex = ref(1);
  const canPass = computed(() => {
    return (
      (currenData.value?.artificialStatus === 1 ||
        currenData.value?.artificialStatus === 3) &&
      currenData.value?.status === 3
    );
  });
  const canUnPass = computed(() => {
    return (
      currenData.value?.artificialStatus === 1 && currenData.value?.status === 3
    );
  });
  function setIndex(index) {
    currentIndex.value = index + 1;
  }

  const isOpen = ref(false);

  const isRefresh = ref(false);

  const videoCarouselRef = ref();

  function handleOpen() {
    setIndex(options.value?.index);
    isOpen.value = true;
  }

  const emits = defineEmits(['refresh', 'deleteSuccess', 'changeSelect']);

  function handleClose() {
    if (isRefresh.value) {
      emits('refresh');
      isRefresh.value = false;
    }
    isOpen.value = false;
    currentIndex.value = 1;
    options.value = getPreviewMaterialDefaultOptions();
  }
  const passLoading = ref(false);
  const delLoading = ref(false);
  function handleButtonPass(status) {
    passLoading.value = true;
    autoMaterial
      .status({
        ids: [currenData.value?.id],
        artStatus: status,
        tacticsId: options.value?.record.tactics_id,
      })
      .then((res) => {
        if (res.code !== 1) {
          Message.error('操作失败');
          return;
        }
        isRefresh.value = true;

        Message.success('操作成功');
        emits('changeSelect', currenData.value?.id);
        if (currentIndex.value === options.value?.list.length) {
          visible.value = false;
          emits('deleteSuccess');
        } else {
          list.value[currentIndex.value - 1].artificialStatus = status;
          currentIndex.value += 1;
        }
      })
      .finally(() => {
        passLoading.value = false;
      });
  }
  function handleButtonClicl() {
    delLoading.value = true;
    autoMaterial
      .stop({
        id: currenData.value?.id,
      })
      .then((res) => {
        if (res.code !== 1) {
          Message.error('终止失败');
          return;
        }
        isRefresh.value = true;
        options.value?.list.splice(currentIndex.value - 1, 1);
        if (options.value?.list && options.value?.list.length > 0) {
          if (!options.value?.list[currentIndex.value - 1]) {
            currentIndex.value = 1;
          }
          Message.success('终止成功，请继续查看下一个素材');
        } else {
          Message.success('终止成功成功');
          visible.value = false;
        }
        emits('deleteSuccess');
      })
      .finally(() => {
        delLoading.value = false;
      });
  }
</script>

<style scoped lang="less">
  .material-info {
    display: flex;
    margin-top: 20px;

    span {
      width: 33.33%;
      font-size: 14px;
      color: rgb(0 0 0 / 84%);
      line-height: 22px;
    }

    .g-ellipsis {
      padding-right: 10px;
      width: 90%;
    }
  }

  .material-img {
    width: 676px;
    height: 411px;
  }
</style>
