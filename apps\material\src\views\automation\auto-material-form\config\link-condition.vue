<template>
  <div class="condition-wrapper">
    <a-row :gutter="8">
      <a-col :span="10">
        <a-form-item
          label="账户筛选"
          field="media"
          :rules="[
            {
              required: true,
              message: '请选择账户筛选',
            },
          ]"
        >
          <a-select
            v-model="form.media"
            placeholder="媒体筛选"
            @change="mediaChange"
          >
            <a-option
              v-for="item in MEDIA_OPTIONS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></a-option>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item
          label=""
          field="app"
          hide-label
          :rules="[
            {
              required: true,
              message: '请选择产品筛选',
            },
          ]"
        >
          <a-cascader
            v-model="form.app"
            :options="productOptions"
            placeholder="产品筛选"
            @change="productChange"
            multiple
            allow-search
            allow-clear
            :max-tag-count="5"
          >
          </a-cascader>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row :gutter="8">
      <a-col :span="10">
        <a-form-item
          label="筛选维度"
          field="accountType"
          :rules="[
            {
              required: true,
              message: '请选择筛选维度',
            },
          ]"
          :content-flex="false"
        >
          <a-radio-group
            type="button"
            v-model="form.accountType"
            @change="handleAccountTypeChange"
          >
            <a-radio value="appId">账户筛选</a-radio>
            <a-radio value="channelCodes">渠道code</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
      <a-col :span="12" v-if="form.accountType === 'appId'">
        <a-form-item
          label="账户筛选"
          field="accountIds"
          hide-label
          :rules="[
            {
              required: true,
              message: '请选择账户筛选',
            },
          ]"
          :content-flex="false"
        >
          <a-select
            v-model="form.accountIds"
            placeholder="账户筛选"
            v-model:input-value="filterValue"
            multiple
            allow-search
            allow-clear
            :max-tag-count="5"
          >
            <a-option
              v-for="item in accountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></a-option>
            <template #header>
              <div style="padding: 6px 12px">
                <a-checkbox
                  :model-value="checkedAll"
                  :indeterminate="indeterminate"
                  @change="handleChangeAll"
                  >全选</a-checkbox
                >
              </div>
            </template>
          </a-select>
        </a-form-item>
      </a-col>
      <a-col :span="12" v-if="form.accountType === 'channelCodes'">
        <a-form-item
          label="渠道code"
          field="channelCodes"
          hide-label
          :rules="[
            {
              required: true,
              message: '渠道code',
            },
          ]"
          :content-flex="false"
        >
          <a-select
            v-model="form.channelCodes"
            placeholder="渠道code"
            v-model:input-value="channelFilterValue"
            multiple
            allow-search
            allow-clear
            :max-tag-count="5"
          >
            <a-option
              v-for="item in showChannelOptions"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            ></a-option>
            <template #header>
              <div style="padding: 6px 12px">
                <a-checkbox
                  :model-value="channelCheckedAll"
                  :indeterminate="channelIndeterminate"
                  @change="handleChannelChangeAll"
                  >全选</a-checkbox
                >
              </div>
            </template>
          </a-select>
        </a-form-item>
      </a-col>
    </a-row>
    <a-form-item
      label="计划筛选"
      field="campaignFilter"
      :rules="[
        {
          required: true,
          message: '请填写计划筛选',
        },
      ]"
      :content-flex="false"
    >
      <ConditionList
        v-model="form.campaignFilter"
        :first-options="planFirstOptions"
        :second-options="secondOptions"
        :third-options="thirdOptions"
        path="campaignFilter"
      ></ConditionList>
    </a-form-item>
    <a-form-item
      label="素材指标筛选"
      field="materialFilter"
      :rules="[
        {
          required: true,
          message: '请填写素材指标筛选',
        },
      ]"
      :content-flex="false"
    >
      <ConditionList
        v-model="form.materialFilter"
        :first-options="firstOptions"
        :second-options="secondOptions"
        :third-options="thirdOptions"
        path="materialFilter"
        can-add
        can-delete
      ></ConditionList>
    </a-form-item>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref, watchEffect, computed } from 'vue';

  import ConditionList from './components/condition-list.vue';
  import { useAccountOptions } from './use-account-options';

  import {
    MEDIA_OPTIONS,
    FILTER_OPTIONS,
    PLAN_FILTER_OPTIONS,
    VALUE_MAP,
    COMPARE_MAP,
  } from './constant';

  const checkedAll = ref<boolean>(false);
  const indeterminate = ref<boolean>(false);
  const channelCheckedAll = ref<boolean>(false);
  const channelIndeterminate = ref<boolean>(false);

  const form = defineModel<any>();
  const filterValue = ref<string>('');
  const channelFilterValue = ref<string>('');
  const showOptions = computed(() => {
    if (!filterValue.value) return accountOptions.value;
    return accountOptions.value.filter((option) => {
      return option.label
        .toLowerCase()
        .includes(filterValue.value.toLowerCase());
    });
  });
  const showChannelOptions = computed(() => {
    if (!channelFilterValue.value) return channelOptions.value;
    return channelOptions.value.filter((option) => {
      return option.label
        .toLowerCase()
        .includes(channelFilterValue.value.toLowerCase());
    });
  });
  const {
    accountOptions,
    productOptions,
    channelOptions,
    getProductOptions,
    getAccountOptions,
    getChannelOptions,
  } = useAccountOptions();
  const handleChangeAll = (checked: boolean) => {
    if (checked) {
      form.value.accountIds = showOptions.value.map((item) => item.value);
    } else {
      form.value.accountIds = [];
    }
  };
  const handleChannelChangeAll = (checked: boolean) => {
    if (checked) {
      form.value.channelCodes = showChannelOptions.value.map((item) => item.id);
    } else {
      form.value.channelCodes = [];
    }
  };
  const handleAccountTypeChange = (value: any) => {
    form.value.accountIds = [];
    form.value.channelCodes = [];
  };
  watchEffect(() => {
    checkedAll.value =
      form.value.accountIds.length === showOptions.value.length &&
      form.value.accountIds.length > 0;
    indeterminate.value =
      form.value.accountIds.length > 0 &&
      form.value.accountIds.length < showOptions.value.length;
  });
  watchEffect(() => {
    channelCheckedAll.value =
      form.value.channelCodes.length === showChannelOptions.value.length &&
      form.value.channelCodes.length > 0;
    channelIndeterminate.value =
      form.value.channelCodes.length > 0 &&
      form.value.channelCodes.length < showChannelOptions.value.length;
  });

  const firstOptions = {
    field: 'field',
    placeholder: '指标维度下拉选择',
    options: FILTER_OPTIONS,
    required: true,
    message: '请选择维度',
  };
  const planFirstOptions = {
    field: 'field',
    placeholder: '指标维度下拉选择',
    options: PLAN_FILTER_OPTIONS,
    required: true,
    message: '请选择维度',
    disabled: true,
  };
  const secondOptions = {
    field: 'condition',
    placeholder: '选择关系',
    options: COMPARE_MAP,
    required: true,
    message: '请选择关系',
  };
  const thirdOptions = {
    field: 'value',
    placeholder: '选择筛选值',
    options: VALUE_MAP,
    required: true,
    message: '筛选值必填',
  };

  const mediaChange = async (value: any) => {
    form.value.accountIds = [];
    form.value.channelCodes = [];
    await getAccountOptions(form.value.app, value);
    await getChannelOptions(form.value.app, form.value.media);
  };
  const productChange = async (value: any) => {
    form.value.accountIds = [];
    form.value.channelCodes = [];
    await getAccountOptions(value, form.value.media);
    await getChannelOptions(form.value.app, form.value.media);
  };
  onMounted(() => {
    getProductOptions();
  });
</script>

<style lang="less" scoped>
  .condition-wrapper {
    margin-top: 10px;
    width: 100%;
    height: auto;
  }
</style>
