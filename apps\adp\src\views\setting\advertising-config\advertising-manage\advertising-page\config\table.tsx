import type { Column } from '@repo/kola/src/components/dynamic/types/table';
import DynamicButton from '@repo/kola/src/components/dynamic/button/index.vue';
import { Message } from '@arco-design/web-vue';
import { advertisingManageApi } from '@/api/setting/advertising-config/advertising-manage';
import { ADVERTISING_MANAGE } from '@/constants/auth-key/setting/advertisingConfig';
import { REPORT_BRANDS } from '@/constants/brand';
import { isEmpty, isNil } from 'lodash';
import AdvertisingRecord from '../components/advertising-record/index.vue';
import { advertisingFormSchema, FIELD_MODE } from './formSchema';
import { getDefaultValueDetail } from './getDefaultValue';
import { modalEditPrice, modalEditEcpm } from './editOperation';

const editOperation = {
  text: '编辑',
  auth: ADVERTISING_MANAGE.ADVERTISING_MANAGE_EDIT,
  props: {
    type: 'text',
    disabled: false,
    loading: false,
  },
  clickActionType: 'modal',
  linkUrl: '',
  modal: {
    props: { title: '编辑广告位', width: '700px' },
    contentType: 'form',
    form: {
      formSchema: { fields: [...advertisingFormSchema(FIELD_MODE.edit)] },
    },
    getDefaultValue: getDefaultValueDetail,
    action: async (value: any): Promise<boolean | void> => {
      const { refreshTable, formData } = value;

      let { qualityScoreCaliberList } = formData;
      qualityScoreCaliberList = qualityScoreCaliberList?.filter((item) => {
        return (
          !isNil(item.caliberType) &&
          item.caliberType !== '' &&
          item.date &&
          !isEmpty(item.date)
        );
      });

      delete formData.batchConfig;
      await advertisingManageApi.modify?.({
        ...formData,
        qualityScoreCaliberList,
      });
      Message.success('编辑成功');
      return refreshTable();
    },
  },
};

const deleteOperation = {
  auth: ADVERTISING_MANAGE.ADVERTISING_MANAGE_DELETE,
  text: '删除',
  props: {
    type: 'text',
    disabled: false,
    loading: false,
  },
  clickActionType: 'modal',
  linkUrl: '',
  modal: {
    props: { title: '删除', width: 500, fullscreen: false },
    contentType: 'text',
    text: '确定删除吗? 删除后不可复原，请谨慎操作',
    action: async ({ record, refreshTable }: any) => {
      const { id } = record;
      await advertisingManageApi.remove?.({ id });
      Message.success('删除成功');
      refreshTable();
    },
  },
};

const recordOperation = {
  text: '变更记录',
  props: {
    type: 'text',
    disabled: false,
    loading: false,
  },
  clickActionType: 'drawer',
  modal: {
    props: {
      title: '变更记录',
      width: '1000px',
      fullscreen: false,
      footer: false,
    },
    contentType: 'custom',
    custom: AdvertisingRecord,
  },
};

const operationsColumn: Column = {
  title: '操作',
  dataIndex: 'operations',
  width: 250,
  fixed: 'right',
  customRender: {
    type: 'operations',
    props: {
      operations: [editOperation, recordOperation, deleteOperation],
    },
  },
};

const CALIBER_MAP = {
  0: { text: '使用单价', color: 'red' },
  1: { text: '实时单价', color: 'green' },
  2: { text: '预估单价', color: 'arcoblue' },
};
// @ts-ignore
const columns: Column[] = [
  {
    title: '厂商',
    dataIndex: 'brand',
    width: 100,
    fixed: 'left',
    customRender: {
      type: 'code-to-name',
      props: {
        map: REPORT_BRANDS,
      },
    },
  },
  {
    title: '质量分口径',
    dataIndex: 'caliber',
    width: 100,
    fixed: 'left',
    render: ({ record }) => (
      <a-tag color={CALIBER_MAP[record.caliberType]?.color ?? 'red'} bordered>
        {CALIBER_MAP[record.caliberType]?.text}
      </a-tag>
    ),
  },
  {
    title: '广告位ID',
    dataIndex: 'adId',
    width: 150,
    fixed: 'left',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '广告位名称',
    dataIndex: 'adName',
    fixed: 'left',
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '产品',
    dataIndex: 'appName',
    fixed: 'left',
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '预估点击单价',
    dataIndex: 'estimateClickPrice',
    width: 150,
    ellipsis: true,
    tooltip: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    },
  },
  {
    title: '预估ecpm',
    dataIndex: 'estimateEcpm',
    width: 150,
    ellipsis: true,
    tooltip: true,
    sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    },
  },
  {
    title: '使用点击单价',
    dataIndex: 'clickPrice',
    width: 150,
    sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    },
    customRender: {
      type: ({ record }) => (
        <div>
          <span>{`${record.clickPrice}`}</span>
          <DynamicButton
            config={modalEditPrice}
            record={record}
          ></DynamicButton>
        </div>
      ),
    },
  },
  {
    title: '使用ecpm',
    dataIndex: 'ecpm',
    width: 150,
    sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    },
    customRender: {
      type: ({ record }) => (
        <div>
          <span>{`${record.ecpm}`}</span>
          <DynamicButton config={modalEditEcpm} record={record}></DynamicButton>
        </div>
      ),
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateAt',
    width: 170,
    sortable: {
      sortDirections: ['ascend', 'descend'],
      sorter: true,
    },
  },
  { title: '更新人', dataIndex: 'updateUserName', width: 100 },
  operationsColumn,
];

const SORT_MAP = {
  ascend: 'asc',
  descend: 'desc',
};

async function loadData(filter, pagination, _customParams, sorter) {
  const { brands, applicationIds } = filter;
  const dataList = await advertisingManageApi.list?.({
    ...pagination,
    ...filter,
    brands: brands || [],
    applicationIds: applicationIds || [],
    order: {
      key: sorter?.sortBy,
      sort: SORT_MAP[sorter?.sortDirection],
    },
  });
  return dataList;
}

const tableJson = {
  load: { action: loadData },
  columns,
  props: {
    columnResizable: true,
  },
};

export default tableJson;
