import switchConfig from '@/views/tactics/common/switch-config';

export const getReturnTacticSchema = () => {
  return [
    {
      type: 'object',
      name: 'returnConfig',
      hideLabel: true,
      fields: [
        {
          type: 'viewCard',
          label: '返回策略',
          contentStyle: {
            width: '640px',
          },
          fields: [
            {
              label: '排序逻辑',
              name: 'returnTacticsRule',
              type: 'select',
              format: 'singleSelect',
              span: 8,
              rules: [{ required: true }],
              source: {
                data: [
                  {
                    label: '按入池时间后进先出',
                    value: 'time',
                  },
                  {
                    label: '按广告价格高价先出',
                    value: 'price',
                  },
                ],
              },
              tooltip:
                '指从数据池拿取广告的逻辑，支持按广告价格高价先出或按入池时间后进先出；按照入池时间后进先出只有小米厂商联盟广告生效',
            },
            {
              label: '是否同时查询数据池和页面池',
              name: 'isQueryBothPool',
              tooltip: '小米厂商',
              ...switchConfig,
              span: 8,
              rules: [{ required: true }],
            },
            {
              label: '是否下载类优先',
              name: 'isDownloadPriority',
              tooltip:
                '关闭时按照原有逻辑,开启时优先下载类,优先于促激活、非下载类广告',
              ...switchConfig,
              span: 4,
              rules: [{ required: true }],
            },
            {
              label: '唤醒广告ecpm',
              name: 'wakeUpEcpm',
              type: 'number',
              span: 4,
              min: 0,
              suffix: '元',
              required: true,
              tooltip: '小于等于配置价格的唤醒广告不拿取',
              visibleOn: (data) => data.isDownloadPriority === 1,
            },
            {
              label: '无广告时是否拿取低价唤醒广告',
              name: 'isGetLowAd',
              ...switchConfig,
              span: 4,
              required: true,
              visibleOn: (data) => data.isDownloadPriority === 1,
            },
          ],
        },
      ],
    },
  ];
};

export const getReturnTacticData = () => {
  return {
    returnConfig: {
      returnTacticsRule: 'time',
      isQueryBothPool: 2,
      isDownloadPriority: 1,
      wakeUpEcpm: 0,
      isGetLowAd: 1,
    },
  };
};

export default null;
