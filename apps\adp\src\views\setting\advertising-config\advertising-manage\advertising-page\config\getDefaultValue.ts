import { advertisingManageApi } from '@/api/setting/advertising-config/advertising-manage';
import { isEmpty } from 'lodash';

export const getDefaultValue = () => {
  return {
    batchConfig: {
      date: [],
      caliberType: '',
    },
    qualityScoreCaliberList: [
      {
        date: '',
        caliberType: '',
      },
    ],
  };
};
export const getDefaultValueDetail = async (record: any) => {
  const { id } = record;
  const { data = {} } = await advertisingManageApi.detail({ id });
  const {
    clickPrice,
    ecpm,
    appId,
    id: resId,
    brand,
    adId,
    qualityScoreCaliberList,
  } = data;

  const detail = {
    clickPrice: parseFloat(clickPrice),
    ecpm: parseFloat(ecpm),
    applicationId: appId,
    brand,
    adId,
    id: resId,
    batchConfig: {
      date: [],
      caliberType: '',
    },
    qualityScoreCaliberList: isEmpty(qualityScoreCaliberList)
      ? [
          {
            date: '',
            caliberType: '',
          },
        ]
      : qualityScoreCaliberList,
  };
  return detail;
};
