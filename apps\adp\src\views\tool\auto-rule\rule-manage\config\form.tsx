import DingConfig from '@/views/tool/components/ding-config.vue';
import { ref } from 'vue';
import { getAccountList, getCampaignList } from '@/api/tool/auto-rule';
import { getChannelList } from '@/api/postback/core';
import { forEach, get, isNil } from 'lodash';
import { useAppStore } from '@/store';
import { clearFormValidate } from '@/views/tool/auto-rule/rule-manage/utils';
import {
  getGlobalAppFormSchema,
  getOneCompanyAppValues,
} from '@/utils/global-app';
import { mediaNumMap } from '@/views/tool/common';
import { getIsRechargeAction } from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/utils';
import { getActions } from '@/views/tool/auto-rule/rule-manage/components/trigger-rule/utils/field-columns';
import postBackCommonApi from '@/api/postback/common';
import TriggerRule from '../components/trigger-rule/index.vue';
import {
  RULE_TYPE_MAP,
  RULE_TARGET_ENUM,
  CHECK_TIME_MAP,
  getTimeMap,
  getDefaultAutoRuleCondition,
  getDefaultAutoRuleTargetItem,
  reportOriginOptions,
  ruleTargetEnum,
  ruleTypeEnum,
  actionMaps,
  reportOriginMap,
  getConditionListItem,
} from '../constants';
import RuleMediaConfig from '../components/rule-media-config.vue';
import useActiveKey from '../hooks/use-active-key';

const { activeKey, setActiveKey } = useActiveKey();

const eventOptions = ref<any[]>();
const initOption = async ({ query }) => {
  eventOptions.value = [];

  const { data } = await getAccountList({
    userId: window.$userInfo.id,
    ...query,
  });
  const showBackData = data.map((accountItem: any) => {
    return {
      value: accountItem.advertiserId,
      label: accountItem.advertiserName,
      appId: accountItem.applicationId,
      level: 1,
    };
  });
  eventOptions.value = showBackData;
  return eventOptions;
};

const loadMoreOption = async (row) => {
  const { level, value, appId } = row;
  let optionList = [];
  const { data } = await getCampaignList({
    advertiserId: value,
    applicationId: appId,
    mediaPlatform: activeKey.value,
  });
  if (data.length) {
    optionList = data?.map((item: any) => {
      return {
        label: item.promotionName,
        value: item.promotionId,
        parent: value,
        level: 2,
      };
    });
  }
  return Promise.resolve(optionList);
};

const timeMap = getTimeMap();
function clearAutoRuleTargetItem(formRef) {
  clearFormValidate({ formRef, field: 'autoRuleTargetItem' });
}

function isNullAndString(val) {
  return isNil(val) || val === '';
}

function resetMediaALLOrItem({ form, isDefaultKey = false }) {
  form.autoRuleCondition = getDefaultAutoRuleCondition();
  form.autoRuleTargetItem = getDefaultAutoRuleTargetItem().map((item) => {
    const result = {
      ...item,
    };
    if (form.ruleTarget === ruleTargetEnum.ad) {
      result.isAllTarget = 2;
    }
    return result;
  });
  if (isDefaultKey) {
    setActiveKey(mediaNumMap.toutiao);
  }
}
export const getFormSchema = (type?: string) => {
  const isDetail = type === 'detail';
  const actions = getActions({
    canFilter: false,
  });
  const notifyCodes = actions
    .filter((item) => item.notify)
    .map((item) => item.value);
  return {
    fields: [
      {
        label: '应用范围',
        type: 'viewCard',
        fields: [
          getGlobalAppFormSchema({
            name: 'applicationIds',
            onChange(value, formData) {
              formData.value.applicationIds = getOneCompanyAppValues({ value });
              forEach(formData.value.autoRuleTargetItem, (item) => {
                if (Array.isArray(value) && value.length > 0) {
                  item.mediaTarget = item.mediaTarget.filter((targetItem) => {
                    return value.includes(targetItem.appId);
                  });
                } else {
                  item.mediaTarget = [];
                }
              });
            },
          }),
          {
            label: '媒体规则',
            name: 'ruleType',
            type: 'select',
            format: 'buttonRadio',
            source: {
              data: RULE_TYPE_MAP,
            },
            required: true,
            onChange(value, formData) {
              resetMediaALLOrItem({ form: formData.value });
            },
          },
          {
            label: '应用对象',
            name: 'ruleTarget',
            type: 'select',
            format: 'buttonRadio',
            tooltip: (field, formData) => {
              if (formData.ruleTarget === ruleTargetEnum.account) {
                return '选择后将会对所选应用对象进行操作';
              }
              return '广告维度对应各媒体层级；今日头条-广告  快手广告组  广点通-广告  百度-计划';
            },
            source: {
              data: RULE_TARGET_ENUM,
            },
            onChange(value, formData) {
              resetMediaALLOrItem({ form: formData.value, isDefaultKey: true });
              setActiveKey(mediaNumMap.toutiao);
            },
            getRef({ formRef }) {
              clearAutoRuleTargetItem(formRef);
            },
            required: true,
          },
          {
            label: '应用报表',
            name: 'dataSource',
            type: 'select',
            format: 'buttonRadio',
            required: true,
            source: {
              data: reportOriginOptions,
            },
            onChange(value, formData) {
              resetMediaALLOrItem({ form: formData.value, isDefaultKey: true });
            },
            getRef({ formRef }) {
              clearAutoRuleTargetItem(formRef);
            },
          },
          {
            label: '',
            name: 'autoRuleTargetItem',
            type: 'array',
            viewType: 'card',
            format: <RuleMediaConfig clearValidate={clearAutoRuleTargetItem} />,
            hideLabel: true,
            rules: (field, formData) => {
              return [
                {
                  validator(value, cb) {
                    if (!value.some((item) => item.isCheck)) {
                      cb('至少选择一个媒体');
                      return;
                    }

                    const checkMedias = value.filter((media) => media.isCheck);
                    const isAccount =
                      formData.ruleTarget === ruleTargetEnum.account;
                    const isAd = formData.ruleTarget === ruleTargetEnum.ad;
                    /* 每个媒体一套规则 */
                    const isEachMedia =
                      formData.ruleType === ruleTypeEnum.singleMedia;
                    if (Array.isArray(checkMedias)) {
                      // eslint-disable-next-line no-plusplus
                      for (let i = 0; i < checkMedias.length; i++) {
                        const mediaItem = checkMedias[i];
                        if (isAccount) {
                          if (mediaItem.isAllTarget === 0) {
                            if (mediaItem.mediaTarget.length <= 0) {
                              cb('请选择账户信息');
                              return;
                            }
                          }
                        } else if (isAd) {
                          if (mediaItem.isAllTarget === 1) {
                            if (mediaItem.mediaTarget.length <= 0) {
                              cb('请选择账户信息');
                              return;
                            }
                          } else if (mediaItem.isAllTarget === 0) {
                            const isAdFlag =
                              mediaItem.mediaTarget <= 0 ||
                              mediaItem.mediaTarget.some(
                                (child) => child.children <= 0
                              );
                            if (isAdFlag) {
                              cb('请选择广告列表');
                              return;
                            }
                          }
                        }
                        if (isEachMedia) {
                          const { autoRuleCondition } = mediaItem;
                          for (
                            let j = 0;
                            j < autoRuleCondition.conditionItems.length;
                            // eslint-disable-next-line no-plusplus
                            j++
                          ) {
                            const conditionItem =
                              autoRuleCondition.conditionItems[j];

                            if (
                              isNullAndString(conditionItem.columnCode) ||
                              isNullAndString(conditionItem.operator)
                            ) {
                              cb('请完善触发方式');
                              return;
                            }
                            if (
                              isNullAndString(conditionItem.comparisonValue)
                            ) {
                              cb('请完善触发方式');
                              return;
                            }
                          }
                          if (isNullAndString(autoRuleCondition.action)) {
                            cb('请完善执行操作');
                            return;
                          }
                          if (
                            [
                              actionMaps.updateBudget,
                              actionMaps.updateAndNotify,
                            ].includes(autoRuleCondition.action)
                          ) {
                            if (
                              isNullAndString(autoRuleCondition.actionValue)
                            ) {
                              cb('请完善预算配置');
                              return;
                            }
                          }
                          if (getIsRechargeAction(autoRuleCondition.action)) {
                            if (
                              isNullAndString(autoRuleCondition.actionValue) ||
                              isNullAndString(autoRuleCondition.actionValue2) ||
                              isNullAndString(autoRuleCondition.actionValue3)
                            ) {
                              cb('请完善账户充值配置');
                              return;
                            }
                          }
                        }
                      }
                    }

                    cb();
                  },
                },
              ];
            },
            item: {
              label: '',
              name: '',
              hideLabel: true,
              type: 'object',
              fields: [
                {
                  label: '选择渠道',
                  name: 'channelIds',
                  type: 'select',
                  format: 'multipleSelect',
                  select: {
                    allowClear: true,
                    allowSearch: true,
                    showAll: true,
                    maxTagCount: 2,
                    openVirtual: true,
                  },
                  source: {
                    data: async (field, formData, path) => {
                      const { applicationIds } = formData;
                      if (applicationIds.length <= 0) {
                        return [];
                      }
                      const mediaPlatform = get(
                        formData,
                        path,
                        {}
                      )?.mediaPlatform;
                      const res = await getChannelList({
                        appIdList: applicationIds.join(','),
                        mediaPlatform,
                      });
                      return res.data.map((item) => {
                        return {
                          label: `${item.channelName}_${item.channelCode}`,
                          value: item.id,
                        };
                      });
                    },
                  },
                  onChange: (value, formData, oldVal, path) => {
                    const curItem = get(formData.value, `${path}`);
                    curItem.mediaTarget = [];
                  },
                },
                {
                  label: '厂商',
                  name: 'brandCodes',
                  type: 'select',
                  format: 'multipleSelect',
                  select: {
                    allowSearch: true,
                    allowClear: true,
                    showAll: true,
                  },
                  source: {
                    valueKey: 'mediaCode',
                    labelKey: 'mediaName',
                    data: async () => {
                      return postBackCommonApi
                        .mediaList({ isSupportUserValue: 1 })
                        .then((res) => {
                          return res?.data ?? [];
                        });
                    },
                  },
                  visibleOn: (_, form) => {
                    return form.dataSource === reportOriginMap.quality;
                  },
                },
                {
                  label: '选择账户',
                  name: 'isAllTarget',
                  type: 'select',
                  format: 'buttonRadio',
                  required: true,
                  source: {
                    data: (field, formData) => {
                      if (formData.ruleTarget === ruleTargetEnum.account) {
                        return [
                          { label: '全部账户', value: 1 },
                          { label: '指定账户', value: 0 },
                        ];
                      }
                      return [
                        { label: '全部广告', value: 2 },
                        { label: '指定账户', value: 1 },
                        { label: '指定广告', value: 0 },
                      ];
                    },
                  },
                  onChange: (value, formData, oldVal, path) => {
                    const curItem = get(formData.value, `${path}`);
                    curItem.mediaTarget = [];
                    const { ruleTarget, ruleType, dataSource } = formData.value;
                    if (
                      // 当是代投报表 应用对象是广告，选择账户 是指定广告
                      dataSource === reportOriginMap.fiction &&
                      ruleTarget === ruleTargetEnum.ad &&
                      value === 0
                    ) {
                      if (ruleType === ruleTypeEnum.allMedia) {
                        formData.value.autoRuleCondition.conditionItems = [
                          getConditionListItem(),
                        ];
                      } else if (ruleType === ruleTypeEnum.singleMedia) {
                        curItem.autoRuleCondition.conditionItems = [
                          getConditionListItem(),
                        ];
                      }
                    }
                  },
                  getRef({ formRef }) {
                    clearAutoRuleTargetItem(formRef);
                  },
                },
                {
                  name: 'mediaTarget',
                  label: '账户',
                  type: 'select',
                  format: 'multipleSelect',
                  placeholder: '请选择账户',
                  required: true,
                  select: {
                    valueKey: 'value',
                    allowClear: true,
                    allowSearch: true,
                    isAsyncSearch: true,
                    filterOption: true,
                    showAll: true,
                    tooltip: true,
                    openVirtual: true,
                    maxTagCount: 3,
                  },
                  source: {
                    data: async (_value, formData, path) => {
                      if (formData.applicationIds?.length <= 0) {
                        return [];
                      }
                      return getAccountList({
                        userId: window.$userInfo.id,
                        mediaPlatform: get(formData, `${path}.mediaPlatform`),
                        applicationIds: formData.applicationIds ?? [],
                        channelIds: get(formData, `${path}.channelIds`) ?? [],
                        abortHttp: true,
                      }).then((res) => {
                        return (res?.data ?? []).map((item) => {
                          return {
                            label: `${item.advertiserName}(${item.advertiserId})`,
                            value: {
                              value: item.advertiserId,
                              label: item.advertiserName,
                              appId: item.applicationId,
                              level: 1,
                            },
                          };
                        });
                      });
                    },
                  },
                  visibleOn: (data, form) => {
                    const { ruleTarget } = form;
                    const { isAllTarget } = data;
                    if (
                      ruleTarget === ruleTargetEnum.account &&
                      isAllTarget === 0
                    ) {
                      return true;
                    }
                    if (ruleTarget === ruleTargetEnum.ad && isAllTarget === 1) {
                      return true;
                    }
                    return false;
                  },
                },

                {
                  name: 'mediaTarget',
                  label: '',
                  type: 'cascaderCard',
                  cardItemStyle: {
                    width: '300px',
                  },
                  isShowValue: true,
                  isShowNumber: true,
                  option: (field, formData, path) => {
                    if (formData.applicationIds?.length <= 0) {
                      return [];
                    }
                    initOption({
                      query: {
                        mediaPlatform: get(formData, `${path}.mediaPlatform`),
                        applicationIds: formData.applicationIds ?? [],
                        channelIds: get(formData, `${path}.channelIds`) ?? [],
                      },
                    });
                    return eventOptions;
                  },
                  loadMore: loadMoreOption,
                  valueStruct: 'tree',
                  getMapField: (info) => {
                    return {
                      appId: info.raw.appId,
                    };
                  },
                  placeholder: '请输入账户或广告名称搜索',
                  isReverse: false,
                  items: (formData, path) => {
                    if (
                      formData.ruleTarget === ruleTargetEnum.ad &&
                      get(formData, `${path}.isAllTarget`) === 0
                    ) {
                      return [
                        {
                          title: '账户列表',
                          multiple: true,
                          isCheckAll: false,
                        },
                        {
                          title: '广告列表',
                          multiple: true,
                          isCheckAll: true,
                        },
                      ];
                    }
                    return [
                      {
                        title: '账户列表',
                        multiple: true,
                        isCheckAll: true,
                      },
                    ];
                  },
                  visibleOn: (data, formData) => {
                    const { ruleTarget } = formData;
                    const { isAllTarget } = data;
                    if (ruleTarget === ruleTargetEnum.ad) {
                      if (isAllTarget === 0) {
                        return true;
                      }
                    }
                    return false;
                  },
                },
                {
                  label: '触发方式',
                  name: 'autoRuleCondition',
                  type: 'custom',
                  component: <TriggerRule />,
                  visibleOn: (data, formData) => {
                    return formData.ruleType === ruleTypeEnum.singleMedia;
                  },
                },
              ],
            },
          },
        ],
      },
      {
        label: '触发方式',
        type: 'viewCard',
        fields: [
          {
            label: '触发方式',
            name: 'autoRuleCondition',
            type: 'custom',
            component: <TriggerRule />,
          },
        ],

        visibleOn: (data) => {
          return data.ruleType === ruleTypeEnum.allMedia;
        },
      },
      {
        label: '规则设置',
        type: 'viewCard',
        fields: [
          {
            label: '检查时间',
            name: 'actionTime',
            type: 'select',
            format: 'buttonRadio',
            hideAsterisk: true,
            required: true,
            source: {
              data: (field, formData, path) => {
                const { ruleTarget, ruleType } = formData;
                const isAccount = ruleTarget === ruleTargetEnum.account;
                if (
                  isAccount &&
                  ((ruleType === ruleTypeEnum.allMedia &&
                    getIsRechargeAction(formData.autoRuleCondition.action)) ||
                    (ruleType === ruleTypeEnum.singleMedia &&
                      formData.autoRuleTargetItem.some((item) =>
                        getIsRechargeAction(item.autoRuleCondition.action)
                      )))
                ) {
                  // 执行操作等于充值 加5分钟选项
                  return [
                    ...CHECK_TIME_MAP,
                    {
                      label: '每5分钟',
                      value: 5,
                    },
                  ];
                }
                if (formData.actionTime === 5) {
                  setTimeout(() => {
                    formData.actionTime = 2;
                  });
                }
                return CHECK_TIME_MAP;
              },
            },
            onChange(value, formData) {
              if (value === 2) {
                formData.value.autoRuleCondition.reachNum = 1;
              }
            },
          },
          {
            label: '选择时间',
            name: 'actionStartTime',
            type: 'select',
            format: 'singleSelect',
            style: {
              width: '300px',
            },
            source: {
              data: timeMap,
            },
            required: true,
            hideAstrisk: true,
            visibleOn(data) {
              return data.actionTime === 2;
            },
            placeholder: '请选择开始时间',
          },
          {
            label: '选择时间',
            name: 'actionTimeRange',
            type: 'object',
            visibleOn(data) {
              return data.actionTime !== 2;
            },
            rules: [
              {
                validator: (value, cb) => {
                  const { actionStartTime, actionEndTime } = value;
                  if (actionStartTime >= actionEndTime) {
                    cb('开始时间不能大于结束时间');
                  }
                },
              },
            ],
            fields: [
              {
                label: '',
                name: 'actionStartTime',
                type: 'select',
                span: 4,
                format: 'singleSelect',
                hideLabel: true,
                source: {
                  data: timeMap,
                },
                required: true,
                placeholder: '请选择开始时间',
              },
              {
                label: '',
                hideLabel: true,
                name: 'actionEndTime',
                type: 'select',
                span: 4,
                format: 'singleSelect',
                required: true,
                source: {
                  data: timeMap,
                },
                placeholder: '请选择结束时间',
              },
            ],
          },
          {
            label: '',
            name: 'autoRuleNotify',
            type: 'custom',
            component: DingConfig,
            params: {
              isCorrelation: true,
            },
            required: true,
            visibleOn: (data) => {
              if (data.ruleType === ruleTypeEnum.allMedia) {
                return notifyCodes.includes(data.autoRuleCondition.action);
              }
              return data.autoRuleTargetItem.some((item) => {
                return notifyCodes.includes(item.autoRuleCondition.action);
              });
            },
          },
        ],
      },
      {
        label: '规则名称',
        type: 'viewCard',
        fields: [
          {
            label: '规则名称',
            name: 'ruleName',
            type: 'text',
            required: true,
            maxLength: 30,
            span: 8,
          },
        ],
      },
    ],
  };
};

export default null;
