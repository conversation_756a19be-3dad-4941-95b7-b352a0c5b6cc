import { commonApi } from '@/api/common';
import { MediaType } from '@/views/promotion/hooks/authorization';

const getfieldsMap = (mediaPlatform) => {
  return [
    MediaType.OPPO,
    MediaType.UC,
    MediaType.XIAOMI,
    MediaType.QU_TOU_TIAO,
    MediaType.WEI_BO,
    MediaType.ALIPAY,
    MediaType.WIFI,
    MediaType.HONOR,
    MediaType.SIGMOB,
  ].includes(mediaPlatform)
    ? []
    : [
        {
          label: '回传状态',
          name: 'transmitStatus',
          type: 'select',
          format: 'singleSelect',
          select: {
            allowClear: true,
          },
          style: {
            width: '168px',
          },
          source: {
            data: [
              { label: '已配置', value: 1 },
              { label: '未配置', value: 0 },
            ],
          },
        },
      ];
};

function getCommonFilter(mediaPlatform) {
  return {
    formSchema: {
      fields: [
        {
          label: '所属人员',
          name: 'ownerIds',
          type: 'select',
          format: 'multipleSelect',
          select: {
            allowSearch: true,
            allowClear: true,
            maxTagCount: 1,
          },
          style: {
            width: '258px',
          },
          source: {
            labelKey: 'userName',
            valueKey: 'id',
            async data() {
              const res = await commonApi.getCurrentUserList();
              return res.data;
            },
          },
        },
        {
          label: '创建时间',
          name: 'dateRange',
          type: 'dateRange',
        },
        {
          label: '授权状态',
          name: 'isValid',
          type: 'select',
          format: 'singleSelect',
          style: {
            width: '218px',
          },
          select: {
            allowClear: true,
          },
          source: {
            data: [
              { label: '已授权', value: 1 },
              { label: '未授权', value: 0 },
            ],
          },
        },
        ...getfieldsMap(mediaPlatform),
        {
          label: '数据更新状态',
          name: 'isOpen',
          type: 'select',
          format: 'singleSelect',
          style: {
            width: '268px',
          },
          select: {
            allowClear: true,
          },
          source: {
            data: [
              { label: '开', value: 1 },
              { label: '关', value: 0 },
            ],
          },
        },
        {
          label: '',
          name: 'keyWord',
          placeholder: [MediaType.SIGMOB].includes(mediaPlatform)
            ? '支持单个账户名称/ID搜索'
            : '支持账户ID搜索，多条请用英文,隔开',
          hideLabel: true,
          useParse: true,
          style: {
            width: '218px',
          },
          allowClear: true,
          type: 'text',
        },
      ],
    },
    defaultFormData: {
      ownerIds: [],
      isValid: 1,
      isOpen: [MediaType.HONOR, MediaType.IQIYI, MediaType.SIGMOB].includes(
        mediaPlatform
      )
        ? undefined
        : 1,
    },
  };
}

export default getCommonFilter;
