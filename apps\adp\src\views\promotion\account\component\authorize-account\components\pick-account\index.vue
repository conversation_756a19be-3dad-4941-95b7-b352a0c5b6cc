<template>
  <div class="pick-account">
    <a-alert style="margin-bottom: 10px">
      注意：所选账户将自动绑定当前页面的产品（当前产品：
      {{ currentApp?.appName }}）
    </a-alert>
    <div class="table-wrapper" v-if="props.params.visible">
      <DTableSelector
        ref="selectorRef"
        :filter="filterFormSchema"
        v-model="formData.adAccountList"
        :columns="accountColumns"
        value-key="advertiserId"
        label-key="advertiserName"
        :load="{
          action: fetchData,
        }"
        :is-multiple="true"
        :select-all-fields="true"
        :auto-load="false"
        :show-count="true"
        :right-operation-style="{ minWidth: '250px' }"
        :show-filter-operation="true"
        :options="operations"
      >
        <template #filterExtra>
          <a-checkbox
            :model-value="customFilterData.ownerIdExist"
            @change="handleOwnerIdExistChange"
            >仅显示无所属人员账户
          </a-checkbox>
        </template>

        <template #tableAgentId="{ record }">
          <a-select
            v-model="record.agentId"
            :disabled="record.disabled"
            :options="agentOptions"
            :virtual-list-props="{ height: 200 }"
            allow-clear
            allow-search
            @change="
              (value) => changeField({ value, record, field: 'agentId' })
            "
          >
            <template #option="{ data }">
              <aTooltip :content="data.label" position="tl">
                <span>{{ data.label }}</span>
              </aTooltip>
            </template>
          </a-select>
        </template>
        <template #tableLinkId="{ record }">
          <div>
            <a-select
              v-model="record.linkId"
              :disabled="record.disabled"
              :options="linkOptions"
              :virtual-list-props="{ height: 200 }"
              :trigger-props="{ autoFitPopupMinWidth: true }"
              allow-clear
              allow-search
              @change="
                (value) => changeField({ value, record, field: 'linkId' })
              "
            >
            </a-select>
            <div class="error-message" v-if="record.linkShowTips"
              >链接选择重复</div
            >
          </div>
        </template>
        <template #tableRemark="{ record }">
          <a-textarea
            v-model="record.remark"
            :disabled="record.ownerId"
            show-word-limit
            :max-length="30"
            @change="(value) => changeField({ value, record, field: 'remark' })"
          >
          </a-textarea>
        </template>
      </DTableSelector>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useAppStore } from '@/store';
  import { computed, nextTick, ref, useTemplateRef, watch } from 'vue';
  import {
    mediaPlatformMap,
    MediaType,
  } from '@/views/promotion/hooks/authorization';
  import useMediaRedirectState from '@/hooks/media-redirect-state';
  import {
    accountGdtApi,
    accountKsApi,
    accountTtApi,
  } from '@/api/promotion/account';
  import { useRoute } from 'vue-router';
  import accountQttApi from '@/api/promotion/account/qutoutiao';
  import accountCommonApi from '@/api/promotion/common';
  import { map } from 'lodash';
  import { getAgentIdOperation, getRemarkOperation } from './operations';
  import usePickOptions from '../../hooks/use-pick-options';
  import { diffTableListVal, formatPickTableData } from './utils';
  import { pickAccountColumns } from './constants';
  import { getFilterFormSchema } from './filter';

  defineOptions({
    name: 'PickAccount',
  });
  const filterFormSchema = getFilterFormSchema();
  const props = defineProps<{
    params: {
      mediaType: number;
      qttIds?: string[] | number[] | undefined;
      visible: boolean;
    };
  }>();

  const appStore = useAppStore();
  const currentApp: any = computed(() => {
    return appStore.appInfo?.curApp;
  });
  const formData = defineModel<any>();
  const accountColumns = computed(() => {
    if (
      [
        MediaType.HUA_WEI,
        MediaType.VIVO,
        MediaType.QU_TOU_TIAO,
        MediaType.BAI_DU,
        MediaType.WEI_BO,
      ].includes(props.params.mediaType)
    ) {
      // 过滤主体名称
      return pickAccountColumns.filter(
        (item) => item.dataIndex !== 'corporationName'
      );
    }
    return pickAccountColumns;
  });
  const { getMediaState }: any = useMediaRedirectState();
  const route = useRoute();
  const mediaGather = computed(() => {
    const queryState: any = getMediaState();
    const developerAppId = queryState?.serverState ?? '';
    const companyCode = appStore.appInfo?.curApp?.companyCode;
    const { mediaType } = props.params;
    const mapping = {
      [MediaType.TOU_TIAO]: {
        api: accountTtApi.originData,
        params: {
          authCode: route.query?.auth_code,
          developerAppId,
          companyCode,
        },
      },
      [MediaType.KUAI_SHOU]: {
        api: accountKsApi.authorizeAccountList,
        params: {
          authCode: route.query?.auth_code,
          groupName: currentApp.value?.departmentCode ?? '',
          developerAppId,
          companyCode,
        },
      },
      [MediaType.GDT]: {
        api: accountGdtApi.originData,
        params: {
          authCode: route.query?.authorization_code,
          developerAppId,
          companyCode,
        },
      },
      [MediaType.QU_TOU_TIAO]: {
        api: accountQttApi.originData,
        params: {
          mediaAppIds: props.params.qttIds,
          authCode: route.query?.authorization_code,
        },
      },
    };
    const otherQueryMap = {
      [MediaType.WEI_BO]: route.query?.code,
      [MediaType.BAI_DU]: route.query?.authCode,
      [MediaType.HUA_WEI]: route.query?.authorization_code,
      [MediaType.VIVO]: route.query?.code,
    };
    const otherParams: any = {
      api: accountCommonApi.originData,
      params: {
        authCode: otherQueryMap[mediaType],
        mediaPlatform: mediaType,
        companyCode,
      },
    };
    if (mediaType === MediaType.BAI_DU) {
      otherParams.params.authUserId = route.query?.userId;
    }
    if ([MediaType.WEI_BO, MediaType.BAI_DU].includes(mediaType)) {
      otherParams.params.developerAppId = developerAppId;
    }

    const config = mapping[mediaType] || otherParams;

    return config;
  });
  function fetchData(filter, pagination) {
    const { api, params } = mediaGather.value;
    const query = {
      ...filter,
      ...pagination,
      ...params,
      ownerIdExist: customFilterData.value.ownerIdExist ? 1 : 0,
    };
    return api?.(query).then(async (res: any) => {
      res.data.list = formatPickTableData({
        mediaType: props.params.mediaType,
        originList: res.data?.list || [],
        linkOptions: linkOptions.value,
      });

      // 每次请求数据完成后，和已选择的数据做对比，更新历史备注
      diffTableListVal({
        list: res.data?.list,
        selectList: formData.value.adAccountList,
        cb: ({ listItem, selectItem }) => {
          listItem.agentId = selectItem.agentId;
          listItem.linkId = selectItem.linkId;
          listItem.remark = selectItem.remark;
        },
      });
      return res.data;
    });
  }

  async function refreshTable() {
    nextTick(() => {
      selectorRef.value?.refreshTable();
    });
  }
  const selectorRef = useTemplateRef<{
    refreshTable: () => void;
  }>('selectorRef');

  const { agentOptions, fetchAgentOptions, linkOptions, fetchLinkOptions } =
    usePickOptions();
  fetchAgentOptions({
    medias: props.params.mediaType,
    companyCode: window.$appInfo.companyCode,
  });
  function loadLinksOptions() {
    return fetchLinkOptions({
      pkg: window.$appInfo?.packageName,
      platform: mediaPlatformMap[props.params.mediaType],
      filterBindAccount: true,
      applicationId: window.$appInfo?.id,
    });
  }
  // loadLinksOptions();
  const customFilterData = ref<any>({
    ownerIdExist: false,
  });
  function handleOwnerIdExistChange(value) {
    customFilterData.value.ownerIdExist = value;
    refreshTable();
  }
  // 更新已选择的字段
  function changeField({ value, record, field }) {
    formData.value.adAccountList = map(formData.value.adAccountList, (item) => {
      if (item.advertiserId === record.advertiserId) {
        return { ...item, [field]: value };
      }
      return item;
    });
    // 当已选账户修改且linkId不为空时，重置已选择的模板配置和回传
    if (field === 'linkId') {
      if (value) {
        formData.value.linkTemplateId = undefined;
        formData.value.ruleConfigTemplateId = undefined;
      }
    }
  }

  async function initPickAccount() {
    await loadLinksOptions();
    // 快手需要预请求ID
    if (props.params.mediaType === MediaType.KUAI_SHOU) {
      await accountKsApi.getAuthorizeAccountId({
        ...mediaGather.value.params,
      });
    }

    refreshTable();
  }

  watch(
    () => props.params.visible,
    (open) => {
      if (open) {
        initPickAccount();
      } else {
        resetData();
      }
    },
    {
      immediate: true,
    }
  );

  const operations = [
    getAgentIdOperation({
      formData,
      action: ({ formData: actionFormVal }) => {
        formData.value.adAccountList = map(
          formData.value.adAccountList,
          (item) => {
            return {
              ...item,
              agentId: actionFormVal.agentId,
            };
          }
        );
        refreshTable();
      },
      agentOptions,
    }),
    getRemarkOperation({
      formData,
      action: ({ formData: actionFormVal }) => {
        formData.value.adAccountList = map(
          formData.value.adAccountList,
          (item) => {
            return {
              ...item,
              remark: actionFormVal.remark,
            };
          }
        );
        refreshTable();
      },
    }),
  ];
  function resetData() {
    customFilterData.value.ownerIdExist = false;
    linkOptions.value = [];
    agentOptions.value = [];
  }

  defineExpose({
    getLinkOptions: () => linkOptions.value,
  });
</script>

<style scoped lang="less">
  .pick-account {
    .error-message {
      color: red;
      font-size: 12px;
      font-weight: 400;
    }
  }
</style>
