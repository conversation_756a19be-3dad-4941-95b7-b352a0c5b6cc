<template>
  <PortalPage title="首页" class="home-wrap">
    <div class="home-wrap-content">
      <div class="home-title material-title">素材工具</div>
      <HomeHead @task-refresh="handleSearch" />
      <div class="tab-content">
        <a-tabs v-model:active-key="activeKey">
          <a-tab-pane key="taskList" title="合成任务">
            <HomeSearch
              ref="homeSearchRef"
              @search="handleSearch"
              v-if="activeKey === 'taskList'"
            />
            <HomeTable ref="homeTableRef" v-if="activeKey === 'taskList'" />
          </a-tab-pane>
          <a-tab-pane key="batchList" title="导入合成任务">
            <BatchSearch
              ref="batchSearchRef"
              @search="handleBatchSearch"
              v-if="activeKey === 'batchList'"
            />
            <BatchTable ref="batchTableRef" v-if="activeKey === 'batchList'" />
          </a-tab-pane>
          <a-tab-pane key="uploadList" title="上传任务">
            <UploadSearch
              ref="uploadSearchRef"
              @search="handleUploadSearch"
              v-if="activeKey === 'uploadList'"
            />
            <UploadTable
              ref="uploadTableRef"
              v-if="activeKey === 'uploadList'"
            />
          </a-tab-pane>
          <a-tab-pane key="pushTask" title="推送任务">
            <TaskSearch
              ref="taskSearchRef"
              @search="handlePushSearch"
              v-if="activeKey === 'pushTask'"
            />
            <TaskTable ref="taskTableRef" v-if="activeKey === 'pushTask'" />
          </a-tab-pane>
          <a-tab-pane key="downloadList" title="下载任务">
            <DownloadSearch
              ref="downloadSearchRef"
              @search="handleDownloadSearch"
              v-if="activeKey === 'downloadList'"
            />
            <DownloadTable
              ref="homeTableRef"
              v-if="activeKey === 'downloadList'"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </PortalPage>
</template>

<script setup lang="ts">
  import { provide, ref } from 'vue';
  import { homeContext } from '@/views/sc-home/utils';
  import HomeHead from './components/home-head.vue';
  import HomeSearch from './components/home-search.vue';
  import HomeTable from './components/hemo-table.vue';
  import TaskSearch from './components/task-search.vue';
  import TaskTable from './components/task-table.vue';
  import UploadSearch from './components/upload-search.vue';
  import UploadTable from './components/upload-table.vue';
  import BatchTable from './components/batch-import/batch-table.vue';
  import BatchSearch from './components/batch-import/batch-search.vue';
  import DownloadSearch from './components/download/download-search.vue';
  import DownloadTable from './components/download/download-table.vue';

  const homeTableRef = ref();
  const homeSearchRef = ref();
  const taskSearchRef = ref();
  const taskTableRef = ref();
  const uploadTableRef = ref();
  const uploadSearchRef = ref();
  const batchSearchRef = ref();
  const batchTableRef = ref();
  const downloadSearchRef = ref();
  const downloadTableRef = ref();
  const activeKey = ref('taskList');
  provide(homeContext, {
    getSearch,
    getPushSearch,
    getUploadSearch,
    handleUploadSearch,
    getBatchSearch,
    handleBatchSearch,
    getDownloadSearch,
  });
  provide('activeInfo', {
    activeKey,
  });

  function getSearch() {
    return homeSearchRef.value?.getValue?.() ?? {};
  }

  function getPushSearch() {
    return taskSearchRef.value?.getValue?.() ?? {};
  }
  function getUploadSearch() {
    return uploadSearchRef.value?.getValue?.() ?? {};
  }
  function getBatchSearch() {
    return batchSearchRef.value?.getValue?.() ?? {};
  }
  function getDownloadSearch() {
    return downloadSearchRef.value?.getValue?.() ?? {};
  }

  function handleSearch() {
    homeTableRef.value?.resetPage?.();
    loadData();
  }
  function handleDownloadSearch() {
    downloadTableRef.value?.resetPage?.();
    loadData();
  }
  function handleBatchSearch() {
    batchTableRef.value?.resetPage?.();
    loadBatchData();
  }
  function handlePushSearch() {
    taskTableRef.value?.resetPage?.();
    loadPushData();
  }
  function handleUploadSearch() {
    uploadTableRef.value?.resetPage?.();
    loadUploadData();
  }
  function loadUploadData() {
    uploadTableRef.value?.loadData?.();
  }
  function loadPushData() {
    taskTableRef.value?.loadData?.();
  }
  function loadData() {
    homeTableRef.value?.loadData?.();
  }
  function loadBatchData() {
    batchTableRef.value?.loadData?.();
  }
</script>

<style scoped>
  .home-wrap {
    background: #fff;
    border-radius: 2px;
    height: auto;
    min-height: 100%;

    .home-wrap-content {
      min-width: 1280px;
      max-width: 90%;
      margin: 0 auto;
      height: 100%;
      display: flex;
      flex-direction: column;

      .tab-content {
        margin-top: 20px;
      }

      .home-title {
        font-weight: 600;
        font-size: 16px;
        color: rgb(0 0 0 / 68%);
        line-height: 22px;
      }

      .material-title {
        padding: 23px 0 19px;
      }

      .material-task {
        padding: 40px 0 24px;
      }
    }

    ::v-deep(.arco-btn-text) {
      padding: 0 8px;
    }
  }
</style>
