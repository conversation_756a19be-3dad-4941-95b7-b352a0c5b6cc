<template>
  <div class="upload-wrapper" @dragenter="handleDragenter" @drop="handleDrop">
    <a-upload
      draggable
      multiple
      :directory="isDirectory"
      ref="uploadRef"
      :auto-upload="true"
      v-model:file-list="fileList"
      :show-file-list="false"
      @before-upload="beforeUpload"
      :custom-request="customRequest"
      @exceed-limit="exceedLimit"
      @change="onUpdatedChange"
      :accept="accept"
      :limit="limit || 200"
      :show-upload-button="{
        showOnExceedLimit: true,
      }"
    >
      <template #upload-button>
        <div class="upload-button-warpper">
          <icon-upload class="icon" size="30" />
          <p>将文件夹拖到此处,或点击上传文件</p>
          <p class="text"
            >1.上传的总文件个数不超过{{ limit }}个, 单个文件不超过500M</p
          >
          <p class="text"
            >2.一个mp3文件,必须对应一个srt文件,两者的命名必须一致</p
          >
          <p class="text"
            >2.支持上传片头,片尾;格式为mp4;同一本书的片头名必须和mp3,srt保持一致;</p
          >
          <p class="text"
            >如果片头或片尾可通用,只用'通用'命名，最后下划线标明片头还是片尾(例如'通用_片头.mp4')</p
          >
        </div>
        <div
          v-if="fileList.length"
          class="progress-bar"
          @click="(e) => e.stopPropagation()"
        >
          <div
            class="progress-bar-inner"
            :style="{
              width: percent + '%',
              backgroundColor: percent === 100 ? '#4caf50' : '#f7793e',
            }"
          ></div>
          <div class="progress-text"
            >上传进度 成功:<span>{{ uploadedCount }}</span
            >失败<span :style="{ color: 'red' }">{{ failCount }}</span> /
            {{ fileList.length }}</div
          >
        </div>
        <div
          v-if="failFileList.length"
          @click="(e) => e.stopPropagation()"
          class="fail-list-wrapper"
        >
          <span class="fail-title" v-if="!show" @click="() => (show = true)"
            >上传失败查看</span
          >
          <div class="fail-list" v-else>
            <div
              v-for="item in failFileList"
              :key="item.uid"
              style="width: 100%; display: flex"
            >
              <a-tooltip :content="item.name">
                <div class="fail-name">{{ item.name }}</div>
              </a-tooltip>
              <div class="fail-text">{{ item.errorMessage }}</div>
            </div>
          </div>
        </div>
      </template>
    </a-upload>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, nextTick } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { getUploadAction } from './utils';
  import useUploadLoading from '../hooks/use-upload-loading';

  const { setUploadLoading } = useUploadLoading();
  const isDirectory = ref(false);
  const folderName = ref('');
  const acceptName = [
    '男古虐',
    '男古爽',
    '男现虐',
    '男现爽',
    '女古虐',
    '女古爽',
    '女现虐',
    '女现爽',
    '男年代文',
    '女年代文',
    '女甜文',
    '男甜文',
    '男悬疑',
    '女悬疑',
  ];
  const props = defineProps<{
    params: any;
  }>();
  const show = ref(false);
  const limit = computed(() => {
    return props.params.limit;
  });

  const uploadRef = ref();
  const modalValue = defineModel<any[]>({ default: [] });
  const accept = '.mp3,.srt,.mp4';
  const maxAudioSize = 500 * 1024 * 1024;
  const maxSubtitleSize = 500 * 1024 * 1024;
  const maxVideoSize = 500 * 1024 * 1024;
  const fileList = ref<any[]>([]);
  const abortMap = new Map();
  const uploadQueue: any[][] = [];
  const doneCount = computed(() => {
    return fileList.value.filter(
      (item) => item.status === 'done' || item.status === 'error'
    ).length;
  });
  const uploadedCount = computed(() => {
    return fileList.value.filter((item) => item.status === 'done').length;
  });
  const failCount = computed(() => {
    return fileList.value.filter((item) => item.status === 'error').length;
  });
  const failFileList = computed(() => {
    return [...fileList.value.filter((item) => item.status === 'error')];
  });
  const percent = computed(() => {
    return Number((doneCount.value / fileList.value.length).toFixed(2)) * 100;
  });
  function handleDragenter(evt) {
    isDirectory.value = true;
  }
  function handleDrop(event) {
    const { items } = event.dataTransfer;
    folderName.value = items[0].webkitGetAsEntry().name || '';
    isDirectory.value = false;
  }
  const customRequest = (option) => {
    // 将abort方法挂载在value上，避免引用丢失
    const abort: Record<string, any> = { value: null };
    upload(option, abort);
    abortMap.set(option.fileItem.uid, abort);
    return {
      abort() {},
    };
  };
  async function upload(option, abort) {
    const { fileItem } = option;
    fileItem.folderName = folderName.value;
    modalValue.value.push(fileItem);
    const info = modalValue.value[modalValue.value.length - 1];
    info.status = 'uploading';
    if (!checkName(info.name)) {
      info.status = 'error';
      info.errorMessage = '文件名格式错误';
      return;
    }
    uploadQueue.push([option, info, abort]);
    processQueue();
  }

  const concurrency = 3; // 可以根据需要调整并发数
  let activeUploads = 0;

  async function processQueue() {
    while (uploadQueue.length && activeUploads < concurrency) {
      const task = uploadQueue.shift() as any;
      // eslint-disable-next-line no-plusplus
      activeUploads++;
      try {
        // eslint-disable-next-line no-await-in-loop
        await uploadTask(...task);
      } catch (e) {
        console.error(e);
      }
      // eslint-disable-next-line no-plusplus
      activeUploads--;
      processQueue();
    }
  }
  const addTimestampToFilename = (filename) => {
    const specialName = ['通用_片头.mp4', '通用_片尾.mp4'];
    if (specialName.includes(filename)) {
      const timestamp = new Date().getTime(); // 获取当前时间戳
      const fileParts = filename.split('.'); // 分割文件名和扩展名
      return `${fileParts[0]}_${timestamp}.${fileParts[1]}`; // 添加时间戳并组合
    }
    return filename;
  };
  async function uploadTask(option, info, abort) {
    const { onProgress, onError, onSuccess, fileItem } = option;
    try {
      const { query, uploadAction } = getUploadAction({
        file: fileItem?.file,
        filename: addTimestampToFilename(fileItem.name),
      });
      const { url }: any = await uploadAction(query);
      fileItem.url = url;
      fileItem.status = 'done';
      onSuccess();
    } catch (err) {
      info.status = 'error';
      info.errorMessage = '上传失败';
      onError(err);
    }
  }
  async function beforeUpload(file) {
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (
      !isAudioType(fileExtension) &&
      !isSubtitleType(fileExtension) &&
      !isVideoType(fileExtension)
    ) {
      Message.warning(`${file.name}格式不支持`);
      return false;
    }

    if (isAudioType(fileExtension) && maxAudioSize < file.size) {
      Message.warning(`${file.name}超出音频大小限制`);
      return false;
    }

    if (isSubtitleType(fileExtension) && maxSubtitleSize < file.size) {
      Message.warning(`${file.name}超出字幕大小限制`);
      return false;
    }
    if (isVideoType(fileExtension) && maxVideoSize < file.size) {
      Message.warning(`${file.name}超出片头/片尾大小限制`);
      return false;
    }
    return true;
  }

  function isAudioType(type) {
    return type === 'mp3';
  }

  function isSubtitleType(type) {
    return type === 'srt';
  }
  function isVideoType(type) {
    return type === 'mp4';
  }

  function exceedLimit(_fileList) {
    Message.warning('超出文件数量限制');
  }
  function onUpdatedChange(files, fileItem) {
    const loading = files.some((item) => item.status === 'uploading');
    setUploadLoading(loading);
  }
  function checkName(name) {
    const regex = /_(.*?)_/;
    const match = name.match(regex);
    const type = match ? match[1] : null;
    return (
      acceptName.includes(type) ||
      ['通用_片头.mp4', '通用_片尾.mp4'].includes(name.toLowerCase())
    );
  }
  function checkPair(name) {
    console.log(fileList.value);
    const baseName = name.substring(0, name.lastIndexOf('.'));
    const target = modalValue.value.findIndex((item) => {
      return item.name.includes(baseName) && item.name !== name;
    });
    return target !== -1;
  }
  watch(
    () => modalValue.value,
    async (val) => {
      fileList.value = val.map((item) => {
        return {
          ...item,
        };
      });

      if (
        modalValue.value.length > 0 &&
        modalValue.value.every(
          (item) => item.status === 'done' || item.status === 'error'
        )
      ) {
        await nextTick();
        setUploadLoading(false);
      }
    },

    {
      deep: true,
      immediate: true,
    }
  );
  // watch(isUploading, (val) => {
  //   setUploadLoading(val);
  // });
</script>

<style scoped lang="less">
  .upload-button-warpper {
    width: 100%;
    background-color: var(--color-neutral-2);
    padding: 32px 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .icon {
      color: #939393;
    }

    .text {
      margin: 2px 0;
      color: #999;
      font-size: 14px;
      text-align: center;
    }
  }

  .upload-wrapper {
    :deep(.arco-upload-icon-upload) {
      display: none;
    }
  }

  .size {
    text-align: right;
    margin: 0;
  }

  .progress-bar {
    width: 100%;
    height: 16px;
    background-color: #f0f0f0;
    border-radius: 5px;
    overflow: hidden;
    margin-top: 10px;
    position: relative;
  }

  .progress-bar-inner {
    height: 100%;
    background-color: #4caf50; /* 可以根据需要调整颜色 */
    width: 0;
    transition: width 0.3s ease;
    display: flex;
  }

  .progress-text {
    position: absolute; /* 可以根据需要调整文本颜色 */
    font-size: 12px;
    white-space: nowrap;
    width: 100%;
    text-align: center;
    top: 0;
  }

  .fail-list-wrapper {
    width: 100%;
    margin-top: 12px;
    position: relative;
  }

  .fail-title {
    position: absolute;
    right: 0;
    color: red;
  }

  .fail-list {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .fail-name {
    color: #999;
    width: 300px;
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏超出容器的内容 */
    text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
  }

  .fail-text {
    text-align: right;
    width: 150px;
    color: red;
  }
</style>
