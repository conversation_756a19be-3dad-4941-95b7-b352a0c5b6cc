import { FormSchema } from '@repo/seal/src/components/dynamic/types/form';
import useAppId from '@/hooks/app-id';
import downloadEffectApi from '@/api/advanced/download-effect';
import downloadEffectAuth from '@/constants/auth-key/advanced/download-effect';

const appId = useAppId();

const ui = {
  width: '800px',
};

function getFormSchema(): FormSchema {
  return {
    fields: [
      {
        label: '广告应用包名',
        name: 'pkg',
        type: 'text',
        maxLength: 50,
        required: true,
        showWordLimit: true,
      },
      {
        label: '广告应用名称',
        name: 'name',
        type: 'text',
        maxLength: 20,
        required: true,
        showWordLimit: true,
      },
      {
        label: '目标次留率',
        name: 'targetRate',
        type: 'number',
        precision: 2,
        suffix: '%',
        required: true,
      },
      {
        label: '安装后激活时间',
        name: 'activateValidHour',
        type: 'number',
        precision: 0,
        required: true,
      },
      {
        label: 'Deeplink地址',
        name: 'deeplink',
        type: 'text',
      },
      {
        label: '关键词',
        name: 'keyword',
        type: 'textarea',
        placeholder: '用英文逗号隔开',
        required: true,
      },
    ],
  };
}

function getFormData() {
  return {
    pkg: '',
    name: '',
    deeplink: '',
    keyword: '',
    activateValidHour: 8,
  };
}

const getOperation = (brand: string) => {
  return {
    create: {
      auth: downloadEffectAuth.ADVANCED_DOWNLOAD_EFFECT_ADD,
      action: (data, customParams) => {
        Object.assign(data, customParams);
        return downloadEffectApi.effect.create({
          appId: appId.value,
          brand,
          ...data,
        });
      },
      form: {
        title: '创建补曝光任务',
        formSchema: getFormSchema(),
        defaultFormData: getFormData(),
      },
      ui,
    },
    modify: {
      auth: downloadEffectAuth.ADVANCED_DOWNLOAD_EFFECT_EDIT,
      action: (data) => {
        return downloadEffectApi.effect.modify({
          ...data,
          brand,
          appId: appId.value,
        });
      },
      form: {
        title: '编辑补曝光任务',
        formSchema: getFormSchema(),
      },
      ui,
      fetchDetail(record) {
        return downloadEffectApi.effect.detail({
          id: record.id,
          brand,
          appId: appId.value,
        });
      },
    },
    remove: {
      auth: downloadEffectAuth.ADVANCED_DOWNLOAD_EFFECT_DELETE,
      action: (data) => {
        return downloadEffectApi.effect.remove({
          ids: [data.id],
          appId: appId.value,
          brand,
        });
      },
    },
    batchUpload: {
      auth: downloadEffectAuth.ADVANCED_DOWNLOAD_EFFECT_BATCH_ADD,
      templateName: '优化效果设置.xlsx',
      action: (formData) => {
        formData.append('brand', brand);
        return downloadEffectApi.effect.batchUpload?.(formData);
      },
    },
    batchRemove: {
      auth: downloadEffectAuth.ADVANCED_DOWNLOAD_EFFECT_BATCH_DELETE,
      action: (data) => {
        return downloadEffectApi.effect.remove?.({
          ids: data,
          appId: appId.value,
          brand,
        });
      },
    },
  };
};

export default getOperation;
