import { get } from 'lodash';
import { ref } from 'vue';
import { autoMaterial } from '@/api/automation/auto-material';

const productOptions = ref<any[]>([]);
const accountOptions = ref<any[]>([]);
const channelOptions = ref<any[]>([]);
export const useAccountOptions = () => {
  function formatProductOptions(tree: any[]) {
    const res: any[] = [];
    tree.forEach((item) => {
      const level_1_node: any = {
        label: item.companyName,
        value: item.companyCode,
        children: [],
      };
      if (item.list?.length > 0) {
        item.list.forEach((level_2: any) => {
          const level_2_node: any = {
            label: level_2.departmentName,
            value: level_2.departmentCode,
            children: [],
          };
          if (level_2.list?.length > 0) {
            level_2.list.forEach((level_3: any) => {
              level_2_node.children.push({
                label: level_3.appName,
                value: level_3.id,
              });
            });
          }
          level_1_node.children.push(level_2_node);
        });
      }
      res.push(level_1_node);
    });
    return res;
  }

  const getProductOptions = async () => {
    const res = await autoMaterial.productList({});
    const productTree = get(res, 'data', []);

    productOptions.value = formatProductOptions(productTree);
  };
  const getAccountOptions = async (productId: any, mediaCode: any) => {
    if (!productId || !mediaCode) {
      accountOptions.value = [];
      return;
    }
    const res = await autoMaterial.accountList({
      mediaPlatform: mediaCode,
      applicationIds: productId,
    });
    accountOptions.value = get(res, 'data', []).map((item: any) => {
      return {
        label: item.advertiserName,
        value: item.advertiserId,
      };
    });
  };
  const getChannelOptions = async (accountId: any, mediaCode: any) => {
    if (!accountId) {
      channelOptions.value = [];
      return;
    }
    const res = await autoMaterial.channelList({
      appIdList: accountId,
      mediaPlatform: mediaCode,
    });
    channelOptions.value = get(res, 'data', []).map((item: any) => {
      return {
        id: item.id,
        label: item.channelName,
        value: item.channelCode,
      };
    });
  };
  return {
    accountOptions,
    productOptions,
    channelOptions,
    getProductOptions,
    getAccountOptions,
    getChannelOptions,
  };
};

export default useAccountOptions;
